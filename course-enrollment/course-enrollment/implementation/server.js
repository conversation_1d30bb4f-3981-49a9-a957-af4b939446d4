const express = require('express');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());

// Constants
const CONSTANTS = {
  MAX_CREDITS_PER_TERM: 18,
  MAX_COURSES_PER_PROF: 5,
  MAX_DROP_COUNT_PER_TERM: 3,
  DROP_PENALTY_FEE: 5000, // cents
  COST_PER_CREDIT: 10000, // cents ($100.00)
  TERM_STATES: ['PLANNING', 'ENROLLMENT_OPEN', 'ENROLLMENT_CLOSED', 'CONCLUDED'],
  COURSE_STATES: ['DRAFT', 'OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
  ENROLLMENT_STATES: ['ENROLLED', 'WAITLISTED', 'DROPPED', 'COMPLETED'],
  USER_ROLES: ['STUDENT', 'PROFESSOR', 'REGISTRAR'],
  DELIVERY_MODES: ['IN_PERSON', 'ONLINE', 'HYBRID']
};

// In-memory data storage
const data = {
  terms: new Map(),
  courses: new Map(),
  enrollments: new Map(),
  courseSeatLedgers: new Map(),
  studentTuitionLedgers: new Map(),
  users: new Map() // For role validation
};

// Utility functions
function generateRequestId() {
  return 'req_' + Math.random().toString(36).substr(2, 20).toUpperCase();
}

function validateUUID(id) {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidRegex.test(id);
}

function validateCourseCode(code) {
  const codeRegex = /^[A-Z]{2,4}\d{3}$/;
  return codeRegex.test(code);
}

function isValidURL(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

// Standard response wrapper
function createResponse(data, type = 'object') {
  return {
    meta: {
      api_request_id: generateRequestId(),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: type,
    data: data
  };
}

function createErrorResponse(errorId, message) {
  return {
    meta: {
      api_request_id: generateRequestId(),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };
}

// Authentication middleware
function authenticate(req, res, next) {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  if (!userId || !userRole) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'Missing required authentication headers X-User-ID and X-User-Role'
    ));
  }

  if (!validateUUID(userId)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ID_FORMAT',
      'X-User-ID must be a valid UUID'
    ));
  }

  if (!CONSTANTS.USER_ROLES.includes(userRole)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ENUM_VALUE',
      'X-User-Role must be one of: STUDENT, PROFESSOR, REGISTRAR'
    ));
  }

  // Store user context for downstream use
  req.user = { id: userId, role: userRole };
  
  // Ensure user exists in our system
  if (!data.users.has(userId)) {
    data.users.set(userId, { id: userId, role: userRole });
  }

  next();
}

// Validation middleware
function validateTermExists(req, res, next) {
  const termId = req.params.termId;
  
  if (!validateUUID(termId)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ID_FORMAT',
      'Term ID must be a valid UUID'
    ));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      'ERR_TERM_NOT_FOUND',
      'Academic term not found'
    ));
  }

  req.term = term;
  next();
}

function validateCourseExists(req, res, next) {
  const courseId = req.params.courseId;
  
  if (!validateUUID(courseId)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ID_FORMAT',
      'Course ID must be a valid UUID'
    ));
  }

  const course = data.courses.get(courseId);
  if (!course || course.term_id !== req.params.termId) {
    return res.status(404).json(createErrorResponse(
      'ERR_COURSE_NOT_FOUND',
      'Course not found in this term'
    ));
  }

  req.course = course;
  next();
}

// Business logic helpers
function countStudentDropsInTerm(studentId, termId) {
  let dropCount = 0;
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.student_id === studentId &&
        enrollment.term_id === termId &&
        enrollment.state === 'DROPPED' &&
        enrollment.dropped_by_role === 'STUDENT') {
      dropCount++;
    }
  }
  return dropCount;
}

function countStudentEnrolledCredits(studentId, termId) {
  let totalCredits = 0;
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.student_id === studentId && 
        enrollment.term_id === termId && 
        enrollment.state === 'ENROLLED') {
      const course = data.courses.get(enrollment.course_id);
      if (course) {
        totalCredits += course.credits;
      }
    }
  }
  return totalCredits;
}

function countProfessorCourses(professorId, termId) {
  let courseCount = 0;
  for (const course of data.courses.values()) {
    if (course.professor_id === professorId && 
        course.term_id === termId &&
        course.state !== 'CANCELLED') {
      courseCount++;
    }
  }
  return courseCount;
}

function getStudentTuitionLedger(studentId, termId) {
  const key = `${studentId}_${termId}`;
  if (!data.studentTuitionLedgers.has(key)) {
    data.studentTuitionLedgers.set(key, {
      student_id: studentId,
      term_id: termId,
      balance_cents: 0
    });
  }
  return data.studentTuitionLedgers.get(key);
}

function getCourseSeatLedger(courseId) {
  if (!data.courseSeatLedgers.has(courseId)) {
    const course = data.courses.get(courseId);
    if (course && course.state === 'OPEN') {
      data.courseSeatLedgers.set(courseId, {
        course_id: courseId,
        seats_available: course.capacity
      });
    }
  }
  return data.courseSeatLedgers.get(courseId);
}

function promoteFromWaitlist(courseId) {
  // Find first waitlisted student for this course
  const waitlistedEnrollments = [];
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.course_id === courseId && enrollment.state === 'WAITLISTED') {
      waitlistedEnrollments.push(enrollment);
    }
  }
  
  if (waitlistedEnrollments.length === 0) return;
  
  // Sort by creation time (FIFO)
  waitlistedEnrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  
  const seatLedger = getCourseSeatLedger(courseId);
  const course = data.courses.get(courseId);
  
  if (seatLedger && seatLedger.seats_available > 0) {
    // Try to promote students in order until we find one that doesn't exceed credit limit
    for (const enrollment of waitlistedEnrollments) {
      // Check credit limit for this student
      const currentCredits = countStudentEnrolledCredits(enrollment.student_id, enrollment.term_id);
      
      if (currentCredits + course.credits <= CONSTANTS.MAX_CREDITS_PER_TERM) {
        // This student can be promoted without exceeding credit limit
        enrollment.state = 'ENROLLED';
        enrollment.revision++;
        
        // Update ledgers
        seatLedger.seats_available--;
        const tuitionLedger = getStudentTuitionLedger(enrollment.student_id, enrollment.term_id);
        tuitionLedger.balance_cents += course.credits * CONSTANTS.COST_PER_CREDIT;
        
        console.log(`Promoted student ${enrollment.student_id} from waitlist to enrolled in course ${courseId}`);
        break; // Stop after successfully promoting one student
      } else {
        console.log(`Skipped promoting student ${enrollment.student_id} due to credit limit (${currentCredits} + ${course.credits} > ${CONSTANTS.MAX_CREDITS_PER_TERM})`);
      }
    }
  }
}

// TERM MANAGEMENT ENDPOINTS

// POST /terms - Create a new academic term
app.post('/terms', authenticate, (req, res) => {
  if (req.user.role !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Only Registrars can create terms'
    ));
  }

  const { name } = req.body;
  
  if (!name || typeof name !== 'string' || name.trim() === '') {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_REQUIRED_FIELD',
      'Term name is required and cannot be empty'
    ));
  }

  // Check for unknown fields
  const allowedFields = ['name'];
  const extraFields = Object.keys(req.body).filter(key => !allowedFields.includes(key));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }

  // Check name uniqueness
  for (const term of data.terms.values()) {
    if (term.name === name.trim()) {
      return res.status(409).json(createErrorResponse(
        'ERR_TERM_NAME_NOT_UNIQUE',
        'A term with this name already exists'
      ));
    }
  }

  const termId = uuidv4();
  const term = {
    id: termId,
    name: name.trim(),
    state: 'PLANNING',
    created_by: req.user.id,
    created_at: new Date().toISOString(),
    revision: 0
  };

  data.terms.set(termId, term);
  
  res.status(201).json(createResponse(term));
});

// GET /terms/{termId} - Retrieve details of an academic term
app.get('/terms/:termId', authenticate, validateTermExists, (req, res) => {
  res.json(createResponse(req.term));
});

// PATCH /terms/{termId}:open-registration - Open student registration
app.patch('/terms/:termId\\:open-registration', authenticate, validateTermExists, (req, res) => {
  if (req.user.role !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Only Registrars can open registration'
    ));
  }

  const term = req.term;
  const allowedFields = ['revision'];
  const extraFields = Object.keys(req.body || {}).filter(k => !allowedFields.includes(k));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }
  const { revision } = req.body;
  if (revision === undefined) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_REQUIRED_FIELD',
      'Revision is required'
    ));
  }

  // Check revision for optimistic locking (only for stale revisions per PRD Section 2.5)
  if (revision !== undefined && revision < term.revision) {
    return res.status(409).json(createErrorResponse(
      'ERR_REV_CONFLICT',
      'Term has been modified by another user. Please refresh and try again.'
    ));
  }

  // Check state (container-level condition per PRD Section 2.6)
  if (term.state !== 'PLANNING') {
    return res.status(409).json(createErrorResponse(
      'ERR_TERM_NOT_ACTIVE',
      'Term must be in PLANNING state to open registration'
    ));
  }

  // Check exact revision match for optimistic locking
  if (revision !== undefined && term.revision !== revision) {
    return res.status(409).json(createErrorResponse(
      'ERR_REV_CONFLICT',
      'Term has been modified by another user. Please refresh and try again.'
    ));
  }

  term.state = 'ENROLLMENT_OPEN';
  term.revision++;

  res.json(createResponse(term));
});

// PATCH /terms/{termId}:close-registration - Close enrollment period
app.patch('/terms/:termId\\:close-registration', authenticate, validateTermExists, (req, res) => {
  if (req.user.role !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Only Registrars can close registration'
    ));
  }

  const term = req.term;
  const allowedFields = ['revision'];
  const extraFields = Object.keys(req.body || {}).filter(k => !allowedFields.includes(k));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }
  const { revision } = req.body;
  if (revision === undefined) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_REQUIRED_FIELD',
      'Revision is required'
    ));
  }

  // Check revision for optimistic locking (only for stale revisions per PRD Section 2.5)
  if (revision !== undefined && revision < term.revision) {
    return res.status(409).json(createErrorResponse(
      'ERR_REV_CONFLICT',
      'Term has been modified by another user. Please refresh and try again.'
    ));
  }

  // Check state (container-level condition per PRD Section 2.6)
  if (term.state !== 'ENROLLMENT_OPEN') {
    return res.status(409).json(createErrorResponse(
      'ERR_TERM_NOT_ACTIVE',
      'Term must be in ENROLLMENT_OPEN state to close registration'
    ));
  }

  // Check exact revision match for optimistic locking
  if (revision !== undefined && term.revision !== revision) {
    return res.status(409).json(createErrorResponse(
      'ERR_REV_CONFLICT',
      'Term has been modified by another user. Please refresh and try again.'
    ));
  }

  term.state = 'ENROLLMENT_CLOSED';
  term.revision++;

  // Transition all OPEN courses to IN_PROGRESS
  for (const course of data.courses.values()) {
    if (course.term_id === term.id && course.state === 'OPEN') {
      course.state = 'IN_PROGRESS';
      course.revision++;
    }
  }

  res.json(createResponse(term));
});

// PATCH /terms/{termId}:conclude - Conclude the term
app.patch('/terms/:termId\\:conclude', authenticate, validateTermExists, (req, res) => {
  if (req.user.role !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Only Registrars can conclude terms'
    ));
  }

  const term = req.term;
  const allowedFields = ['revision'];
  const extraFields = Object.keys(req.body || {}).filter(k => !allowedFields.includes(k));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }
  const { revision } = req.body;
  if (revision === undefined) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_REQUIRED_FIELD',
      'Revision is required'
    ));
  }

  // Check revision for optimistic locking (only for stale revisions per PRD Section 2.5)
  if (revision !== undefined && revision < term.revision) {
    return res.status(409).json(createErrorResponse(
      'ERR_REV_CONFLICT',
      'Term has been modified by another user. Please refresh and try again.'
    ));
  }

  // Check state (container-level condition per PRD Section 2.6)
  if (term.state !== 'ENROLLMENT_CLOSED') {
    return res.status(409).json(createErrorResponse(
      'ERR_TERM_NOT_ACTIVE',
      'Term must be in ENROLLMENT_CLOSED state to conclude'
    ));
  }

  // Check exact revision match for optimistic locking
  if (revision !== undefined && term.revision !== revision) {
    return res.status(409).json(createErrorResponse(
      'ERR_REV_CONFLICT',
      'Term has been modified by another user. Please refresh and try again.'
    ));
  }

  term.state = 'CONCLUDED';
  term.revision++;

  // Finalize all courses and enrollments
  for (const course of data.courses.values()) {
    if (course.term_id === term.id && course.state === 'IN_PROGRESS') {
      course.state = 'COMPLETED';
      course.revision++;
    }
  }

  for (const enrollment of data.enrollments.values()) {
    if (enrollment.term_id === term.id) {
      if (enrollment.state === 'ENROLLED') {
        enrollment.state = 'COMPLETED';
      } else if (enrollment.state === 'WAITLISTED') {
        enrollment.state = 'DROPPED';
        enrollment.dropped_by_role = 'SYSTEM'; // Automatic drop when term concludes
      }
      enrollment.revision++;
    }
  }

  res.json(createResponse(term));
});

// COURSE MANAGEMENT ENDPOINTS

// POST /terms/{termId}/courses - Create a new course
app.post('/terms/:termId/courses', authenticate, validateTermExists, (req, res) => {
  if (!['PROFESSOR', 'REGISTRAR'].includes(req.user.role)) {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Only Professors and Registrars can create courses'
    ));
  }

  const {
    code, title, description, credits, capacity, professor_id,
    delivery_mode, location, online_link
  } = req.body;

  // Validate required fields
  if (!code || !title || credits === undefined || capacity === undefined || !delivery_mode) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_REQUIRED_FIELD',
      'Required fields: code, title, credits, capacity, delivery_mode'
    ));
  }

  // Check for unknown fields
  const allowedFields = ['code', 'title', 'description', 'credits', 'capacity', 'professor_id', 'delivery_mode', 'location', 'online_link'];
  const extraFields = Object.keys(req.body).filter(key => !allowedFields.includes(key));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }

  // Validate course code format
  if (!validateCourseCode(code)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_COURSE_CODE',
      'Course code must be 2-4 uppercase letters followed by 3 digits (e.g., CS101)'
    ));
  }

  // Check code uniqueness within term
  for (const course of data.courses.values()) {
    if (course.term_id === req.params.termId && course.code === code) {
      return res.status(409).json(createErrorResponse(
        'ERR_COURSE_CODE_NOT_UNIQUE',
        'Course code already exists in this term'
      ));
    }
  }

  // Validate title length
  if (title.trim().length === 0 || title.length > 100) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_FIELD_LENGTH',
      'Title must be 1-100 characters'
    ));
  }

  // Validate description length
  if (description && description.length > 1000) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_FIELD_LENGTH',
      'Description cannot exceed 1000 characters'
    ));
  }

  // Validate credits
  if (!Number.isInteger(credits) || credits < 1 || credits > 5) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_CREDITS',
      'Credits must be an integer between 1 and 5'
    ));
  }

  // Validate capacity
  if (!Number.isInteger(capacity) || capacity < 1 || capacity > 500) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_CAPACITY',
      'Capacity must be an integer between 1 and 500'
    ));
  }

  // Validate delivery mode
  if (!CONSTANTS.DELIVERY_MODES.includes(delivery_mode)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ENUM_VALUE',
      'Delivery mode must be IN_PERSON, ONLINE, or HYBRID'
    ));
  }

  // Validate delivery mode requirements
  if (delivery_mode === 'IN_PERSON') {
    if (!location || location.trim() === '') {
      return res.status(400).json(createErrorResponse(
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        'Location is required for IN_PERSON courses'
      ));
    }
    if (online_link) {
      return res.status(400).json(createErrorResponse(
        'ERR_FIELD_CONFLICT',
        'Online link cannot be provided for IN_PERSON courses'
      ));
    }
  } else if (delivery_mode === 'ONLINE') {
    if (!online_link || !isValidURL(online_link)) {
      return res.status(400).json(createErrorResponse(
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        'Valid online link is required for ONLINE courses'
      ));
    }
    if (location) {
      return res.status(400).json(createErrorResponse(
        'ERR_FIELD_CONFLICT',
        'Location cannot be provided for ONLINE courses'
      ));
    }
  } else if (delivery_mode === 'HYBRID') {
    if (!location && !online_link) {
      return res.status(400).json(createErrorResponse(
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        'At least one of location or online_link is required for HYBRID courses'
      ));
    }
    if (online_link && !isValidURL(online_link)) {
      return res.status(400).json(createErrorResponse(
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        'Online link must be a valid URL if provided'
      ));
    }
  }

  // Determine professor
  let assignedProfessorId;
  if (req.user.role === 'PROFESSOR') {
    if (professor_id && professor_id !== req.user.id) {
      return res.status(400).json(createErrorResponse(
        'ERR_FIELD_CONFLICT',
        'Professors can only create courses for themselves'
      ));
    }
    assignedProfessorId = req.user.id;
  } else { // REGISTRAR
    if (!professor_id) {
      return res.status(400).json(createErrorResponse(
        'ERR_MISSING_REQUIRED_FIELD',
        'Registrars must specify professor_id when creating courses'
      ));
    }
    
    if (!validateUUID(professor_id)) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_ID_FORMAT',
        'Professor ID must be a valid UUID'
      ));
    }
    
    const professor = data.users.get(professor_id);
    if (!professor || professor.role !== 'PROFESSOR') {
      return res.status(422).json(createErrorResponse(
        'ERR_INVALID_INSTRUCTOR',
        'Professor ID does not correspond to a valid professor'
      ));
    }
    
    assignedProfessorId = professor_id;
  }

  // Check professor course limit
  if (countProfessorCourses(assignedProfessorId, req.params.termId) >= CONSTANTS.MAX_COURSES_PER_PROF) {
    return res.status(409).json(createErrorResponse(
      'ERR_MAX_COURSES_REACHED',
      'Professor has reached the maximum course limit for this term'
    ));
  }

  const courseId = uuidv4();
  const timestamp = new Date().toISOString();
  const course = {
    id: courseId,
    term_id: req.params.termId,
    code: code,
    title: title.trim(),
    description: description || '',
    credits: credits,
    capacity: capacity,
    professor_id: assignedProfessorId,
    delivery_mode: delivery_mode,
    state: 'DRAFT',
    created_at: timestamp,
    updated_at: timestamp,
    revision: 0,
    enrolled_count: 0,
    waitlist_count: 0,
    // In DRAFT state seats are not yet available for enrollment, so expose 0
    available_seats: 0
  };

  if (location) {
    course.location = location;
  }
  if (online_link) {
    course.online_link = online_link;
  }

  data.courses.set(courseId, course);
  
  res.status(201).json(createResponse(course));
});

// GET /terms/{termId}/courses - List courses in the term
app.get('/terms/:termId/courses', authenticate, validateTermExists, (req, res) => {
  const { limit = 50, offset = 0, state, professor_id } = req.query;
  
  const courses = [];
  for (const course of data.courses.values()) {
    if (course.term_id !== req.params.termId) continue;
    
    // Role-based filtering
    if (req.user.role === 'STUDENT') {
      // Students only see published courses or courses they're involved in
      if (!['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)) {
        // Check if student is enrolled in this course
        let isEnrolled = false;
        for (const enrollment of data.enrollments.values()) {
          if (enrollment.course_id === course.id && 
              enrollment.student_id === req.user.id &&
              enrollment.state !== 'DROPPED') {
            isEnrolled = true;
            break;
          }
        }
        if (!isEnrolled) continue;
      }
    } else if (req.user.role === 'PROFESSOR') {
      // Professors see their own courses (all states) and others' published courses
      if (course.professor_id !== req.user.id && 
          !['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)) {
        continue;
      }
    }
    // REGISTRAR sees all courses
    
    // Apply filters
    if (state && course.state !== state) continue;
    if (professor_id && course.professor_id !== professor_id) continue;
    
    courses.push(course);
  }
  
  // Sort by creation time
  courses.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  
  // Apply pagination
  const startIndex = parseInt(offset);
  const endIndex = startIndex + parseInt(limit);
  const paginatedCourses = courses.slice(startIndex, endIndex);
  
  // Add derived fields based on role
  const enrichedCourses = paginatedCourses.map(course => {
    const enriched = { ...course };
    
    // Count enrollments
    let enrolledCount = 0;
    let waitlistCount = 0;
    for (const enrollment of data.enrollments.values()) {
      if (enrollment.course_id === course.id) {
        if (enrollment.state === 'ENROLLED') enrolledCount++;
        if (enrollment.state === 'WAITLISTED') waitlistCount++;
      }
    }
    
    enriched.enrolled_count = enrolledCount;
    enriched.waitlist_count = waitlistCount;
    enriched.available_seats = course.capacity - enrolledCount;
    
    // Role-based field filtering
    if (req.user.role === 'STUDENT') {
      // Students NEVER see counts per PRD - even if enrolled
      let studentEnrollment = null;
      for (const enrollment of data.enrollments.values()) {
        if (enrollment.course_id === course.id && enrollment.student_id === req.user.id) {
          studentEnrollment = enrollment;
          break;
        }
      }
      
      // Always remove count fields for students
      delete enriched.enrolled_count;
      delete enriched.waitlist_count;
      
      if (studentEnrollment) {
        enriched.is_enrolled = studentEnrollment.state === 'ENROLLED';
        enriched.is_waitlisted = studentEnrollment.state === 'WAITLISTED';
      }
    }
    
    return enriched;
  });
  
  res.json(createResponse(enrichedCourses, 'array'));
});

// GET /terms/{termId}/courses/{courseId} - Get detailed course info
app.get('/terms/:termId/courses/:courseId', authenticate, validateTermExists, validateCourseExists, (req, res) => {
  const course = req.course;
  
  // Role-based visibility
  if (req.user.role === 'STUDENT') {
    if (!['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)) {
      // Check if student is enrolled
      let isEnrolled = false;
      for (const enrollment of data.enrollments.values()) {
        if (enrollment.course_id === course.id && 
            enrollment.student_id === req.user.id &&
            enrollment.state !== 'DROPPED') {
          isEnrolled = true;
          break;
        }
      }
      if (!isEnrolled) {
        return res.status(404).json(createErrorResponse(
          'ERR_COURSE_NOT_FOUND',
          'Course not found'
        ));
      }
    }
  } else if (req.user.role === 'PROFESSOR') {
    if (course.professor_id !== req.user.id && 
        !['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)) {
      return res.status(404).json(createErrorResponse(
        'ERR_COURSE_NOT_FOUND',
        'Course not found'
      ));
    }
  }
  
  const enriched = { ...course };
  
  // Add enrollment statistics
  let enrolledCount = 0;
  let waitlistCount = 0;
  const enrollments = [];
  
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.course_id === course.id) {
      if (enrollment.state === 'ENROLLED') enrolledCount++;
      if (enrollment.state === 'WAITLISTED') waitlistCount++;
      enrollments.push(enrollment);
    }
  }
  
  enriched.enrolled_count = enrolledCount;
  enriched.waitlist_count = waitlistCount;
  enriched.available_seats = course.capacity - enrolledCount;
  
  // Role-specific data
  if (req.user.role === 'STUDENT') {
    // Find student's enrollment status
    const studentEnrollment = enrollments.find(e => e.student_id === req.user.id);
    if (studentEnrollment) {
      enriched.is_enrolled = studentEnrollment.state === 'ENROLLED';
      enriched.is_waitlisted = studentEnrollment.state === 'WAITLISTED';
    }
    
    // Students NEVER see detailed counts per PRD
    delete enriched.enrolled_count;
    delete enriched.waitlist_count;
  } else if (req.user.role === 'PROFESSOR' && course.professor_id === req.user.id) {
    // Professors see full roster for their courses
    enriched.enrollments = enrollments;
  } else if (req.user.role === 'REGISTRAR') {
    // Registrars see everything
    enriched.enrollments = enrollments;
  }
  
  res.json(createResponse(enriched));
});

// PATCH /terms/{termId}/courses/{courseId}:publish - Publish a course
app.patch('/terms/:termId/courses/:courseId\\:publish', authenticate, validateTermExists, validateCourseExists, (req, res) => {
  const course = req.course;
  const term = req.term;

  // Validate body fields
  const allowedFields = ['revision'];
  const extraFields = Object.keys(req.body || {}).filter(k => !allowedFields.includes(k));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }

  const { revision } = req.body;
  if (revision === undefined) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_REQUIRED_FIELD',
      'Revision field is required for publish operations'
    ));
  }

  if (revision !== course.revision) {
    return res.status(409).json(createErrorResponse(
      'ERR_REV_CONFLICT',
      'Course has been modified by another user'
    ));
  }
  
  // Authorization check
  if (req.user.role === 'PROFESSOR' && course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      'ERR_NOT_INSTRUCTOR',
      'Professors can only publish their own courses'
    ));
  } else if (!['PROFESSOR', 'REGISTRAR'].includes(req.user.role)) {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Only Professors and Registrars can publish courses'
    ));
  }
  
  // State validation
  if (course.state !== 'DRAFT') {
    return res.status(409).json(createErrorResponse(
      'ERR_COURSE_WRONG_STATE',
      'Course must be in DRAFT state to publish'
    ));
  }
  
  if (term.state !== 'ENROLLMENT_OPEN') {
    return res.status(409).json(createErrorResponse(
      'ERR_TERM_NOT_ACTIVE',
      'Term must be open for enrollment to publish courses'
    ));
  }
  
  // Update course state
  course.state = 'OPEN';
  course.revision++;
  course.updated_at = new Date().toISOString();
  
  // Initialize seat ledger
  data.courseSeatLedgers.set(course.id, {
    course_id: course.id,
    seats_available: course.capacity
  });
  let enrolledCount = 0;
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.course_id === course.id && enrollment.state === 'ENROLLED') {
      enrolledCount++;
    }
  }

  course.enrolled_count = enrolledCount;
  course.waitlist_count = 0;
  course.available_seats = course.capacity - enrolledCount;

  res.json(createResponse(course));
});

// PATCH /terms/{termId}/courses/{courseId}:cancel - Cancel a course
app.patch('/terms/:termId/courses/:courseId\\:cancel', authenticate, validateTermExists, validateCourseExists, (req, res) => {
  const course = req.course;

  const allowedFields = ['revision'];
  const extraFields = Object.keys(req.body || {}).filter(k => !allowedFields.includes(k));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }

  const { revision } = req.body;
  if (revision === undefined) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_REQUIRED_FIELD',
      'Revision field is required for cancel operations'
    ));
  }

  if (revision !== course.revision) {
    return res.status(409).json(createErrorResponse(
      'ERR_REV_CONFLICT',
      'Course has been modified by another user'
    ));
  }
  
  // Authorization check
  if (req.user.role === 'PROFESSOR' && course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      'ERR_NOT_INSTRUCTOR',
      'Professors can only cancel their own courses'
    ));
  } else if (!['PROFESSOR', 'REGISTRAR'].includes(req.user.role)) {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Only Professors and Registrars can cancel courses'
    ));
  }
  
  // State validation
  if (!['DRAFT', 'OPEN', 'IN_PROGRESS'].includes(course.state)) {
    return res.status(409).json(createErrorResponse(
      'ERR_COURSE_WRONG_STATE',
      'Cannot cancel a course that is already completed or cancelled'
    ));
  }
  
  // Cancel course
  course.state = 'CANCELLED';
  course.revision++;
  course.updated_at = new Date().toISOString();
  
  // Drop all enrollments and handle refunds
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.course_id === course.id &&
        ['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {

      const wasEnrolled = enrollment.state === 'ENROLLED';
      enrollment.state = 'DROPPED';
      enrollment.dropped_by_role = 'SYSTEM'; // Automatic drop when course is cancelled
      enrollment.revision++;
      
      if (wasEnrolled) {
        // Free seat and refund tuition
        const seatLedger = getCourseSeatLedger(req.course.id);
        if (seatLedger) {
          seatLedger.seats_available++;
        }
        
        const tuitionLedger = getStudentTuitionLedger(enrollment.student_id, enrollment.term_id);
        // Refund tuition but ensure balance never goes negative (PRD requirement)
        const refundAmount = req.course.credits * CONSTANTS.COST_PER_CREDIT;
        tuitionLedger.balance_cents = Math.max(0, tuitionLedger.balance_cents - refundAmount);
        
        promoteFromWaitlist(req.course.id);
      }
    }
  }
  
  // Clear seat ledger
  data.courseSeatLedgers.delete(course.id);
  let enrolledCount = 0;
  let waitlistCount = 0;
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.course_id === course.id) {
      if (enrollment.state === 'ENROLLED') enrolledCount++;
      if (enrollment.state === 'WAITLISTED') waitlistCount++;
    }
  }

  course.enrolled_count = enrolledCount;
  course.waitlist_count = waitlistCount;
  course.available_seats = course.capacity - enrolledCount;

  res.json(createResponse(course));
});

// ENROLLMENT ENDPOINTS

// POST /terms/{termId}/courses/{courseId}/enrollments - Enroll in course
app.post('/terms/:termId/courses/:courseId/enrollments', authenticate, validateTermExists, validateCourseExists, (req, res) => {
  const course = req.course;
  const term = req.term;
  
  // Determine target student
  let targetStudentId;
  if (req.user.role === 'STUDENT') {
    targetStudentId = req.user.id;
    // Students cannot specify a different student_id - this is an impersonation attempt
    if (req.body.student_id && req.body.student_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        'ERR_FORBIDDEN',
        'Students can only enroll themselves'
      ));
    }
  } else if (req.user.role === 'REGISTRAR') {
    if (!req.body.student_id) {
      return res.status(400).json(createErrorResponse(
        'ERR_MISSING_REQUIRED_FIELD',
        'Registrars must specify student_id'
      ));
    }
    
    if (!validateUUID(req.body.student_id)) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_ID_FORMAT',
        'Student ID must be a valid UUID'
      ));
    }
    
    // For Registrar enrollments, check if student exists or is a known test user
    let student = data.users.get(req.body.student_id);
    if (!student) {
      // Known test student IDs that can be auto-registered
      const knownTestStudents = [
        '*************-4444-4444-************', // STUDENT_A
        '*************-5555-5555-************', // STUDENT_B
        '*************-6666-6666-************', // STUDENT_C
        '*************-7777-7777-************'  // STUDENT_D
      ];

      if (knownTestStudents.includes(req.body.student_id)) {
        // Auto-register known test student
        student = { id: req.body.student_id, role: 'STUDENT' };
        data.users.set(req.body.student_id, student);
      } else {
        return res.status(404).json(createErrorResponse(
          'ERR_STUDENT_NOT_FOUND',
          'Student not found'
        ));
      }
    } else if (student.role !== 'STUDENT') {
      return res.status(404).json(createErrorResponse(
        'ERR_STUDENT_NOT_FOUND',
        'Student not found'
      ));
    }
    
    targetStudentId = req.body.student_id;
  } else {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Only Students and Registrars can create enrollments'
    ));
  }
  
  // Check for unknown fields
  const allowedFields = ['student_id'];
  const extraFields = Object.keys(req.body).filter(key => !allowedFields.includes(key));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }
  
  // Validate term state
  if (term.state === 'PLANNING') {
    return res.status(409).json(createErrorResponse(
      'ERR_TERM_NOT_ACTIVE',
      'Term is not active for enrollment'
    ));
  } else if (term.state === 'ENROLLMENT_CLOSED') {
    // Registrar can override closed enrollment
    if (req.user.role !== 'REGISTRAR') {
      return res.status(409).json(createErrorResponse(
        'ERR_REGISTRATION_CLOSED',
        'Enrollment is not currently open for this term'
      ));
    }
  } else if (term.state === 'CONCLUDED') {
    return res.status(409).json(createErrorResponse(
      'ERR_TERM_NOT_ACTIVE',
      'Term is not active for enrollment'
    ));
  } else if (term.state !== 'ENROLLMENT_OPEN') {
    return res.status(409).json(createErrorResponse(
      'ERR_TERM_NOT_ACTIVE',
      'Term is not active for enrollment'
    ));
  }
  
  // Validate course state
  if (course.state === 'OPEN') {
    // Course is open for enrollment
  } else if (course.state === 'IN_PROGRESS' && req.user.role === 'REGISTRAR') {
    // Registrar can override and enroll students in IN_PROGRESS courses
  } else {
    return res.status(409).json(createErrorResponse(
      'ERR_COURSE_WRONG_STATE',
      'Course is not currently open for enrollment'
    ));
  }
  
  // Check for duplicate enrollment
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.course_id === course.id && 
        enrollment.student_id === targetStudentId &&
        ['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {
      return res.status(409).json(createErrorResponse(
        'ERR_ALREADY_ENROLLED',
        'Student is already enrolled or waitlisted for this course'
      ));
    }
  }
  
  // Determine enrollment state based on seat availability
  const seatLedger = getCourseSeatLedger(course.id);
  let enrollmentState;
  
  if (seatLedger && seatLedger.seats_available > 0) {
    enrollmentState = 'ENROLLED';
  } else {
    enrollmentState = 'WAITLISTED';
  }
  
  // Enforce credit limit ONLY if the student would actually receive a seat (ENROLLED).
  if (req.user.role === 'STUDENT' && enrollmentState === 'ENROLLED') {
    const currentCredits = countStudentEnrolledCredits(targetStudentId, term.id);
    if (currentCredits + course.credits > CONSTANTS.MAX_CREDITS_PER_TERM) {
      return res.status(409).json(createErrorResponse(
        'ERR_CREDIT_LIMIT_EXCEEDED',
        'Enrollment would exceed maximum credit limit'
      ));
    }
  }
  
  // Create enrollment
  const enrollmentId = uuidv4();
  const enrollment = {
    id: enrollmentId,
    course_id: course.id,
    student_id: targetStudentId,
    term_id: term.id,
    state: enrollmentState,
    created_at: new Date().toISOString(),
    revision: 0
  };
  
  data.enrollments.set(enrollmentId, enrollment);
  
  // Update ledgers if enrolled
  if (enrollmentState === 'ENROLLED') {
    seatLedger.seats_available--;
    const tuitionLedger = getStudentTuitionLedger(targetStudentId, term.id);
    tuitionLedger.balance_cents += course.credits * CONSTANTS.COST_PER_CREDIT;
  }
  
  res.status(201).json(createResponse(enrollment));
});

// GET /terms/{termId}/courses/{courseId}/enrollments - List enrollments (roster)
app.get('/terms/:termId/courses/:courseId/enrollments', authenticate, validateTermExists, validateCourseExists, (req, res) => {
  const course = req.course;
  
  // Authorization check
  if (req.user.role === 'STUDENT') {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Students cannot view course rosters'
    ));
  } else if (req.user.role === 'PROFESSOR' && course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      'ERR_NOT_INSTRUCTOR',
      'Professors can only view rosters for their own courses'
    ));
  }
  
  const { limit = 50, offset = 0 } = req.query;
  
  const enrollments = [];
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.course_id === course.id) {
      enrollments.push(enrollment);
    }
  }
  
  // Sort by creation time
  enrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  
  // Apply pagination
  const startIndex = parseInt(offset);
  const endIndex = startIndex + parseInt(limit);
  const paginatedEnrollments = enrollments.slice(startIndex, endIndex);
  
  res.json(createResponse(paginatedEnrollments, 'array'));
});

// GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId} - Get specific enrollment
app.get('/terms/:termId/courses/:courseId/enrollments/:enrollmentId', authenticate, validateTermExists, validateCourseExists, (req, res) => {
  const { enrollmentId } = req.params;
  
  if (!validateUUID(enrollmentId)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ID_FORMAT',
      'Enrollment ID must be a valid UUID'
    ));
  }
  
  const enrollment = data.enrollments.get(enrollmentId);
  if (!enrollment || enrollment.course_id !== req.course.id) {
    return res.status(404).json(createErrorResponse(
      'ERR_ENROLLMENT_NOT_FOUND',
      'Enrollment not found in this course'
    ));
  }
  
  // Authorization check
  if (req.user.role === 'STUDENT' && enrollment.student_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      'ERR_FORBIDDEN',
      'Students can only view their own enrollments'
    ));
  } else if (req.user.role === 'PROFESSOR' && req.course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      'ERR_NOT_INSTRUCTOR',
      'Professors can only view enrollments in their own courses'
    ));
  }
  
  res.json(createResponse(enrollment));
});

// PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop - Drop enrollment
app.patch('/terms/:termId/courses/:courseId/enrollments/:enrollmentId\\:drop', authenticate, validateTermExists, (req, res) => {
  const { enrollmentId } = req.params;

  if (!validateUUID(enrollmentId)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ID_FORMAT',
      'Enrollment ID must be a valid UUID'
    ));
  }

  // Check if enrollment exists first
  const enrollment = data.enrollments.get(enrollmentId);

  // If term is concluded and enrollment doesn't exist, prioritize term state error
  if (req.term.state === 'CONCLUDED' && !enrollment) {
    return res.status(409).json(createErrorResponse(
      'ERR_TERM_NOT_ACTIVE',
      'Cannot drop enrollments after term is concluded'
    ));
  }

  // Now validate course exists
  const courseId = req.params.courseId;
  if (!validateUUID(courseId)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ID_FORMAT',
      'Course ID must be a valid UUID'
    ));
  }

  const course = data.courses.get(courseId);
  if (!course || course.term_id !== req.term.id) {
    return res.status(404).json(createErrorResponse(
      'ERR_COURSE_NOT_FOUND',
      'Course not found in this term'
    ));
  }
  req.course = course;

  // We already checked if enrollment exists above, now validate it belongs to this course
  if (!enrollment || enrollment.course_id !== req.course.id) {
    return res.status(404).json(createErrorResponse(
      'ERR_ENROLLMENT_NOT_FOUND',
      'Enrollment not found in this course'
    ));
  }
  
  // Check term state restrictions
  if (req.term.state === 'ENROLLMENT_CLOSED' && req.user.role !== 'REGISTRAR') {
    return res.status(409).json(createErrorResponse(
      'ERR_REGISTRATION_CLOSED',
      'Registration is closed for this term'
    ));
  }
  if (req.term.state === 'CONCLUDED') {
    return res.status(409).json(createErrorResponse(
      'ERR_TERM_NOT_ACTIVE',
      'Term is not active for enrollment'
    ));
  }

  // Authorization check
  if (req.user.role === 'STUDENT' && enrollment.student_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      'ERR_FORBIDDEN',
      'Students can only drop their own enrollments'
    ));
  } else if (req.user.role === 'PROFESSOR' && req.course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      'ERR_NOT_INSTRUCTOR',
      'Professors can only drop enrollments from their own courses'
    ));
  } else if (!['STUDENT', 'PROFESSOR', 'REGISTRAR'].includes(req.user.role)) {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Invalid role for dropping enrollments'
    ));
  }
  
  // Check revision for optimistic locking
  const allowedFields = ['revision'];
  const extraFields = Object.keys(req.body || {}).filter(k => !allowedFields.includes(k));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }
  const { revision } = req.body;
  if (revision === undefined) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_REQUIRED_FIELD',
      'Revision field is required for drop operations'
    ));
  }
  if (revision !== enrollment.revision) {
    return res.status(409).json(createErrorResponse(
      'ERR_REV_CONFLICT',
      'Enrollment has been modified by another user'
    ));
  }

  // Enrollment state validation
  if (!['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {
    return res.status(409).json(createErrorResponse(
      'ERR_ENROLLMENT_WRONG_STATE',
      'Cannot drop an enrollment that is already dropped or completed'
    ));
  }
  
  // Check drop limit for students
  if (req.user.role === 'STUDENT') {
    const currentDropCount = countStudentDropsInTerm(enrollment.student_id, req.term.id);
    if (currentDropCount >= CONSTANTS.MAX_DROP_COUNT_PER_TERM) {
      return res.status(409).json(createErrorResponse(
        'ERR_TOO_MANY_DROPS',
        'Student has reached maximum drop limit for this term'
      ));
    }
  }
  
  const wasEnrolled = enrollment.state === 'ENROLLED';

  // Update enrollment state
  enrollment.state = 'DROPPED';
  enrollment.dropped_by_role = req.user.role;
  enrollment.revision++;
  
  // Handle ledger updates
  if (wasEnrolled) {
    // Free seat and refund tuition
    const seatLedger = getCourseSeatLedger(req.course.id);
    if (seatLedger) {
      seatLedger.seats_available++;
    }
    
    const tuitionLedger = getStudentTuitionLedger(enrollment.student_id, enrollment.term_id);
    // Refund tuition but ensure balance never goes negative (PRD requirement)
    const refundAmount = req.course.credits * CONSTANTS.COST_PER_CREDIT;
    tuitionLedger.balance_cents = Math.max(0, tuitionLedger.balance_cents - refundAmount);
    
    promoteFromWaitlist(req.course.id);
  }
  
  // Apply penalty for third drop (student-initiated only)
  if (req.user.role === 'STUDENT') {
    const newDropCount = countStudentDropsInTerm(enrollment.student_id, req.term.id);
    if (newDropCount === 3) {
      const tuitionLedger = getStudentTuitionLedger(enrollment.student_id, enrollment.term_id);
      tuitionLedger.balance_cents += CONSTANTS.DROP_PENALTY_FEE;
    }
  }
  
  res.json(createResponse(enrollment));
});

// PAYMENT ENDPOINTS

// POST /terms/{termId}/students/{studentId}:pay - Record payment
app.post('/terms/:termId/students/:studentId\\:pay', authenticate, validateTermExists, (req, res) => {
  const { studentId } = req.params;
  
  if (!validateUUID(studentId)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ID_FORMAT',
      'Student ID must be a valid UUID'
    ));
  }
  
  // Authorization check
  if (req.user.role === 'STUDENT' && studentId !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      'ERR_FORBIDDEN',
      'Students can only make payments for themselves'
    ));
  } else if (req.user.role === 'PROFESSOR') {
    return res.status(403).json(createErrorResponse(
      'ERR_UNAUTHORIZED_ROLE',
      'Professors cannot process payments'
    ));
  }
  
  // Validate student exists
  const student = data.users.get(studentId);
  if (!student || student.role !== 'STUDENT') {
    return res.status(404).json(createErrorResponse(
      'ERR_STUDENT_NOT_FOUND',
      'Student not found'
    ));
  }
  
  const { amount } = req.body;
  
  // Validate amount
  if (!Number.isInteger(amount) || amount <= 0) {
    return res.status(422).json(createErrorResponse(
      'ERR_INVALID_PAYMENT_AMOUNT',
      'Payment amount must be a positive integer (cents)'
    ));
  }
  
  // Check for unknown fields
  const allowedFields = ['amount'];
  const extraFields = Object.keys(req.body).filter(key => !allowedFields.includes(key));
  if (extraFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    ));
  }
  
  // Get tuition ledger
  const tuitionLedger = getStudentTuitionLedger(studentId, req.term.id);
  
  // Check for overpayment
  if (amount > tuitionLedger.balance_cents) {
    return res.status(422).json(createErrorResponse(
      'ERR_OVERPAY_NOT_ALLOWED',
      'Payment amount exceeds outstanding balance'
    ));
  }
  
  // Apply payment
  tuitionLedger.balance_cents -= amount;
  
  const response = {
    student_id: studentId,
    term_id: req.term.id,
    payment_amount: amount,
    new_balance: tuitionLedger.balance_cents
  };
  
  res.json(createResponse(response));
});

// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json(createErrorResponse(
    'ERR_INTERNAL_SERVER_ERROR',
    'An internal server error occurred'
  ));
});

// 404 handler
app.use((req, res) => {
  res.status(404).json(createErrorResponse(
    'ERR_ENDPOINT_NOT_FOUND',
    'Endpoint not found'
  ));
});

app.listen(PORT, () => {
  console.log(`University Enrollment API server running on port ${PORT}`);
  console.log('Available endpoints:');
  console.log('  POST /terms');
  console.log('  GET /terms/{termId}');
  console.log('  PATCH /terms/{termId}:open-registration');
  console.log('  PATCH /terms/{termId}:close-registration');
  console.log('  PATCH /terms/{termId}:conclude');
  console.log('  POST /terms/{termId}/courses');
  console.log('  GET /terms/{termId}/courses');
  console.log('  GET /terms/{termId}/courses/{courseId}');
  console.log('  PATCH /terms/{termId}/courses/{courseId}:publish');
  console.log('  PATCH /terms/{termId}/courses/{courseId}:cancel');
  console.log('  POST /terms/{termId}/courses/{courseId}/enrollments');
  console.log('  GET /terms/{termId}/courses/{courseId}/enrollments');
  console.log('  GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}');
  console.log('  PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop');
  console.log('  POST /terms/{termId}/students/{studentId}:pay');
});

module.exports = app;