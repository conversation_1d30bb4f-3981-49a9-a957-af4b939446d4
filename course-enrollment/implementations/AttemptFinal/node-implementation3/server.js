const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

const termsRouter = require('./routes/terms');
const coursesRouter = require('./routes/courses');
const enrollmentsRouter = require('./routes/enrollments');
const paymentsRouter = require('./routes/payments');
const { createErrorResponse } = require('./utils/responses');

const app = express();
const PORT = process.env.PORT || 3000;

app.use(helmet());
app.use(cors());
app.use(express.json());

app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

app.get('/', (req, res) => {
  res.json({ message: 'University Course Registration & Enrollment API' });
});

app.use('/terms', termsRouter);
app.use('/terms/:termId/courses', coursesRouter);
app.use('/terms/:termId/courses/:courseId/enrollments', enrollmentsRouter);
app.use('/terms/:termId/students', paymentsRouter);

app.use((req, res) => {
  res.status(404).json(createErrorResponse(
    req,
    'ERR_ENDPOINT_NOT_FOUND',
    'Endpoint not found'
  ));
});

app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json(createErrorResponse(
    req,
    'ERR_INTERNAL_SERVER_ERROR',
    'Internal server error'
  ));
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;