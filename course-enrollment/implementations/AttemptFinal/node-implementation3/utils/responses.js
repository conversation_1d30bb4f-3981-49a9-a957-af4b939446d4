const storage = require('../models/storage');

function createMeta(req) {
  return {
    api_request_id: storage.generateRequestId(),
    api_request_timestamp: new Date().toISOString()
  };
}

function createSuccessResponse(req, data, responseType = 'object') {
  return {
    meta: createMeta(req),
    response_type: responseType,
    data: data || {}
  };
}

function createErrorResponse(req, errorId, message) {
  return {
    meta: createMeta(req),
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };
}

function createListResponse(req, items, total = null) {
  const response = {
    meta: createMeta(req),
    response_type: 'array',
    data: items || []
  };

  if (total !== null) {
    response.meta.total = total;
  }

  return response;
}

function applyPagination(items, limit = 50, offset = 0) {
  const parsedLimit = Math.max(1, Math.min(500, parseInt(limit) || 50));
  const parsedOffset = Math.max(0, parseInt(offset) || 0);
  
  const paginatedItems = items.slice(parsedOffset, parsedOffset + parsedLimit);
  
  return {
    items: paginatedItems,
    total: items.length,
    limit: parsedLimit,
    offset: parsedOffset
  };
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  createListResponse,
  applyPagination
};