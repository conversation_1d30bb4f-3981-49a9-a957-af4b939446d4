const { ROLES } = require('../models/constants');
const storage = require('../models/storage');
const { createErrorResponse } = require('../utils/responses');

const UUID_PATTERN = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

function validateUUID(id) {
  return UUID_PATTERN.test(id);
}

function authMiddleware(req, res, next) {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  if (!userId || !userRole) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'Missing or invalid user context headers. Both X-User-ID and X-User-Role are required.'
    ));
  }

  if (!validateUUID(userId)) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'Invalid X-User-ID format. Must be a valid UUID.'
    ));
  }

  if (!Object.values(ROLES).includes(userRole)) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_ENUM_VALUE',
      `Invalid X-User-Role. Must be one of: ${Object.values(ROLES).join(', ')}`
    ));
  }

  req.user = {
    id: userId,
    role: userRole
  };

  next();
}

function requireRole(...allowedRoles) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(createErrorResponse(
        req,
        'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
        'Authentication required'
      ));
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_UNAUTHORIZED_ROLE',
        `Access denied. Required role: ${allowedRoles.join(' or ')}`
      ));
    }

    next();
  };
}

function validateUUIDParam(paramName) {
  return (req, res, next) => {
    const id = req.params[paramName];
    if (!validateUUID(id)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        `Invalid ${paramName} format. Must be a valid UUID.`
      ));
    }
    next();
  };
}

module.exports = {
  authMiddleware,
  requireRole,
  validateUUID,
  validateUUIDParam
};