const ROLES = {
  STUDENT: 'STUDENT',
  PROFESSOR: 'PROFESSOR',
  REGISTRAR: 'REGISTRAR'
};

const TERM_STATES = {
  PLANNING: 'PLANNING',
  ENROLLMENT_OPEN: '<PERSON><PERSON><PERSON><PERSON>ENT_OPEN',
  E<PERSON><PERSON>LMENT_CLOSED: 'ENROLLMENT_CLOSED',
  CONCLUDED: 'CONCLUDED'
};

const COURSE_STATES = {
  DRAFT: 'DRAFT',
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

const ENROLLMENT_STATES = {
  ENROLLED: 'ENROLLED',
  WAITLISTED: 'WAITLISTED',
  DROPPED: 'DROPPED',
  COMPLETED: 'COMPLETED'
};

const DELIVERY_MODES = {
  IN_PERSON: 'IN_PERSON',
  ONLINE: 'ONLINE',
  HYBRID: 'HYBRID'
};

const BUSINESS_RULES = {
  MAX_CREDITS_PER_TERM: 18,
  MAX_COURSES_PER_PROF: 5,
  MAX_DROP_COUNT_PER_TERM: 3,
  COST_PER_CREDIT: 10000, // in cents ($100.00)
  DROP_PENALTY_FEE: 5000 // in cents ($50.00)
};

module.exports = {
  ROLES,
  TERM_STATES,
  COURSE_STATES,
  ENROLLMENT_STATES,
  DELIVERY_MODES,
  BUSINESS_RULES
};