const { v4: uuidv4 } = require('uuid');

class InMemoryStorage {
  constructor() {
    this.terms = new Map();
    this.courses = new Map();
    this.enrollments = new Map();
    this.courseSeatLedgers = new Map();
    this.studentTuitionLedgers = new Map();
    this.users = new Map(); // For validation purposes
    this.auditLog = [];
  }

  reset() {
    this.terms.clear();
    this.courses.clear();
    this.enrollments.clear();
    this.courseSeatLedgers.clear();
    this.studentTuitionLedgers.clear();
    this.users.clear();
    this.auditLog = [];
  }

  generateId() {
    return uuidv4();
  }

  generateRequestId() {
    return 'req_' + Math.random().toString(36).substring(2, 18).toUpperCase();
  }

  log(event, data) {
    this.auditLog.push({
      id: this.generateId(),
      event,
      data,
      timestamp: new Date().toISOString()
    });
  }

  createTerm(termData) {
    const id = this.generateId();
    const term = {
      id,
      ...termData,
      created_at: new Date().toISOString(),
      revision: 0
    };
    this.terms.set(id, term);
    return term;
  }

  getTerm(id) {
    return this.terms.get(id);
  }

  updateTerm(id, updates) {
    const term = this.terms.get(id);
    if (!term) return null;
    
    const updatedTerm = { ...term, ...updates, revision: term.revision + 1 };
    this.terms.set(id, updatedTerm);
    return updatedTerm;
  }

  createCourse(termId, courseData) {
    const id = this.generateId();
    const course = {
      id,
      term_id: termId,
      ...courseData,
      created_at: new Date().toISOString(),
      revision: 0
    };
    this.courses.set(id, course);
    return course;
  }

  getCourse(id) {
    return this.courses.get(id);
  }

  getCoursesByTerm(termId) {
    return Array.from(this.courses.values()).filter(course => course.term_id === termId);
  }

  updateCourse(id, updates) {
    const course = this.courses.get(id);
    if (!course) return null;
    
    const updatedCourse = { ...course, ...updates, revision: course.revision + 1 };
    this.courses.set(id, updatedCourse);
    return updatedCourse;
  }

  createEnrollment(enrollmentData) {
    const id = this.generateId();
    const enrollment = {
      id,
      ...enrollmentData,
      created_at: new Date().toISOString(),
      revision: 0
    };
    this.enrollments.set(id, enrollment);
    return enrollment;
  }

  getEnrollment(id) {
    return this.enrollments.get(id);
  }

  getEnrollmentsByCourse(courseId) {
    return Array.from(this.enrollments.values()).filter(enrollment => enrollment.course_id === courseId);
  }

  getEnrollmentsByStudent(studentId, termId) {
    return Array.from(this.enrollments.values()).filter(enrollment => {
      const course = this.getCourse(enrollment.course_id);
      return enrollment.student_id === studentId && course && course.term_id === termId;
    });
  }

  updateEnrollment(id, updates) {
    const enrollment = this.enrollments.get(id);
    if (!enrollment) return null;
    
    const updatedEnrollment = { ...enrollment, ...updates, revision: enrollment.revision + 1 };
    this.enrollments.set(id, updatedEnrollment);
    return updatedEnrollment;
  }

  initializeSeatLedger(courseId, capacity) {
    this.courseSeatLedgers.set(courseId, {
      course_id: courseId,
      seats_available: capacity,
      capacity: capacity
    });
  }

  getSeatLedger(courseId) {
    return this.courseSeatLedgers.get(courseId);
  }

  updateSeatLedger(courseId, seatsChange) {
    const ledger = this.courseSeatLedgers.get(courseId);
    if (!ledger) return null;
    
    const newSeatsAvailable = ledger.seats_available + seatsChange;
    if (newSeatsAvailable < 0 || newSeatsAvailable > ledger.capacity) {
      throw new Error('Invalid seat ledger operation');
    }
    
    ledger.seats_available = newSeatsAvailable;
    this.courseSeatLedgers.set(courseId, ledger);
    return ledger;
  }

  initializeTuitionLedger(studentId, termId) {
    const key = `${studentId}-${termId}`;
    if (!this.studentTuitionLedgers.has(key)) {
      this.studentTuitionLedgers.set(key, {
        student_id: studentId,
        term_id: termId,
        balance_cents: 0
      });
    }
    return this.studentTuitionLedgers.get(key);
  }

  getTuitionLedger(studentId, termId) {
    const key = `${studentId}-${termId}`;
    return this.studentTuitionLedgers.get(key);
  }

  updateTuitionLedger(studentId, termId, balanceChange) {
    const key = `${studentId}-${termId}`;
    let ledger = this.studentTuitionLedgers.get(key);
    
    if (!ledger) {
      ledger = this.initializeTuitionLedger(studentId, termId);
    }
    
    const newBalance = ledger.balance_cents + balanceChange;
    if (newBalance < 0) {
      throw new Error('Invalid tuition ledger operation - balance cannot be negative');
    }
    
    ledger.balance_cents = newBalance;
    this.studentTuitionLedgers.set(key, ledger);
    return ledger;
  }

  createUser(userData) {
    const id = this.generateId();
    const user = { id, ...userData };
    this.users.set(id, user);
    return user;
  }

  getUser(id) {
    return this.users.get(id);
  }
}

const storage = new InMemoryStorage();

module.exports = storage;