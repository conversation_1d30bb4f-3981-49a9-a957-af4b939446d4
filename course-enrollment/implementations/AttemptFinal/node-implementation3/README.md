# University Course Registration & Enrollment API

A complete Node.js implementation of the University Course Registration & Enrollment API as specified in the Product Requirements Document (PRD).

## Overview

This API manages academic course offerings and student enrollments within academic terms, implementing role-based access control for Students, Professors, and Registrars.

## Features Implemented

### Core Functionality
- ✅ **Term Management**: Create, open, close, and conclude academic terms
- ✅ **Course Management**: Create, publish, and cancel courses with delivery modes
- ✅ **Enrollment System**: Student registration with waitlist support
- ✅ **Payment Processing**: Tuition payment handling with ledger management
- ✅ **Waitlist Promotion**: Automatic promotion when seats become available

### Business Rules
- ✅ **Credit Limits**: 18 credit maximum per student per term
- ✅ **Professor Limits**: 5 courses maximum per professor per term
- ✅ **Drop Limits**: 3 drops maximum per student with $50 penalty on 3rd drop
- ✅ **Seat Management**: Automatic seat allocation and ledger tracking
- ✅ **Term State Gating**: Enrollment only allowed during open periods

### Technical Features
- ✅ **Role-Based Access Control**: STUDENT, PROFESSOR, REGISTRAR authorization
- ✅ **Field-Level Security**: Data filtering based on user role and ownership
- ✅ **Standard Response Formats**: Unified success/error envelope structure
- ✅ **Comprehensive Validation**: Input validation with specific error codes
- ✅ **Pagination Support**: List endpoints with limit/offset parameters
- ✅ **Optimistic Locking**: Revision-based concurrency control
- ✅ **Audit Logging**: Complete event tracking for all actions

## API Endpoints

### Term Management
- `POST /terms` - Create academic term (Registrar only)
- `GET /terms/{termId}` - Get term details
- `PATCH /terms/{termId}:open-registration` - Open enrollment (Registrar only)
- `PATCH /terms/{termId}:close-registration` - Close enrollment (Registrar only)
- `PATCH /terms/{termId}:conclude` - Conclude term (Registrar only)

### Course Management
- `POST /terms/{termId}/courses` - Create course (Professor/Registrar)
- `GET /terms/{termId}/courses` - List courses (role-filtered)
- `GET /terms/{termId}/courses/{courseId}` - Get course details
- `PATCH /terms/{termId}/courses/{courseId}:publish` - Publish course
- `PATCH /terms/{termId}/courses/{courseId}:cancel` - Cancel course

### Enrollment Management
- `POST /terms/{termId}/courses/{courseId}/enrollments` - Enroll student
- `GET /terms/{termId}/courses/{courseId}/enrollments` - List enrollments (Professor/Registrar)
- `GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}` - Get enrollment
- `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop` - Drop enrollment

### Payment Processing
- `POST /terms/{termId}/students/{studentId}:pay` - Process payment

## Authentication

All requests require these headers:
- `X-User-ID`: User's UUID
- `X-User-Role`: One of STUDENT, PROFESSOR, REGISTRAR

## Installation & Setup

1. Install dependencies:
```bash
npm install
```

2. Start the server:
```bash
npm start
```

3. For development with auto-reload:
```bash
npm run dev
```

4. Run tests:
```bash
npm test
```

## Data Models

### AcademicTerm
- States: PLANNING → ENROLLMENT_OPEN → ENROLLMENT_CLOSED → CONCLUDED
- Unique name across all terms
- Revision control for concurrent updates

### Course
- States: DRAFT → OPEN → IN_PROGRESS → COMPLETED/CANCELLED
- Delivery modes: IN_PERSON, ONLINE, HYBRID
- Capacity management with seat ledgers
- Professor ownership and limits

### Enrollment
- States: ENROLLED, WAITLISTED, DROPPED, COMPLETED
- FIFO waitlist promotion
- Credit limit enforcement for students

### Ledger Systems
- **CourseSeatLedger**: Tracks available seats per course
- **StudentTuitionLedger**: Tracks outstanding balances per student per term

## Business Constants

- `MAX_CREDITS_PER_TERM`: 18 credits
- `MAX_COURSES_PER_PROF`: 5 courses
- `MAX_DROP_COUNT_PER_TERM`: 3 drops
- `COST_PER_CREDIT`: $100.00 (10000 cents)
- `DROP_PENALTY_FEE`: $50.00 (5000 cents)

## Error Handling

The API implements comprehensive error handling with specific error codes as defined in the PRD:

- `ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER` (400)
- `ERR_UNAUTHORIZED_ROLE` (403)
- `ERR_TERM_NOT_FOUND` (404)
- `ERR_COURSE_NOT_FOUND` (404)
- `ERR_ALREADY_ENROLLED` (409)
- `ERR_CREDIT_LIMIT_EXCEEDED` (409)
- `ERR_TOO_MANY_DROPS` (409)
- And many more...

## Response Format

### Success Response
```json
{
  "meta": {
    "api_request_id": "req_ABC123",
    "api_request_timestamp": "2025-01-01T12:00:00.000Z"
  },
  "response_type": "object",
  "data": { ... }
}
```

### Error Response
```json
{
  "meta": {
    "api_request_id": "req_ABC123", 
    "api_request_timestamp": "2025-01-01T12:00:00.000Z"
  },
  "response_type": "error",
  "data": {
    "error_id": "ERR_CREDIT_LIMIT_EXCEEDED",
    "message": "Enrollment would exceed credit limit of 18 credits"
  }
}
```

## Architecture

- **In-Memory Storage**: Simple Map-based storage for development/testing
- **Event-Driven**: Async waitlist promotion and side effects
- **Modular Design**: Separate route handlers, middleware, and utilities
- **Validation Precedence**: Hierarchical validation from broad to specific
- **Stateless**: Each request is independent with proper authentication

## PRD Compliance

This implementation strictly follows the Product Requirements Document, implementing:
- All specified endpoints and methods
- Complete business logic and validation rules
- Exact error codes and response formats
- Role-based authorization and field-level security
- All system invariants and constraints
- Proper state transitions and lifecycle management

The implementation is designed to be simple, maintainable, and fully compliant with the PRD specifications.