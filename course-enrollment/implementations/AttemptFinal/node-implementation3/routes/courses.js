const express = require('express');
const router = express.Router({ mergeParams: true });
const storage = require('../models/storage');
const { ROLES, TERM_STATES, COURSE_STATES, DELIVERY_MODES, BUSINESS_RULES, ENROLLMENT_STATES } = require('../models/constants');
const { authMiddleware, requireRole, validateUUIDParam, validateUUID } = require('../middleware/auth');
const { createSuccessResponse, createErrorResponse, createListResponse, applyPagination } = require('../utils/responses');

router.use(authMiddleware);

const COURSE_CODE_PATTERN = /^[A-Z]{2,4}\d{3}$/;

function validateCourseCode(code) {
  return COURSE_CODE_PATTERN.test(code);
}

function isValidURL(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

function filterCourseForRole(course, userRole, userId) {
  const enrollments = storage.getEnrollmentsByCourse(course.id);
  const enrolledCount = enrollments.filter(e => e.state === ENROLLMENT_STATES.ENROLLED).length;
  const waitlistCount = enrollments.filter(e => e.state === ENROLLMENT_STATES.WAITLISTED).length;
  
  const seatLedger = storage.getSeatLedger(course.id);
  const availableSeats = seatLedger ? seatLedger.seats_available : course.capacity;

  const baseCourse = {
    ...course,
    enrolled_count: enrolledCount,
    waitlist_count: waitlistCount,
    available_seats: availableSeats
  };

  if (userRole === ROLES.REGISTRAR) {
    baseCourse.enrollments = enrollments;
    return baseCourse;
  }

  if (userRole === ROLES.PROFESSOR && course.professor_id === userId) {
    baseCourse.enrollments = enrollments;
    return baseCourse;
  }

  if (userRole === ROLES.STUDENT) {
    if ([COURSE_STATES.DRAFT, COURSE_STATES.CANCELLED].includes(course.state)) {
      const studentEnrollment = enrollments.find(e => e.student_id === userId);
      if (!studentEnrollment) {
        return null;
      }
    }

    const studentEnrollment = enrollments.find(e => e.student_id === userId);
    if (studentEnrollment) {
      baseCourse.is_enrolled = studentEnrollment.state === ENROLLMENT_STATES.ENROLLED;
      baseCourse.is_waitlisted = studentEnrollment.state === ENROLLMENT_STATES.WAITLISTED;
    }

    delete baseCourse.enrolled_count;
    delete baseCourse.waitlist_count;
    return baseCourse;
  }

  if (userRole === ROLES.PROFESSOR) {
    if ([COURSE_STATES.DRAFT, COURSE_STATES.CANCELLED].includes(course.state)) {
      return null;
    }
    
    delete baseCourse.enrolled_count;
    delete baseCourse.waitlist_count;
    return baseCourse;
  }

  return baseCourse;
}

router.post('/', (req, res) => {
  const { termId } = req.params;
  const { code, title, description, credits, capacity, professor_id, delivery_mode, location, online_link } = req.body;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  const allowedFields = ['code', 'title', 'description', 'credits', 'capacity', 'professor_id', 'delivery_mode', 'location', 'online_link'];
  const unknownFields = Object.keys(req.body).filter(key => !allowedFields.includes(key));
  if (unknownFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${unknownFields.join(', ')}`
    ));
  }

  if (!code || !validateCourseCode(code)) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_COURSE_CODE',
      'Course code must be 2-4 uppercase letters followed by 3 digits (e.g., CS101)'
    ));
  }

  if (!title || typeof title !== 'string' || title.trim().length === 0 || title.length > 100) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_FIELD_LENGTH',
      'Title is required and must be 1-100 characters'
    ));
  }

  if (description && (typeof description !== 'string' || description.length > 1000)) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_FIELD_LENGTH',
      'Description must be 1000 characters or less'
    ));
  }

  if (!credits || !Number.isInteger(credits) || credits < 1 || credits > 5) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_CREDITS',
      'Credits must be an integer between 1 and 5'
    ));
  }

  if (!capacity || !Number.isInteger(capacity) || capacity < 1 || capacity > 500) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_CAPACITY',
      'Capacity must be an integer between 1 and 500'
    ));
  }

  if (!delivery_mode || !Object.values(DELIVERY_MODES).includes(delivery_mode)) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_ENUM_VALUE',
      `Delivery mode must be one of: ${Object.values(DELIVERY_MODES).join(', ')}`
    ));
  }

  if (delivery_mode === DELIVERY_MODES.IN_PERSON) {
    if (!location || typeof location !== 'string' || location.trim().length === 0) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        'Location is required for IN_PERSON delivery mode'
      ));
    }
    if (online_link) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_FIELD_CONFLICT',
        'Online link cannot be provided for IN_PERSON delivery mode'
      ));
    }
  } else if (delivery_mode === DELIVERY_MODES.ONLINE) {
    if (!online_link || typeof online_link !== 'string' || !isValidURL(online_link)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        'Valid online link is required for ONLINE delivery mode'
      ));
    }
    if (location) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_FIELD_CONFLICT',
        'Location cannot be provided for ONLINE delivery mode'
      ));
    }
  } else if (delivery_mode === DELIVERY_MODES.HYBRID) {
    if (!location && !online_link) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        'At least one of location or online_link is required for HYBRID delivery mode'
      ));
    }
    if (online_link && !isValidURL(online_link)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        'Online link must be a valid URL when provided'
      ));
    }
  }

  const existingCourses = storage.getCoursesByTerm(termId);
  if (existingCourses.some(course => course.code === code)) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_COURSE_CODE_NOT_UNIQUE',
      'A course with this code already exists in this term'
    ));
  }

  let finalProfessorId;
  if (req.user.role === ROLES.REGISTRAR) {
    if (!professor_id || !validateUUID(professor_id)) {
      return res.status(422).json(createErrorResponse(
        req,
        'ERR_INVALID_INSTRUCTOR',
        'Valid professor_id is required when creating course as Registrar'
      ));
    }
    finalProfessorId = professor_id;
  } else if (req.user.role === ROLES.PROFESSOR) {
    if (professor_id && professor_id !== req.user.id) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_FIELD_CONFLICT',
        'Professors can only create courses for themselves'
      ));
    }
    finalProfessorId = req.user.id;
  } else {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_UNAUTHORIZED_ROLE',
      'Only Professors and Registrars can create courses'
    ));
  }

  const professorCourses = existingCourses.filter(course => 
    course.professor_id === finalProfessorId && 
    ![COURSE_STATES.CANCELLED, COURSE_STATES.COMPLETED].includes(course.state)
  );
  
  if (professorCourses.length >= BUSINESS_RULES.MAX_COURSES_PER_PROF) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_MAX_COURSES_REACHED',
      `Professor cannot teach more than ${BUSINESS_RULES.MAX_COURSES_PER_PROF} courses per term`
    ));
  }

  const courseData = {
    code,
    title: title.trim(),
    description: description ? description.trim() : null,
    credits,
    capacity,
    professor_id: finalProfessorId,
    delivery_mode,
    state: COURSE_STATES.DRAFT
  };

  if (location) courseData.location = location.trim();
  if (online_link) courseData.online_link = online_link;

  const course = storage.createCourse(termId, courseData);

  storage.log('course_created', { 
    course_id: course.id, 
    term_id: termId, 
    created_by: req.user.id,
    professor_id: finalProfessorId
  });

  res.status(201).json(createSuccessResponse(req, course));
});

router.get('/', (req, res) => {
  const { termId } = req.params;
  const { limit, offset, state, professor_id } = req.query;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  let courses = storage.getCoursesByTerm(termId);

  if (state) {
    if (!Object.values(COURSE_STATES).includes(state)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ENUM_VALUE',
        `Invalid state filter. Must be one of: ${Object.values(COURSE_STATES).join(', ')}`
      ));
    }
    courses = courses.filter(course => course.state === state);
  }

  if (professor_id) {
    if (!validateUUID(professor_id)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'Invalid professor_id format'
      ));
    }
    courses = courses.filter(course => course.professor_id === professor_id);
  }

  const filteredCourses = courses
    .map(course => filterCourseForRole(course, req.user.role, req.user.id))
    .filter(course => course !== null);

  const { items, total } = applyPagination(filteredCourses, limit, offset);

  const response = createListResponse(req, items, total);
  if (limit || offset) {
    response.meta.limit = parseInt(limit) || 50;
    response.meta.offset = parseInt(offset) || 0;
  }

  res.json(response);
});

router.get('/:courseId', validateUUIDParam('courseId'), (req, res) => {
  const { termId, courseId } = req.params;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  const course = storage.getCourse(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_COURSE_NOT_FOUND',
      'Course not found'
    ));
  }

  const filteredCourse = filterCourseForRole(course, req.user.role, req.user.id);
  if (!filteredCourse) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_COURSE_NOT_FOUND',
      'Course not found'
    ));
  }

  res.json(createSuccessResponse(req, filteredCourse));
});

router.patch('/:courseId\\:publish', validateUUIDParam('courseId'), (req, res) => {
  const { termId, courseId } = req.params;
  const { revision } = req.body;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_ACTIVE',
      'Term must be in ENROLLMENT_OPEN state to publish courses'
    ));
  }

  const course = storage.getCourse(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_COURSE_NOT_FOUND',
      'Course not found'
    ));
  }

  if (req.user.role === ROLES.PROFESSOR && course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_NOT_INSTRUCTOR',
      'Only the course instructor can publish this course'
    ));
  } else if (req.user.role === ROLES.STUDENT) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_UNAUTHORIZED_ROLE',
      'Students cannot publish courses'
    ));
  }

  if (revision !== undefined && course.revision !== revision) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_REV_CONFLICT',
      'Course has been modified by another request. Please refetch and try again.'
    ));
  }

  if (course.state !== COURSE_STATES.DRAFT) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_COURSE_WRONG_STATE',
      'Course must be in DRAFT state to publish'
    ));
  }

  const updatedCourse = storage.updateCourse(courseId, {
    state: COURSE_STATES.OPEN,
    published_at: new Date().toISOString()
  });

  storage.initializeSeatLedger(courseId, course.capacity);

  storage.log('course_published', {
    course_id: courseId,
    term_id: termId,
    published_by: req.user.id
  });

  res.json(createSuccessResponse(req, updatedCourse));
});

router.patch('/:courseId\\:cancel', validateUUIDParam('courseId'), (req, res) => {
  const { termId, courseId } = req.params;
  const { revision } = req.body;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  const course = storage.getCourse(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_COURSE_NOT_FOUND',
      'Course not found'
    ));
  }

  if (req.user.role === ROLES.PROFESSOR && course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_NOT_INSTRUCTOR',
      'Only the course instructor can cancel this course'
    ));
  } else if (req.user.role === ROLES.STUDENT) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_UNAUTHORIZED_ROLE',
      'Students cannot cancel courses'
    ));
  }

  if (revision !== undefined && course.revision !== revision) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_REV_CONFLICT',
      'Course has been modified by another request. Please refetch and try again.'
    ));
  }

  if ([COURSE_STATES.COMPLETED, COURSE_STATES.CANCELLED].includes(course.state)) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_COURSE_WRONG_STATE',
      'Course cannot be cancelled from its current state'
    ));
  }

  const updatedCourse = storage.updateCourse(courseId, {
    state: COURSE_STATES.CANCELLED
  });

  const enrollments = storage.getEnrollmentsByCourse(courseId);
  enrollments.forEach(enrollment => {
    if ([ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED].includes(enrollment.state)) {
      storage.updateEnrollment(enrollment.id, { state: ENROLLMENT_STATES.DROPPED });

      if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
        try {
          storage.updateSeatLedger(courseId, 1);
        } catch (error) {
          // Ignore seat ledger errors during cancellation
        }

        try {
          const courseCost = course.credits * BUSINESS_RULES.COST_PER_CREDIT;
          storage.updateTuitionLedger(enrollment.student_id, termId, -courseCost);
        } catch (error) {
          // Ignore tuition ledger errors during cancellation
        }
      }

      storage.log('enrollment_dropped_due_to_course_cancellation', {
        enrollment_id: enrollment.id,
        student_id: enrollment.student_id,
        course_id: courseId,
        cancelled_by: req.user.id
      });
    }
  });

  storage.log('course_cancelled', {
    course_id: courseId,
    term_id: termId,
    cancelled_by: req.user.id
  });

  res.json(createSuccessResponse(req, updatedCourse));
});

module.exports = router;