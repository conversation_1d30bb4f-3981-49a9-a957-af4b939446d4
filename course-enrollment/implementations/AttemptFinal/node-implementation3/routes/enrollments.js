const express = require('express');
const router = express.Router({ mergeParams: true });
const storage = require('../models/storage');
const { ROLES, TERM_STATES, COURSE_STATES, ENROLLMENT_STATES, BUSINESS_RULES } = require('../models/constants');
const { authMiddleware, requireRole, validateUUIDParam, validateUUID } = require('../middleware/auth');
const { createSuccessResponse, createErrorResponse, createListResponse, applyPagination } = require('../utils/responses');

router.use(authMiddleware);

function promoteFromWaitlist(courseId, termId) {
  const enrollments = storage.getEnrollmentsByCourse(courseId);
  const waitlistedEnrollments = enrollments
    .filter(e => e.state === ENROLLMENT_STATES.WAITLISTED)
    .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  if (waitlistedEnrollments.length === 0) {
    return null;
  }

  const seatLedger = storage.getSeatLedger(courseId);
  if (!seatLedger || seatLedger.seats_available <= 0) {
    return null;
  }

  const nextStudent = waitlistedEnrollments[0];
  const course = storage.getCourse(courseId);

  try {
    storage.updateSeatLedger(courseId, -1);
    
    const courseCost = course.credits * BUSINESS_RULES.COST_PER_CREDIT;
    storage.updateTuitionLedger(nextStudent.student_id, termId, courseCost);
    
    const updatedEnrollment = storage.updateEnrollment(nextStudent.id, {
      state: ENROLLMENT_STATES.ENROLLED
    });

    storage.log('waitlist_promoted', {
      enrollment_id: nextStudent.id,
      student_id: nextStudent.student_id,
      course_id: courseId,
      promoted_at: new Date().toISOString()
    });

    return updatedEnrollment;
  } catch (error) {
    console.error('Error promoting from waitlist:', error);
    return null;
  }
}

function getStudentDropCount(studentId, termId) {
  const studentEnrollments = storage.getEnrollmentsByStudent(studentId, termId);
  return studentEnrollments.filter(e => e.state === ENROLLMENT_STATES.DROPPED).length;
}

function getStudentTotalCredits(studentId, termId) {
  const studentEnrollments = storage.getEnrollmentsByStudent(studentId, termId);
  let totalCredits = 0;
  
  studentEnrollments.forEach(enrollment => {
    if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
      const course = storage.getCourse(enrollment.course_id);
      if (course) {
        totalCredits += course.credits;
      }
    }
  });
  
  return totalCredits;
}

router.post('/', (req, res) => {
  const { termId, courseId } = req.params;
  const { student_id } = req.body;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_REGISTRATION_CLOSED',
      'Enrollment is not currently open for this term'
    ));
  }

  const course = storage.getCourse(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_COURSE_NOT_FOUND',
      'Course not found'
    ));
  }

  if (course.state !== COURSE_STATES.OPEN) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_COURSE_WRONG_STATE',
      'Course is not open for enrollment'
    ));
  }

  let targetStudentId;
  if (req.user.role === ROLES.STUDENT) {
    if (student_id && student_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_FORBIDDEN',
        'Students can only enroll themselves'
      ));
    }
    targetStudentId = req.user.id;
  } else if (req.user.role === ROLES.REGISTRAR) {
    if (!student_id || !validateUUID(student_id)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_MISSING_REQUIRED_FIELD',
        'student_id is required when Registrar enrolls a student'
      ));
    }
    targetStudentId = student_id;
  } else {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_UNAUTHORIZED_ROLE',
      'Only Students and Registrars can create enrollments'
    ));
  }

  const allowedFields = ['student_id'];
  const unknownFields = Object.keys(req.body).filter(key => !allowedFields.includes(key));
  if (unknownFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${unknownFields.join(', ')}`
    ));
  }

  const existingEnrollments = storage.getEnrollmentsByCourse(courseId);
  const studentExistingEnrollment = existingEnrollments.find(e => 
    e.student_id === targetStudentId && 
    [ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED].includes(e.state)
  );

  if (studentExistingEnrollment) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_ALREADY_ENROLLED',
      'Student is already enrolled or waitlisted in this course'
    ));
  }

  if (req.user.role === ROLES.STUDENT) {
    const currentCredits = getStudentTotalCredits(targetStudentId, termId);
    if (currentCredits + course.credits > BUSINESS_RULES.MAX_CREDITS_PER_TERM) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_CREDIT_LIMIT_EXCEEDED',
        `Enrollment would exceed credit limit of ${BUSINESS_RULES.MAX_CREDITS_PER_TERM} credits`
      ));
    }
  }

  const seatLedger = storage.getSeatLedger(courseId);
  const hasAvailableSeat = seatLedger && seatLedger.seats_available > 0;

  let enrollmentState;
  let shouldChargeTuition = false;
  let shouldDecrementSeat = false;

  if (hasAvailableSeat) {
    enrollmentState = ENROLLMENT_STATES.ENROLLED;
    shouldChargeTuition = true;
    shouldDecrementSeat = true;
  } else {
    enrollmentState = ENROLLMENT_STATES.WAITLISTED;
  }

  const enrollment = storage.createEnrollment({
    course_id: courseId,
    student_id: targetStudentId,
    state: enrollmentState
  });

  if (shouldDecrementSeat) {
    try {
      storage.updateSeatLedger(courseId, -1);
    } catch (error) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_CAPACITY_EXCEEDED',
        'Course capacity exceeded'
      ));
    }
  }

  if (shouldChargeTuition) {
    try {
      const courseCost = course.credits * BUSINESS_RULES.COST_PER_CREDIT;
      storage.updateTuitionLedger(targetStudentId, termId, courseCost);
    } catch (error) {
      storage.updateSeatLedger(courseId, 1);
      return res.status(422).json(createErrorResponse(
        req,
        'ERR_LEDGER_INVALID_OP',
        'Error processing tuition charge'
      ));
    }
  }

  storage.log('enrollment_created', {
    enrollment_id: enrollment.id,
    student_id: targetStudentId,
    course_id: courseId,
    term_id: termId,
    state: enrollmentState,
    created_by: req.user.id
  });

  res.status(201).json(createSuccessResponse(req, enrollment));
});

router.get('/', (req, res) => {
  const { termId, courseId } = req.params;
  const { limit, offset } = req.query;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  const course = storage.getCourse(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_COURSE_NOT_FOUND',
      'Course not found'
    ));
  }

  if (req.user.role === ROLES.STUDENT) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_UNAUTHORIZED_ROLE',
      'Students cannot view course rosters'
    ));
  }

  if (req.user.role === ROLES.PROFESSOR && course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_NOT_INSTRUCTOR',
      'Professors can only view rosters for their own courses'
    ));
  }

  const enrollments = storage.getEnrollmentsByCourse(courseId);
  const sortedEnrollments = enrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  const { items, total } = applyPagination(sortedEnrollments, limit, offset);

  const response = createListResponse(req, items, total);
  if (limit || offset) {
    response.meta.limit = parseInt(limit) || 50;
    response.meta.offset = parseInt(offset) || 0;
  }

  res.json(response);
});

router.get('/:enrollmentId', validateUUIDParam('enrollmentId'), (req, res) => {
  const { termId, courseId, enrollmentId } = req.params;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  const course = storage.getCourse(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_COURSE_NOT_FOUND',
      'Course not found'
    ));
  }

  const enrollment = storage.getEnrollment(enrollmentId);
  if (!enrollment || enrollment.course_id !== courseId) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_ENROLLMENT_NOT_FOUND',
      'Enrollment not found'
    ));
  }

  if (req.user.role === ROLES.STUDENT && enrollment.student_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_FORBIDDEN',
      'Students can only view their own enrollments'
    ));
  }

  if (req.user.role === ROLES.PROFESSOR && course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_NOT_INSTRUCTOR',
      'Professors can only view enrollments for their own courses'
    ));
  }

  res.json(createSuccessResponse(req, enrollment));
});

router.patch('/:enrollmentId\\:drop', validateUUIDParam('enrollmentId'), (req, res) => {
  const { termId, courseId, enrollmentId } = req.params;
  const { revision } = req.body;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  if (term.state === TERM_STATES.CONCLUDED) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_ACTIVE',
      'Cannot drop from courses in a concluded term'
    ));
  }

  const course = storage.getCourse(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_COURSE_NOT_FOUND',
      'Course not found'
    ));
  }

  const enrollment = storage.getEnrollment(enrollmentId);
  if (!enrollment || enrollment.course_id !== courseId) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_ENROLLMENT_NOT_FOUND',
      'Enrollment not found'
    ));
  }

  if (req.user.role === ROLES.STUDENT && enrollment.student_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_FORBIDDEN',
      'Students can only drop their own enrollments'
    ));
  }

  if (req.user.role === ROLES.PROFESSOR && course.professor_id !== req.user.id) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_NOT_INSTRUCTOR',
      'Professors can only drop enrollments from their own courses'
    ));
  }

  if (revision !== undefined && enrollment.revision !== revision) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_REV_CONFLICT',
      'Enrollment has been modified by another request. Please refetch and try again.'
    ));
  }

  if ([ENROLLMENT_STATES.DROPPED, ENROLLMENT_STATES.COMPLETED].includes(enrollment.state)) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_ENROLLMENT_WRONG_STATE',
      'Enrollment cannot be dropped from its current state'
    ));
  }

  if (req.user.role === ROLES.STUDENT) {
    const currentDropCount = getStudentDropCount(enrollment.student_id, termId);
    if (currentDropCount >= BUSINESS_RULES.MAX_DROP_COUNT_PER_TERM) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_TOO_MANY_DROPS',
        `Student has reached maximum drop limit of ${BUSINESS_RULES.MAX_DROP_COUNT_PER_TERM} courses per term`
      ));
    }
  }

  const wasEnrolled = enrollment.state === ENROLLMENT_STATES.ENROLLED;

  const updatedEnrollment = storage.updateEnrollment(enrollmentId, {
    state: ENROLLMENT_STATES.DROPPED
  });

  if (wasEnrolled) {
    try {
      storage.updateSeatLedger(courseId, 1);
    } catch (error) {
      console.error('Error updating seat ledger on drop:', error);
    }

    try {
      const courseCost = course.credits * BUSINESS_RULES.COST_PER_CREDIT;
      storage.updateTuitionLedger(enrollment.student_id, termId, -courseCost);
    } catch (error) {
      console.error('Error updating tuition ledger on drop:', error);
    }

    setTimeout(() => {
      promoteFromWaitlist(courseId, termId);
    }, 0);
  }

  if (req.user.role === ROLES.STUDENT) {
    const newDropCount = getStudentDropCount(enrollment.student_id, termId);
    if (newDropCount === BUSINESS_RULES.MAX_DROP_COUNT_PER_TERM) {
      try {
        storage.updateTuitionLedger(enrollment.student_id, termId, BUSINESS_RULES.DROP_PENALTY_FEE);
        storage.log('drop_penalty_applied', {
          student_id: enrollment.student_id,
          term_id: termId,
          penalty_amount: BUSINESS_RULES.DROP_PENALTY_FEE,
          drop_count: newDropCount
        });
      } catch (error) {
        console.error('Error applying drop penalty:', error);
      }
    }
  }

  storage.log('enrollment_dropped', {
    enrollment_id: enrollmentId,
    student_id: enrollment.student_id,
    course_id: courseId,
    term_id: termId,
    was_enrolled: wasEnrolled,
    dropped_by: req.user.id
  });

  res.json(createSuccessResponse(req, updatedEnrollment));
});

module.exports = router;