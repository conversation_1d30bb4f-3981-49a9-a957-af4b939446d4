const express = require('express');
const router = express.Router({ mergeParams: true });
const storage = require('../models/storage');
const { ROLES } = require('../models/constants');
const { authMiddleware, validateUUIDParam, validateUUID } = require('../middleware/auth');
const { createSuccessResponse, createErrorResponse } = require('../utils/responses');

router.use(authMiddleware);

router.post('/:studentId\\:pay', validateUUIDParam('studentId'), (req, res) => {
  const { termId, studentId } = req.params;
  const { amount } = req.body;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  if (req.user.role === ROLES.STUDENT && req.user.id !== studentId) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_FORBIDDEN',
      'Students can only pay their own tuition'
    ));
  }

  if (req.user.role === ROLES.PROFESSOR) {
    return res.status(403).json(createErrorResponse(
      req,
      'ERR_UNAUTHORIZED_ROLE',
      'Professors cannot process payments'
    ));
  }

  const allowedFields = ['amount'];
  const unknownFields = Object.keys(req.body).filter(key => !allowedFields.includes(key));
  if (unknownFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${unknownFields.join(', ')}`
    ));
  }

  if (!amount || !Number.isInteger(amount) || amount <= 0) {
    return res.status(422).json(createErrorResponse(
      req,
      'ERR_INVALID_PAYMENT_AMOUNT',
      'Payment amount must be a positive integer (in cents)'
    ));
  }

  let tuitionLedger = storage.getTuitionLedger(studentId, termId);
  if (!tuitionLedger) {
    tuitionLedger = storage.initializeTuitionLedger(studentId, termId);
  }

  if (amount > tuitionLedger.balance_cents) {
    return res.status(422).json(createErrorResponse(
      req,
      'ERR_OVERPAY_NOT_ALLOWED',
      `Payment amount exceeds outstanding balance. Current balance: ${tuitionLedger.balance_cents} cents`
    ));
  }

  try {
    const updatedLedger = storage.updateTuitionLedger(studentId, termId, -amount);

    storage.log('payment_processed', {
      student_id: studentId,
      term_id: termId,
      amount_paid: amount,
      new_balance: updatedLedger.balance_cents,
      processed_by: req.user.id
    });

    if (updatedLedger.balance_cents === 0) {
      storage.log('tuition_fully_paid', {
        student_id: studentId,
        term_id: termId
      });
    }

    const responseData = {
      student_id: studentId,
      term_id: termId,
      amount_paid: amount,
      new_balance: updatedLedger.balance_cents
    };

    res.json(createSuccessResponse(req, responseData));
  } catch (error) {
    return res.status(422).json(createErrorResponse(
      req,
      'ERR_LEDGER_INVALID_OP',
      'Error processing payment'
    ));
  }
});

module.exports = router;