const express = require('express');
const router = express.Router();
const storage = require('../models/storage');
const { ROLES, TERM_STATES, COURSE_STATES, ENROLLMENT_STATES } = require('../models/constants');
const { authMiddleware, requireRole, validateUUIDParam } = require('../middleware/auth');
const { createSuccessResponse, createErrorResponse } = require('../utils/responses');

router.use(authMiddleware);

router.post('/', requireRole(ROLES.REGISTRAR), (req, res) => {
  const { name } = req.body;

  if (!name || typeof name !== 'string' || name.trim().length === 0) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_MISSING_REQUIRED_FIELD',
      'Term name is required and cannot be empty'
    ));
  }

  const existingTerms = Array.from(storage.terms.values());
  if (existingTerms.some(term => term.name === name.trim())) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_TERM_NAME_NOT_UNIQUE',
      'A term with this name already exists'
    ));
  }

  const unknownFields = Object.keys(req.body).filter(key => !['name'].includes(key));
  if (unknownFields.length > 0) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${unknownFields.join(', ')}`
    ));
  }

  const term = storage.createTerm({
    name: name.trim(),
    state: TERM_STATES.PLANNING,
    created_by: req.user.id
  });

  storage.log('term_created', { term_id: term.id, created_by: req.user.id });

  res.status(201).json(createSuccessResponse(req, term));
});

router.get('/:termId', validateUUIDParam('termId'), (req, res) => {
  const { termId } = req.params;
  const term = storage.getTerm(termId);

  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  res.json(createSuccessResponse(req, term));
});

router.patch('/:termId\\:open-registration', validateUUIDParam('termId'), requireRole(ROLES.REGISTRAR), (req, res) => {
  const { termId } = req.params;
  const { revision } = req.body;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  if (revision !== undefined && term.revision !== revision) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_REV_CONFLICT',
      'Term has been modified by another request. Please refetch and try again.'
    ));
  }

  if (term.state !== TERM_STATES.PLANNING) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_ACTIVE',
      'Term must be in PLANNING state to open registration'
    ));
  }

  const updatedTerm = storage.updateTerm(termId, {
    state: TERM_STATES.ENROLLMENT_OPEN
  });

  storage.log('term_registration_opened', { term_id: termId, opened_by: req.user.id });

  res.json(createSuccessResponse(req, updatedTerm));
});

router.patch('/:termId\\:close-registration', validateUUIDParam('termId'), requireRole(ROLES.REGISTRAR), (req, res) => {
  const { termId } = req.params;
  const { revision } = req.body;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  if (revision !== undefined && term.revision !== revision) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_REV_CONFLICT',
      'Term has been modified by another request. Please refetch and try again.'
    ));
  }

  if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_ACTIVE',
      'Term must be in ENROLLMENT_OPEN state to close registration'
    ));
  }

  const updatedTerm = storage.updateTerm(termId, {
    state: TERM_STATES.ENROLLMENT_CLOSED
  });

  const courses = storage.getCoursesByTerm(termId);
  courses.forEach(course => {
    if (course.state === COURSE_STATES.OPEN) {
      storage.updateCourse(course.id, { state: COURSE_STATES.IN_PROGRESS });
      storage.log('course_transitioned_to_in_progress', { 
        course_id: course.id, 
        term_id: termId,
        triggered_by: 'term_registration_closed'
      });
    }
  });

  storage.log('term_registration_closed', { term_id: termId, closed_by: req.user.id });

  res.json(createSuccessResponse(req, updatedTerm));
});

router.patch('/:termId\\:conclude', validateUUIDParam('termId'), requireRole(ROLES.REGISTRAR), (req, res) => {
  const { termId } = req.params;
  const { revision } = req.body;

  const term = storage.getTerm(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_FOUND',
      'Term not found'
    ));
  }

  if (revision !== undefined && term.revision !== revision) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_REV_CONFLICT',
      'Term has been modified by another request. Please refetch and try again.'
    ));
  }

  if (term.state !== TERM_STATES.ENROLLMENT_CLOSED) {
    return res.status(409).json(createErrorResponse(
      req,
      'ERR_TERM_NOT_ACTIVE',
      'Term must be in ENROLLMENT_CLOSED state to conclude'
    ));
  }

  const updatedTerm = storage.updateTerm(termId, {
    state: TERM_STATES.CONCLUDED
  });

  const courses = storage.getCoursesByTerm(termId);
  courses.forEach(course => {
    if (course.state === COURSE_STATES.IN_PROGRESS) {
      storage.updateCourse(course.id, { state: COURSE_STATES.COMPLETED });
      storage.log('course_completed', { 
        course_id: course.id, 
        term_id: termId,
        triggered_by: 'term_concluded'
      });
    }

    const enrollments = storage.getEnrollmentsByCourse(course.id);
    enrollments.forEach(enrollment => {
      if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
        storage.updateEnrollment(enrollment.id, { state: ENROLLMENT_STATES.COMPLETED });
        storage.log('enrollment_completed', {
          enrollment_id: enrollment.id,
          student_id: enrollment.student_id,
          course_id: course.id,
          triggered_by: 'term_concluded'
        });
      } else if (enrollment.state === ENROLLMENT_STATES.WAITLISTED) {
        storage.updateEnrollment(enrollment.id, { state: ENROLLMENT_STATES.DROPPED });
        storage.log('enrollment_dropped_from_waitlist', {
          enrollment_id: enrollment.id,
          student_id: enrollment.student_id,
          course_id: course.id,
          triggered_by: 'term_concluded'
        });
      }
    });
  });

  storage.log('term_concluded', { term_id: termId, concluded_by: req.user.id });

  res.json(createSuccessResponse(req, updatedTerm));
});

module.exports = router;