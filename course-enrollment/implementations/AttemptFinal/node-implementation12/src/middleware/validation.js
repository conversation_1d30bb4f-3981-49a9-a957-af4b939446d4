const Joi = require('joi');
const { CONSTANTS } = require('../models');

// Common validation schemas
const uuidSchema = Joi.string().uuid().required();
const paginationSchema = Joi.object({
  limit: Joi.number().integer().min(1).max(1000).default(50),
  offset: Joi.number().integer().min(0).default(0)
});

// Request validation middleware
const validateRequest = (schema, target = 'body') => {
  return (req, res, next) => {
    const dataToValidate = req[target];
    const { error, value } = schema.validate(dataToValidate, { 
      abortEarly: false,
      stripUnknown: false,
      allowUnknown: false
    });

    if (error) {
      // Check for unknown fields
      const unknownField = error.details.find(detail => detail.type === 'object.unknown');
      if (unknownField) {
        return res.apiError('ERR_UNKNOWN_FIELD', `Unknown field: ${unknownField.path}`, 400);
      }

      // Check for missing required fields
      const missingField = error.details.find(detail => detail.type === 'any.required');
      if (missingField) {
        return res.apiError('ERR_MISSING_REQUIRED_FIELD', `Missing required field: ${missingField.path}`, 400);
      }

      // General validation error
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    // Update request with validated data
    req[target] = value;
    next();
  };
};

// Path parameter validation middleware
const validateParams = (paramSchema) => {
  return (req, res, next) => {
    const { error, value } = paramSchema.validate(req.params);
    if (error) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid parameter format', 400);
    }
    req.params = value;
    next();
  };
};

// Query parameter validation middleware
const validateQuery = (querySchema) => {
  return (req, res, next) => {
    const { error, value } = querySchema.validate(req.query);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }
    req.query = value;
    next();
  };
};

// Field length validation
const validateFieldLength = (field, minLength = 0, maxLength = Infinity) => {
  return (req, res, next) => {
    const value = req.body[field];
    if (value !== undefined) {
      if (typeof value !== 'string') {
        return res.apiError('ERR_INVALID_FIELD_TYPE', `Field ${field} must be a string`, 400);
      }
      if (value.length < minLength || value.length > maxLength) {
        return res.apiError('ERR_INVALID_FIELD_LENGTH', 
          `Field ${field} must be between ${minLength} and ${maxLength} characters`, 400);
      }
    }
    next();
  };
};

// Enum validation
const validateEnum = (field, enumValues, required = false) => {
  return (req, res, next) => {
    const value = req.body[field];
    if (value !== undefined) {
      if (!enumValues.includes(value)) {
        return res.apiError('ERR_INVALID_ENUM_VALUE', 
          `Invalid value for ${field}. Must be one of: ${enumValues.join(', ')}`, 400);
      }
    } else if (required) {
      return res.apiError('ERR_MISSING_REQUIRED_FIELD', `Field ${field} is required`, 400);
    }
    next();
  };
};

// Credit validation
const validateCredits = (req, res, next) => {
  const credits = req.body.credits;
  if (credits !== undefined) {
    if (!Number.isInteger(credits) || credits < 1 || credits > 5) {
      return res.apiError('ERR_INVALID_CREDITS', 'Credits must be an integer between 1 and 5', 400);
    }
  }
  next();
};

// Capacity validation
const validateCapacity = (req, res, next) => {
  const capacity = req.body.capacity;
  if (capacity !== undefined) {
    if (!Number.isInteger(capacity) || capacity < 1 || capacity > 500) {
      return res.apiError('ERR_INVALID_CAPACITY', 'Capacity must be an integer between 1 and 500', 400);
    }
  }
  next();
};

// Course code validation
const validateCourseCode = (req, res, next) => {
  const code = req.body.code;
  if (code !== undefined) {
    const codeRegex = /^[A-Z]{2,4}\d{3}$/;
    if (!codeRegex.test(code)) {
      return res.apiError('ERR_INVALID_COURSE_CODE', 
        'Course code must be 2-4 uppercase letters followed by 3 digits (e.g., CS101)', 400);
    }
  }
  next();
};

// Payment amount validation
const validatePaymentAmount = (req, res, next) => {
  const amount = req.body.amount;
  if (amount !== undefined) {
    if (!Number.isInteger(amount) || amount <= 0) {
      return res.apiError('ERR_INVALID_PAYMENT_AMOUNT', 
        'Payment amount must be a positive integer (in cents)', 422);
    }
  }
  next();
};

// Delivery mode conditional validation
const validateDeliveryMode = (req, res, next) => {
  const { delivery_mode, location, online_link } = req.body;
  
  if (delivery_mode) {
    switch (delivery_mode) {
      case CONSTANTS.DELIVERY_MODES.IN_PERSON:
        if (!location) {
          return res.apiError('ERR_CONDITIONAL_FIELD_REQUIRED', 
            'Location is required for IN_PERSON delivery mode', 400);
        }
        if (online_link) {
          return res.apiError('ERR_FIELD_CONFLICT', 
            'online_link cannot be provided for IN_PERSON delivery mode', 400);
        }
        break;
        
      case CONSTANTS.DELIVERY_MODES.ONLINE:
        if (!online_link) {
          return res.apiError('ERR_CONDITIONAL_FIELD_REQUIRED', 
            'online_link is required for ONLINE delivery mode', 400);
        }
        if (location) {
          return res.apiError('ERR_FIELD_CONFLICT', 
            'location cannot be provided for ONLINE delivery mode', 400);
        }
        break;
        
      case CONSTANTS.DELIVERY_MODES.HYBRID:
        if (!location && !online_link) {
          return res.apiError('ERR_CONDITIONAL_FIELD_REQUIRED', 
            'At least one of location or online_link is required for HYBRID delivery mode', 400);
        }
        break;
    }
  }
  
  next();
};

module.exports = {
  validateRequest,
  validateParams,
  validateQuery,
  validateFieldLength,
  validateEnum,
  validateCredits,
  validateCapacity,
  validateCourseCode,
  validatePaymentAmount,
  validateDeliveryMode,
  uuidSchema,
  paginationSchema
};