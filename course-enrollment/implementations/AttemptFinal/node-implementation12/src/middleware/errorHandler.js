const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Default error response
  let errorId = 'ERR_INTERNAL_SERVER_ERROR';
  let message = 'An internal server error occurred';
  let statusCode = 500;

  // Handle specific error types
  if (err.message && err.message.startsWith('ERR_')) {
    errorId = err.message;
    
    // Map error IDs to status codes and messages
    const errorMap = {
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER': { status: 400, message: 'Missing or invalid user context headers' },
      'ERR_UNAUTHORIZED_ROLE': { status: 403, message: 'Unauthorized role' },
      'ERR_NOT_INSTRUCTOR': { status: 403, message: 'Not the course instructor' },
      'ERR_PERMISSION_DENIED': { status: 403, message: 'Permission denied' },
      'ERR_TERM_NOT_FOUND': { status: 404, message: 'Term not found' },
      'ERR_COURSE_NOT_FOUND': { status: 404, message: 'Course not found' },
      'ERR_ENROLLMENT_NOT_FOUND': { status: 404, message: 'Enrollment not found' },
      'ERR_STUDENT_NOT_FOUND': { status: 404, message: 'Student not found' },
      'ERR_TERM_CLOSED': { status: 404, message: 'Term is closed' },
      'ERR_INVALID_ID_FORMAT': { status: 400, message: 'Invalid ID format' },
      'ERR_INVALID_COURSE_CODE': { status: 400, message: 'Invalid course code format' },
      'ERR_COURSE_CODE_NOT_UNIQUE': { status: 409, message: 'Course code already exists in this term' },
      'ERR_INVALID_FIELD_LENGTH': { status: 400, message: 'Invalid field length' },
      'ERR_INVALID_CREDITS': { status: 400, message: 'Invalid credits value' },
      'ERR_INVALID_CAPACITY': { status: 400, message: 'Invalid capacity value' },
      'ERR_INVALID_ENUM_VALUE': { status: 400, message: 'Invalid enum value' },
      'ERR_MISSING_REQUIRED_FIELD': { status: 400, message: 'Missing required field' },
      'ERR_CONDITIONAL_FIELD_REQUIRED': { status: 400, message: 'Conditional field required' },
      'ERR_FIELD_CONFLICT': { status: 400, message: 'Field conflict' },
      'ERR_UNKNOWN_FIELD': { status: 400, message: 'Unknown field in request' },
      'ERR_COURSE_WRONG_STATE': { status: 409, message: 'Course is in wrong state for this operation' },
      'ERR_ENROLLMENT_WRONG_STATE': { status: 409, message: 'Enrollment is in wrong state for this operation' },
      'ERR_TERM_NOT_ACTIVE': { status: 409, message: 'Term is not active for this operation' },
      'ERR_REGISTRATION_CLOSED': { status: 409, message: 'Registration is closed' },
      'ERR_COURSE_FULL': { status: 409, message: 'Course is full' },
      'ERR_CAPACITY_EXCEEDED': { status: 409, message: 'Capacity exceeded' },
      'ERR_ALREADY_ENROLLED': { status: 409, message: 'Already enrolled in this course' },
      'ERR_NOT_ENROLLED': { status: 409, message: 'Not enrolled in this course' },
      'ERR_MAX_COURSES_REACHED': { status: 409, message: 'Maximum courses per professor reached' },
      'ERR_CREDIT_LIMIT_EXCEEDED': { status: 409, message: 'Credit limit exceeded' },
      'ERR_TOO_MANY_DROPS': { status: 409, message: 'Too many drops for this term' },
      'ERR_ILLEGAL_COURSE_STATE_TRANSITION': { status: 409, message: 'Illegal course state transition' },
      'ERR_ILLEGAL_ENROLLMENT_STATE': { status: 409, message: 'Illegal enrollment state' },
      'ERR_FORBIDDEN': { status: 403, message: 'Forbidden' },
      'ERR_TERM_NAME_NOT_UNIQUE': { status: 409, message: 'Term name is not unique' },
      'ERR_INVALID_INSTRUCTOR': { status: 422, message: 'Invalid instructor' },
      'ERR_REV_CONFLICT': { status: 409, message: 'Revision conflict' },
      'ERR_INSUFFICIENT_FUNDS': { status: 402, message: 'Insufficient funds' },
      'ERR_OVERPAY_NOT_ALLOWED': { status: 422, message: 'Overpayment not allowed' },
      'ERR_INVALID_PAYMENT_AMOUNT': { status: 422, message: 'Invalid payment amount' },
      'ERR_LEDGER_INVALID_OP': { status: 422, message: 'Invalid ledger operation' }
    };

    if (errorMap[errorId]) {
      statusCode = errorMap[errorId].status;
      message = errorMap[errorId].message;
    }
  }

  // Handle validation errors
  if (err.isJoi) {
    errorId = 'ERR_INVALID_REQUEST';
    message = err.details[0].message;
    statusCode = 400;
  }

  // Send error response
  const response = {
    meta: {
      api_request_id: req.requestId || require('uuid').v4(),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };

  res.status(statusCode).json(response);
};

module.exports = errorHandler;