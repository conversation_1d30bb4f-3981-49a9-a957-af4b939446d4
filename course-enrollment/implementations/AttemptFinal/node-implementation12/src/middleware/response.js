const { v4: uuidv4 } = require('uuid');

const responseMiddleware = (req, res, next) => {
  // Generate unique request ID
  req.requestId = uuidv4();
  
  // Helper function to send standardized responses
  res.apiResponse = (data, responseType = 'object', statusCode = 200) => {
    const response = {
      meta: {
        api_request_id: req.requestId,
        api_request_timestamp: new Date().toISOString()
      },
      response_type: responseType,
      data: data || {}
    };
    
    res.status(statusCode).json(response);
  };

  // Helper function to send standardized error responses
  res.apiError = (errorId, message, statusCode = 400) => {
    const response = {
      meta: {
        api_request_id: req.requestId,
        api_request_timestamp: new Date().toISOString()
      },
      response_type: 'error',
      data: {
        error_id: errorId,
        message: message
      }
    };
    
    res.status(statusCode).json(response);
  };

  next();
};

module.exports = responseMiddleware;