const { Users, CONSTANTS, validateUUID } = require('../models');

const authMiddleware = (req, res, next) => {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  // Validate required headers
  if (!userId || !userRole) {
    return res.status(400).json({
      meta: {
        api_request_id: req.requestId || require('uuid').v4(),
        api_request_timestamp: new Date().toISOString()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
        message: 'Missing or invalid user context headers (X-User-ID and X-User-Role required)'
      }
    });
  }

  // Validate UUID format
  if (!validateUUID(userId)) {
    return res.status(400).json({
      meta: {
        api_request_id: req.requestId || require('uuid').v4(),
        api_request_timestamp: new Date().toISOString()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_INVALID_ID_FORMAT',
        message: 'Invalid user ID format'
      }
    });
  }

  // Validate role enum
  if (!Object.values(CONSTANTS.USER_ROLES).includes(userRole)) {
    return res.status(400).json({
      meta: {
        api_request_id: req.requestId || require('uuid').v4(),
        api_request_timestamp: new Date().toISOString()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_INVALID_ENUM_VALUE',
        message: 'Invalid user role value'
      }
    });
  }

  // For demo purposes, we'll check if user exists in our mock data
  const user = Users.findById(userId);
  if (!user || user.role !== userRole) {
    return res.status(400).json({
      meta: {
        api_request_id: req.requestId || require('uuid').v4(),
        api_request_timestamp: new Date().toISOString()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
        message: 'User not found or role mismatch'
      }
    });
  }

  // Attach user context to request
  req.user = {
    id: userId,
    role: userRole
  };

  next();
};

// Authorization helper functions
const authorize = {
  registrarOnly: (req, res, next) => {
    if (req.user.role !== CONSTANTS.USER_ROLES.REGISTRAR) {
      return res.status(403).json({
        meta: {
          api_request_id: req.requestId,
          api_request_timestamp: new Date().toISOString()
        },
        response_type: 'error',
        data: {
          error_id: 'ERR_UNAUTHORIZED_ROLE',
          message: 'This operation requires Registrar role'
        }
      });
    }
    next();
  },

  professorOrRegistrar: (req, res, next) => {
    if (![CONSTANTS.USER_ROLES.PROFESSOR, CONSTANTS.USER_ROLES.REGISTRAR].includes(req.user.role)) {
      return res.status(403).json({
        meta: {
          api_request_id: req.requestId,
          api_request_timestamp: new Date().toISOString()
        },
        response_type: 'error',
        data: {
          error_id: 'ERR_UNAUTHORIZED_ROLE',
          message: 'This operation requires Professor or Registrar role'
        }
      });
    }
    next();
  },

  studentOrRegistrar: (req, res, next) => {
    if (![CONSTANTS.USER_ROLES.STUDENT, CONSTANTS.USER_ROLES.REGISTRAR].includes(req.user.role)) {
      return res.status(403).json({
        meta: {
          api_request_id: req.requestId,
          api_request_timestamp: new Date().toISOString()
        },
        response_type: 'error',
        data: {
          error_id: 'ERR_UNAUTHORIZED_ROLE',
          message: 'This operation requires Student or Registrar role'
        }
      });
    }
    next();
  },

  anyRole: (req, res, next) => {
    // All authenticated users can access
    next();
  }
};

module.exports = authMiddleware;
module.exports.authorize = authorize;