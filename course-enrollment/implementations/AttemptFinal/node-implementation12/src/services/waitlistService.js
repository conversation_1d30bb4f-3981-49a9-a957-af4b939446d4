const { Courses, Enrollments, CourseSeatLedgers, StudentTuitionLedgers, CONSTANTS } = require('../models');

/**
 * Promotes the first student from waitlist to enrolled status when a seat becomes available
 * This function implements the automatic waitlist promotion guarantee
 */
function promoteFromWaitlist(courseId) {
  try {
    // Get course information
    const course = Courses.findById(courseId);
    if (!course) {
      console.error(`Course ${courseId} not found for waitlist promotion`);
      return;
    }

    // Check if there are available seats
    const seatLedger = CourseSeatLedgers.findByCourseId(courseId);
    if (!seatLedger || seatLedger.seats_available <= 0) {
      // No seats available, nothing to promote
      return;
    }

    // Get all waitlisted enrollments for this course, ordered by creation time (FIFO)
    const waitlistedEnrollments = Enrollments.findByCourseId(courseId)
      .filter(enrollment => enrollment.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED)
      .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    if (waitlistedEnrollments.length === 0) {
      // No one on waitlist to promote
      return;
    }

    // Get the first student in line (longest waiting)
    const nextEnrollment = waitlistedEnrollments[0];

    // Promote the student
    try {
      // Update enrollment state to ENROLLED
      const updatedEnrollment = Enrollments.update(nextEnrollment.id, {
        state: CONSTANTS.ENROLLMENT_STATES.ENROLLED
      });

      // Debit seat ledger (consume the available seat)
      CourseSeatLedgers.debit(courseId, 1);

      // Charge tuition to student's ledger
      const tuitionCost = course.credits * CONSTANTS.COST_PER_CREDIT;
      StudentTuitionLedgers.credit(nextEnrollment.student_id, course.term_id, tuitionCost);

      console.log(`Successfully promoted student ${nextEnrollment.student_id} from waitlist to enrolled in course ${courseId}`);

      // Check if there are more seats and more waitlisted students
      // If so, recursively promote more students
      const updatedSeatLedger = CourseSeatLedgers.findByCourseId(courseId);
      if (updatedSeatLedger && updatedSeatLedger.seats_available > 0) {
        // There might be more seats available, check again
        promoteFromWaitlist(courseId);
      }

    } catch (error) {
      console.error(`Error during waitlist promotion for course ${courseId}:`, error);
      
      // If seat debit failed, it means capacity was exceeded somehow
      // This shouldn't happen in normal flow, but we handle it gracefully
      if (error.message === 'ERR_CAPACITY_EXCEEDED') {
        console.error(`Capacity exceeded during promotion - this indicates a race condition or data inconsistency`);
      }
    }

  } catch (error) {
    console.error(`Error in promoteFromWaitlist for course ${courseId}:`, error);
  }
}

/**
 * Handles the seat_vacated event when a seat becomes available
 * This is the entry point for the automatic promotion system
 */
function handleSeatVacated(courseId) {
  // Use setImmediate to ensure this runs asynchronously
  setImmediate(() => {
    promoteFromWaitlist(courseId);
  });
}

/**
 * Validates waitlist invariants for a course
 * Used for debugging and ensuring data consistency
 */
function validateWaitlistInvariants(courseId) {
  const course = Courses.findById(courseId);
  if (!course) return false;

  const seatLedger = CourseSeatLedgers.findByCourseId(courseId);
  if (!seatLedger) return true; // Course not published yet

  const enrollments = Enrollments.findByCourseId(courseId);
  const enrolledCount = enrollments.filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED).length;
  const waitlistedCount = enrollments.filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED).length;

  // Invariant: available_seats + enrolled_count = capacity
  const expectedAvailableSeats = course.capacity - enrolledCount;
  if (seatLedger.seats_available !== expectedAvailableSeats) {
    console.error(`Seat ledger inconsistency for course ${courseId}: expected ${expectedAvailableSeats}, got ${seatLedger.seats_available}`);
    return false;
  }

  // Invariant: if there are available seats and waitlisted students, something is wrong
  if (seatLedger.seats_available > 0 && waitlistedCount > 0) {
    console.error(`Waitlist promotion invariant violated for course ${courseId}: ${seatLedger.seats_available} seats available but ${waitlistedCount} students waitlisted`);
    return false;
  }

  return true;
}

module.exports = {
  promoteFromWaitlist,
  handleSeatVacated,
  validateWaitlistInvariants
};