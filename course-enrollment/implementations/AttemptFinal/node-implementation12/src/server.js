const express = require('express');
const { v4: uuidv4 } = require('uuid');
const authMiddleware = require('./middleware/auth');
const responseMiddleware = require('./middleware/response');
const errorHandler = require('./middleware/errorHandler');

const termRoutes = require('./routes/terms');
const courseRoutes = require('./routes/courses');
const enrollmentRoutes = require('./routes/enrollments');
const paymentRoutes = require('./routes/payments');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(authMiddleware);
app.use(responseMiddleware);

// Routes
app.use('/terms', termRoutes);
app.use('/terms/:termId/courses', courseRoutes);
app.use('/terms/:termId/courses/:courseId/enrollments', enrollmentRoutes);
app.use('/terms/:termId/students', paymentRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// Error handling
app.use(errorHandler);

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    meta: {
      api_request_id: req.requestId,
      api_request_timestamp: new Date().toISOString()
    },
    response_type: 'error',
    data: {
      error_id: 'ERR_NOT_FOUND',
      message: 'Endpoint not found'
    }
  });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;