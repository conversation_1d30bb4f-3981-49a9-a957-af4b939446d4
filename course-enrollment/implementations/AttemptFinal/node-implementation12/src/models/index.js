// In-memory data storage
const data = {
  terms: new Map(),
  courses: new Map(),
  enrollments: new Map(),
  courseSeatLedgers: new Map(),
  studentTuitionLedgers: new Map(),
  users: new Map() // For demo purposes
};

// Constants
const CONSTANTS = {
  MAX_CREDITS_PER_TERM: 18,
  MAX_COURSES_PER_PROF: 5,
  MAX_DROP_COUNT_PER_TERM: 3,
  COST_PER_CREDIT: 10000, // $100 in cents
  DROP_PENALTY_FEE: 5000, // $50 in cents
  
  TERM_STATES: {
    PLANNING: 'PLANNING',
    ENROLLMENT_OPEN: 'ENROLLMENT_OPEN',
    ENROLLMENT_CLOSED: 'ENROLLMENT_CLOSED',
    CONCLUDED: 'CONCLUDED'
  },
  
  COURSE_STATES: {
    DRAFT: 'DRAFT',
    OPEN: 'OPEN',
    IN_PROGRESS: 'IN_PROGRESS',
    COMPLETED: 'COMPLETED',
    CANCELLED: 'CANCELLED'
  },
  
  ENROLLMENT_STATES: {
    ENROLLED: 'ENROLLED',
    WAITLISTED: 'WAITLISTED',
    DROPPED: 'DROPPED',
    COMPLETED: 'COMPLETED'
  },
  
  USER_ROLES: {
    STUDENT: 'STUDENT',
    PROFESSOR: 'PROFESSOR',
    REGISTRAR: 'REGISTRAR'
  },
  
  DELIVERY_MODES: {
    IN_PERSON: 'IN_PERSON',
    ONLINE: 'ONLINE',
    HYBRID: 'HYBRID'
  }
};

// Helper functions
function generateId() {
  const { v4: uuidv4 } = require('uuid');
  return uuidv4();
}

function validateUUID(id) {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidRegex.test(id);
}

function validateCourseCode(code) {
  const codeRegex = /^[A-Z]{2,4}\d{3}$/;
  return codeRegex.test(code);
}

// Data access functions
const Terms = {
  create(termData) {
    const term = {
      id: generateId(),
      name: termData.name,
      state: CONSTANTS.TERM_STATES.PLANNING,
      created_by: termData.created_by,
      created_at: new Date().toISOString(),
      revision: 0
    };
    data.terms.set(term.id, term);
    return term;
  },

  findById(id) {
    return data.terms.get(id);
  },

  findByName(name) {
    for (const term of data.terms.values()) {
      if (term.name === name) {
        return term;
      }
    }
    return null;
  },

  update(id, updates) {
    const term = data.terms.get(id);
    if (!term) return null;
    
    const updatedTerm = { ...term, ...updates, revision: term.revision + 1 };
    data.terms.set(id, updatedTerm);
    return updatedTerm;
  }
};

const Courses = {
  create(courseData) {
    const course = {
      id: generateId(),
      code: courseData.code,
      title: courseData.title,
      description: courseData.description || '',
      credits: courseData.credits,
      capacity: courseData.capacity,
      professor_id: courseData.professor_id,
      term_id: courseData.term_id,
      delivery_mode: courseData.delivery_mode,
      location: courseData.location || null,
      online_link: courseData.online_link || null,
      state: CONSTANTS.COURSE_STATES.DRAFT,
      created_at: new Date().toISOString(),
      revision: 0
    };
    data.courses.set(course.id, course);
    return course;
  },

  findById(id) {
    return data.courses.get(id);
  },

  findByTermId(termId) {
    return Array.from(data.courses.values()).filter(course => course.term_id === termId);
  },

  findByCodeAndTerm(code, termId) {
    return Array.from(data.courses.values()).find(course => 
      course.code === code && course.term_id === termId
    );
  },

  findByProfessorAndTerm(professorId, termId) {
    return Array.from(data.courses.values()).filter(course => 
      course.professor_id === professorId && course.term_id === termId
    );
  },

  update(id, updates) {
    const course = data.courses.get(id);
    if (!course) return null;
    
    const updatedCourse = { ...course, ...updates, revision: course.revision + 1 };
    data.courses.set(id, updatedCourse);
    return updatedCourse;
  }
};

const Enrollments = {
  create(enrollmentData) {
    const enrollment = {
      id: generateId(),
      course_id: enrollmentData.course_id,
      student_id: enrollmentData.student_id,
      state: enrollmentData.state,
      created_at: new Date().toISOString(),
      revision: 0
    };
    data.enrollments.set(enrollment.id, enrollment);
    return enrollment;
  },

  findById(id) {
    return data.enrollments.get(id);
  },

  findByCourseId(courseId) {
    return Array.from(data.enrollments.values()).filter(enrollment => 
      enrollment.course_id === courseId
    );
  },

  findByStudentAndCourse(studentId, courseId) {
    return Array.from(data.enrollments.values()).find(enrollment => 
      enrollment.student_id === studentId && enrollment.course_id === courseId
    );
  },

  findByStudentAndTerm(studentId, termId) {
    const termCourses = Courses.findByTermId(termId);
    const courseIds = termCourses.map(course => course.id);
    
    return Array.from(data.enrollments.values()).filter(enrollment => 
      enrollment.student_id === studentId && courseIds.includes(enrollment.course_id)
    );
  },

  update(id, updates) {
    const enrollment = data.enrollments.get(id);
    if (!enrollment) return null;
    
    const updatedEnrollment = { ...enrollment, ...updates, revision: enrollment.revision + 1 };
    data.enrollments.set(id, updatedEnrollment);
    return updatedEnrollment;
  }
};

const CourseSeatLedgers = {
  create(courseId, seatsAvailable) {
    const ledger = {
      course_id: courseId,
      seats_available: seatsAvailable
    };
    data.courseSeatLedgers.set(courseId, ledger);
    return ledger;
  },

  findByCourseId(courseId) {
    return data.courseSeatLedgers.get(courseId);
  },

  update(courseId, seatsAvailable) {
    const ledger = data.courseSeatLedgers.get(courseId);
    if (!ledger) return null;
    
    ledger.seats_available = seatsAvailable;
    data.courseSeatLedgers.set(courseId, ledger);
    return ledger;
  },

  debit(courseId, amount = 1) {
    const ledger = data.courseSeatLedgers.get(courseId);
    if (!ledger || ledger.seats_available < amount) {
      throw new Error('ERR_CAPACITY_EXCEEDED');
    }
    
    ledger.seats_available -= amount;
    return ledger;
  },

  credit(courseId, amount = 1) {
    const ledger = data.courseSeatLedgers.get(courseId);
    const course = Courses.findById(courseId);
    
    if (!ledger || !course) return null;
    
    if (ledger.seats_available + amount > course.capacity) {
      throw new Error('ERR_CAPACITY_EXCEEDED');
    }
    
    ledger.seats_available += amount;
    return ledger;
  }
};

const StudentTuitionLedgers = {
  create(studentId, termId, balance = 0) {
    const ledgerKey = `${studentId}_${termId}`;
    const ledger = {
      student_id: studentId,
      term_id: termId,
      balance_cents: balance
    };
    data.studentTuitionLedgers.set(ledgerKey, ledger);
    return ledger;
  },

  findByStudentAndTerm(studentId, termId) {
    const ledgerKey = `${studentId}_${termId}`;
    return data.studentTuitionLedgers.get(ledgerKey);
  },

  credit(studentId, termId, amount) {
    const ledgerKey = `${studentId}_${termId}`;
    let ledger = data.studentTuitionLedgers.get(ledgerKey);
    
    if (!ledger) {
      ledger = this.create(studentId, termId, 0);
    }
    
    ledger.balance_cents += amount;
    return ledger;
  },

  debit(studentId, termId, amount) {
    const ledgerKey = `${studentId}_${termId}`;
    const ledger = data.studentTuitionLedgers.get(ledgerKey);
    
    if (!ledger || ledger.balance_cents < amount) {
      throw new Error('ERR_OVERPAY_NOT_ALLOWED');
    }
    
    ledger.balance_cents -= amount;
    return ledger;
  }
};

// Mock user data for demo
const demoUsers = [
  { id: '550e8400-e29b-41d4-a716-446655440001', role: 'REGISTRAR' },
  { id: '550e8400-e29b-41d4-a716-446655440002', role: 'PROFESSOR' },
  { id: '550e8400-e29b-41d4-a716-446655440003', role: 'PROFESSOR' },
  { id: '550e8400-e29b-41d4-a716-************', role: 'STUDENT' },
  { id: '550e8400-e29b-41d4-a716-************', role: 'STUDENT' }
];

demoUsers.forEach(user => {
  data.users.set(user.id, user);
});

const Users = {
  findById(id) {
    return data.users.get(id);
  },

  findByRole(role) {
    return Array.from(data.users.values()).filter(user => user.role === role);
  }
};

module.exports = {
  data,
  CONSTANTS,
  Terms,
  Courses,
  Enrollments,
  CourseSeatLedgers,
  StudentTuitionLedgers,
  Users,
  generateId,
  validateUUID,
  validateCourseCode
};