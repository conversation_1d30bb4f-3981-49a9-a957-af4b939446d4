const express = require('express');
const Joi = require('joi');
const { authorize } = require('../middleware/auth');
const { Terms, Courses, Enrollments, CourseSeatLedgers, StudentTuitionLedgers, Users, CONSTANTS, validateUUID, validateCourseCode } = require('../models');

const router = express.Router({ mergeParams: true });

// Validation schemas
const createCourseSchema = Joi.object({
  code: Joi.string().required(),
  title: Joi.string().min(1).max(100).required(),
  description: Joi.string().max(1000).optional(),
  credits: Joi.number().integer().min(1).max(5).required(),
  capacity: Joi.number().integer().min(1).max(500).required(),
  professor_id: Joi.string().optional(),
  delivery_mode: Joi.string().valid('IN_PERSON', 'ONLINE', 'HYBRID').required(),
  location: Joi.string().optional(),
  online_link: Joi.string().uri().optional()
});

const revisionSchema = Joi.object({
  revision: Joi.number().integer().min(0).required()
});

// Helper function to validate delivery mode requirements
function validateDeliveryModeFields(deliveryMode, location, onlineLink) {
  switch (deliveryMode) {
    case CONSTANTS.DELIVERY_MODES.IN_PERSON:
      if (!location) {
        throw new Error('ERR_CONDITIONAL_FIELD_REQUIRED');
      }
      if (onlineLink) {
        throw new Error('ERR_FIELD_CONFLICT');
      }
      break;
    case CONSTANTS.DELIVERY_MODES.ONLINE:
      if (!onlineLink) {
        throw new Error('ERR_CONDITIONAL_FIELD_REQUIRED');
      }
      if (location) {
        throw new Error('ERR_FIELD_CONFLICT');
      }
      break;
    case CONSTANTS.DELIVERY_MODES.HYBRID:
      if (!location && !onlineLink) {
        throw new Error('ERR_CONDITIONAL_FIELD_REQUIRED');
      }
      break;
  }
}

// Helper function to filter course data based on user role
function filterCourseData(course, userRole, userId, includeEnrollments = false) {
  const baseData = {
    id: course.id,
    code: course.code,
    title: course.title,
    description: course.description,
    credits: course.credits,
    capacity: course.capacity,
    professor_id: course.professor_id,
    delivery_mode: course.delivery_mode,
    location: course.location,
    online_link: course.online_link,
    state: course.state,
    created_at: course.created_at,
    revision: course.revision
  };

  // Add derived fields based on role
  const enrollments = Enrollments.findByCourseId(course.id);
  const enrolledCount = enrollments.filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED).length;
  const waitlistCount = enrollments.filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED).length;
  const seatLedger = CourseSeatLedgers.findByCourseId(course.id);
  const availableSeats = seatLedger ? seatLedger.seats_available : course.capacity;

  if (userRole === CONSTANTS.USER_ROLES.STUDENT) {
    // Students see limited information
    baseData.available_seats = availableSeats;
    
    // Check if this student is enrolled or waitlisted
    const studentEnrollment = enrollments.find(e => e.student_id === userId);
    if (studentEnrollment) {
      baseData.is_enrolled = studentEnrollment.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED;
      baseData.is_waitlisted = studentEnrollment.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED;
    }
  } else if (userRole === CONSTANTS.USER_ROLES.PROFESSOR && course.professor_id === userId) {
    // Professor sees full details for their own courses
    baseData.enrolled_count = enrolledCount;
    baseData.waitlist_count = waitlistCount;
    baseData.available_seats = availableSeats;
    
    if (includeEnrollments) {
      baseData.enrollments = enrollments;
    }
  } else if (userRole === CONSTANTS.USER_ROLES.REGISTRAR) {
    // Registrar sees everything
    baseData.enrolled_count = enrolledCount;
    baseData.waitlist_count = waitlistCount;
    baseData.available_seats = availableSeats;
    
    if (includeEnrollments) {
      baseData.enrollments = enrollments;
    }
  } else {
    // Other professors see basic info for published courses
    if (course.state !== CONSTANTS.COURSE_STATES.DRAFT && course.state !== CONSTANTS.COURSE_STATES.CANCELLED) {
      baseData.available_seats = availableSeats;
    }
  }

  return baseData;
}

// POST /terms/:termId/courses - Create a new course
router.post('/', authorize.professorOrRegistrar, async (req, res, next) => {
  try {
    const { termId } = req.params;

    // Validate UUID format
    if (!validateUUID(termId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid term ID format', 400);
    }

    // Validate request body
    const { error, value } = createCourseSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    const { code, title, description, credits, capacity, professor_id, delivery_mode, location, online_link } = value;

    // Validate course code format
    if (!validateCourseCode(code)) {
      return res.apiError('ERR_INVALID_COURSE_CODE', 'Invalid course code format', 400);
    }

    // Validate delivery mode fields
    try {
      validateDeliveryModeFields(delivery_mode, location, online_link);
    } catch (err) {
      return res.apiError(err.message, 'Invalid delivery mode configuration', 400);
    }

    // Check if term exists
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Check if course code is unique in this term
    const existingCourse = Courses.findByCodeAndTerm(code, termId);
    if (existingCourse) {
      return res.apiError('ERR_COURSE_CODE_NOT_UNIQUE', 'Course code already exists in this term', 409);
    }

    // Determine professor ID
    let finalProfessorId;
    if (req.user.role === CONSTANTS.USER_ROLES.REGISTRAR) {
      if (!professor_id) {
        return res.apiError('ERR_MISSING_REQUIRED_FIELD', 'professor_id is required for Registrar', 400);
      }
      
      // Validate professor exists and has correct role
      const professor = Users.findById(professor_id);
      if (!professor || professor.role !== CONSTANTS.USER_ROLES.PROFESSOR) {
        return res.apiError('ERR_INVALID_INSTRUCTOR', 'Invalid professor ID', 422);
      }
      
      finalProfessorId = professor_id;
    } else {
      // Professor creating course for themselves
      if (professor_id && professor_id !== req.user.id) {
        return res.apiError('ERR_FIELD_CONFLICT', 'Professor cannot create course for another professor', 400);
      }
      finalProfessorId = req.user.id;
    }

    // Check professor course limit
    const professorCourses = Courses.findByProfessorAndTerm(finalProfessorId, termId);
    if (professorCourses.length >= CONSTANTS.MAX_COURSES_PER_PROF) {
      return res.apiError('ERR_MAX_COURSES_REACHED', 'Maximum courses per professor reached', 409);
    }

    // Create course
    const course = Courses.create({
      code,
      title,
      description,
      credits,
      capacity,
      professor_id: finalProfessorId,
      term_id: termId,
      delivery_mode,
      location,
      online_link
    });

    res.apiResponse(course, 'object', 201);
  } catch (err) {
    next(err);
  }
});

// GET /terms/:termId/courses - List courses in the term
router.get('/', authorize.anyRole, async (req, res, next) => {
  try {
    const { termId } = req.params;
    const { state, professor_id, limit = 50, offset = 0 } = req.query;

    // Validate UUID format
    if (!validateUUID(termId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid term ID format', 400);
    }

    // Check if term exists
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Get courses for the term
    let courses = Courses.findByTermId(termId);

    // Apply filters
    if (state) {
      if (!Object.values(CONSTANTS.COURSE_STATES).includes(state)) {
        return res.apiError('ERR_INVALID_ENUM_VALUE', 'Invalid state value', 400);
      }
      courses = courses.filter(course => course.state === state);
    }

    if (professor_id) {
      if (!validateUUID(professor_id)) {
        return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid professor ID format', 400);
      }
      courses = courses.filter(course => course.professor_id === professor_id);
    }

    // Filter based on user role and permissions
    courses = courses.filter(course => {
      if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
        // Students only see published courses
        return [CONSTANTS.COURSE_STATES.OPEN, CONSTANTS.COURSE_STATES.IN_PROGRESS, CONSTANTS.COURSE_STATES.COMPLETED].includes(course.state);
      } else if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR) {
        // Professors see their own courses (any state) and other published courses
        return course.professor_id === req.user.id || 
               [CONSTANTS.COURSE_STATES.OPEN, CONSTANTS.COURSE_STATES.IN_PROGRESS, CONSTANTS.COURSE_STATES.COMPLETED].includes(course.state);
      } else {
        // Registrar sees all courses
        return true;
      }
    });

    // Apply pagination
    const total = courses.length;
    const startIndex = parseInt(offset);
    const endIndex = startIndex + parseInt(limit);
    const paginatedCourses = courses.slice(startIndex, endIndex);

    // Filter data based on user role
    const filteredCourses = paginatedCourses.map(course => 
      filterCourseData(course, req.user.role, req.user.id)
    );

    const response = {
      courses: filteredCourses,
      total,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    res.apiResponse(response, 'object');
  } catch (err) {
    next(err);
  }
});

// GET /terms/:termId/courses/:courseId - Get detailed info on a specific course
router.get('/:courseId', authorize.anyRole, async (req, res, next) => {
  try {
    const { termId, courseId } = req.params;

    // Validate UUID formats
    if (!validateUUID(termId) || !validateUUID(courseId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid ID format', 400);
    }

    // Check if term exists
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Find course
    const course = Courses.findById(courseId);
    if (!course || course.term_id !== termId) {
      return res.apiError('ERR_COURSE_NOT_FOUND', 'Course not found', 404);
    }

    // Check visibility based on role
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      // Students can only see published courses or courses they're enrolled in
      if ([CONSTANTS.COURSE_STATES.DRAFT, CONSTANTS.COURSE_STATES.CANCELLED].includes(course.state)) {
        const studentEnrollment = Enrollments.findByStudentAndCourse(req.user.id, courseId);
        if (!studentEnrollment) {
          return res.apiError('ERR_COURSE_NOT_FOUND', 'Course not found', 404);
        }
      }
    } else if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR) {
      // Professors can see their own courses or published courses
      if (course.professor_id !== req.user.id && 
          [CONSTANTS.COURSE_STATES.DRAFT, CONSTANTS.COURSE_STATES.CANCELLED].includes(course.state)) {
        return res.apiError('ERR_COURSE_NOT_FOUND', 'Course not found', 404);
      }
    }

    // Filter and include enrollments for authorized users
    const includeEnrollments = req.user.role === CONSTANTS.USER_ROLES.REGISTRAR || 
                              (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR && course.professor_id === req.user.id);
    
    const filteredCourse = filterCourseData(course, req.user.role, req.user.id, includeEnrollments);

    res.apiResponse(filteredCourse, 'object');
  } catch (err) {
    next(err);
  }
});

// PATCH /terms/:termId/courses/:courseId:publish - Publish a draft course
router.patch('/:courseId\\:publish', authorize.professorOrRegistrar, async (req, res, next) => {
  try {
    const { termId, courseId } = req.params;

    // Validate UUID formats
    if (!validateUUID(termId) || !validateUUID(courseId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid ID format', 400);
    }

    // Validate request body
    const { error, value } = revisionSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    const { revision } = value;

    // Check if term exists and is open for enrollment
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    if (term.state !== CONSTANTS.TERM_STATES.ENROLLMENT_OPEN) {
      return res.apiError('ERR_TERM_NOT_ACTIVE', 'Term must be open for enrollment to publish courses', 409);
    }

    // Find course
    const course = Courses.findById(courseId);
    if (!course || course.term_id !== termId) {
      return res.apiError('ERR_COURSE_NOT_FOUND', 'Course not found', 404);
    }

    // Check authorization
    if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.apiError('ERR_NOT_INSTRUCTOR', 'Not the course instructor', 403);
    }

    // Check revision for optimistic locking
    if (course.revision !== revision) {
      return res.apiError('ERR_REV_CONFLICT', 'Revision conflict', 409);
    }

    // Check current state
    if (course.state !== CONSTANTS.COURSE_STATES.DRAFT) {
      return res.apiError('ERR_COURSE_WRONG_STATE', 'Course must be in DRAFT state to publish', 409);
    }

    // Update course state and initialize seat ledger
    const updatedCourse = Courses.update(courseId, {
      state: CONSTANTS.COURSE_STATES.OPEN
    });

    // Initialize seat ledger
    CourseSeatLedgers.create(courseId, course.capacity);

    res.apiResponse(updatedCourse, 'object');
  } catch (err) {
    next(err);
  }
});

// PATCH /terms/:termId/courses/:courseId:cancel - Cancel a course
router.patch('/:courseId\\:cancel', authorize.professorOrRegistrar, async (req, res, next) => {
  try {
    const { termId, courseId } = req.params;

    // Validate UUID formats
    if (!validateUUID(termId) || !validateUUID(courseId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid ID format', 400);
    }

    // Validate request body
    const { error, value } = revisionSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    const { revision } = value;

    // Check if term exists
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Find course
    const course = Courses.findById(courseId);
    if (!course || course.term_id !== termId) {
      return res.apiError('ERR_COURSE_NOT_FOUND', 'Course not found', 404);
    }

    // Check authorization
    if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.apiError('ERR_NOT_INSTRUCTOR', 'Not the course instructor', 403);
    }

    // Check revision for optimistic locking
    if (course.revision !== revision) {
      return res.apiError('ERR_REV_CONFLICT', 'Revision conflict', 409);
    }

    // Check current state
    if (![CONSTANTS.COURSE_STATES.DRAFT, CONSTANTS.COURSE_STATES.OPEN, CONSTANTS.COURSE_STATES.IN_PROGRESS].includes(course.state)) {
      return res.apiError('ERR_COURSE_WRONG_STATE', 'Course cannot be cancelled in current state', 409);
    }

    // Update course state
    const updatedCourse = Courses.update(courseId, {
      state: CONSTANTS.COURSE_STATES.CANCELLED
    });

    // Drop all enrollments and handle refunds
    const enrollments = Enrollments.findByCourseId(courseId);
    enrollments.forEach(enrollment => {
      if ([CONSTANTS.ENROLLMENT_STATES.ENROLLED, CONSTANTS.ENROLLMENT_STATES.WAITLISTED].includes(enrollment.state)) {
        // Mark as dropped
        Enrollments.update(enrollment.id, {
          state: CONSTANTS.ENROLLMENT_STATES.DROPPED
        });

        // If student was enrolled (had a seat), refund tuition
        if (enrollment.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED) {
          const refundAmount = course.credits * CONSTANTS.COST_PER_CREDIT;
          try {
            StudentTuitionLedgers.debit(enrollment.student_id, termId, refundAmount);
          } catch (err) {
            // Student may not have had any charges yet
          }
        }
      }
    });

    res.apiResponse(updatedCourse, 'object');
  } catch (err) {
    next(err);
  }
});

module.exports = router;