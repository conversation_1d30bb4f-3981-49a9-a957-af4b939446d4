const express = require('express');
const Joi = require('joi');
const { authorize } = require('../middleware/auth');
const { Terms, Courses, Enrollments, CourseSeatLedgers, StudentTuitionLedgers, Users, CONSTANTS, validateUUID } = require('../models');
const { promoteFromWaitlist } = require('../services/waitlistService');

const router = express.Router({ mergeParams: true });

// Validation schemas
const createEnrollmentSchema = Joi.object({
  student_id: Joi.string().optional()
});

const revisionSchema = Joi.object({
  revision: Joi.number().integer().min(0).required()
});

// Helper function to count student's drops in term
function countStudentDropsInTerm(studentId, termId) {
  const studentEnrollments = Enrollments.findByStudentAndTerm(studentId, termId);
  return studentEnrollments.filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.DROPPED).length;
}

// Helper function to calculate student's enrolled credits in term
function calculateStudentCredits(studentId, termId) {
  const studentEnrollments = Enrollments.findByStudentAndTerm(studentId, termId);
  let totalCredits = 0;
  
  studentEnrollments.forEach(enrollment => {
    if (enrollment.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED) {
      const course = Courses.findById(enrollment.course_id);
      if (course) {
        totalCredits += course.credits;
      }
    }
  });
  
  return totalCredits;
}

// POST /terms/:termId/courses/:courseId/enrollments - Enroll in a course
router.post('/', authorize.studentOrRegistrar, async (req, res, next) => {
  try {
    const { termId, courseId } = req.params;

    // Validate UUID formats
    if (!validateUUID(termId) || !validateUUID(courseId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid ID format', 400);
    }

    // Validate request body
    const { error, value } = createEnrollmentSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    const { student_id } = value;

    // Determine target student ID
    let targetStudentId;
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      // Students can only enroll themselves
      if (student_id && student_id !== req.user.id) {
        return res.apiError('ERR_FORBIDDEN', 'Students can only enroll themselves', 403);
      }
      targetStudentId = req.user.id;
    } else if (req.user.role === CONSTANTS.USER_ROLES.REGISTRAR) {
      // Registrar must specify student_id
      if (!student_id) {
        return res.apiError('ERR_MISSING_REQUIRED_FIELD', 'student_id is required for Registrar', 400);
      }
      
      // Validate student exists
      const student = Users.findById(student_id);
      if (!student || student.role !== CONSTANTS.USER_ROLES.STUDENT) {
        return res.apiError('ERR_STUDENT_NOT_FOUND', 'Student not found', 404);
      }
      
      targetStudentId = student_id;
    }

    // Check if term exists and is open for enrollment
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    if (term.state !== CONSTANTS.TERM_STATES.ENROLLMENT_OPEN) {
      return res.apiError('ERR_REGISTRATION_CLOSED', 'Registration is not open', 409);
    }

    // Find course
    const course = Courses.findById(courseId);
    if (!course || course.term_id !== termId) {
      return res.apiError('ERR_COURSE_NOT_FOUND', 'Course not found', 404);
    }

    // Check course state
    if (course.state !== CONSTANTS.COURSE_STATES.OPEN) {
      return res.apiError('ERR_COURSE_WRONG_STATE', 'Course is not open for enrollment', 409);
    }

    // Check for duplicate enrollment
    const existingEnrollment = Enrollments.findByStudentAndCourse(targetStudentId, courseId);
    if (existingEnrollment && existingEnrollment.state !== CONSTANTS.ENROLLMENT_STATES.DROPPED) {
      return res.apiError('ERR_ALREADY_ENROLLED', 'Already enrolled in this course', 409);
    }

    // Check credit limit (only for students, registrar can override)
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      const currentCredits = calculateStudentCredits(targetStudentId, termId);
      if (currentCredits + course.credits > CONSTANTS.MAX_CREDITS_PER_TERM) {
        return res.apiError('ERR_CREDIT_LIMIT_EXCEEDED', 'Credit limit exceeded', 409);
      }
    }

    // Check seat availability
    const seatLedger = CourseSeatLedgers.findByCourseId(courseId);
    const hasAvailableSeats = seatLedger && seatLedger.seats_available > 0;

    // Determine enrollment state
    const enrollmentState = hasAvailableSeats ? 
      CONSTANTS.ENROLLMENT_STATES.ENROLLED : 
      CONSTANTS.ENROLLMENT_STATES.WAITLISTED;

    // Create enrollment
    const enrollment = Enrollments.create({
      course_id: courseId,
      student_id: targetStudentId,
      state: enrollmentState
    });

    // Handle seat and financial ledgers for enrolled students
    if (enrollmentState === CONSTANTS.ENROLLMENT_STATES.ENROLLED) {
      // Debit seat ledger
      try {
        CourseSeatLedgers.debit(courseId, 1);
      } catch (err) {
        return res.apiError('ERR_CAPACITY_EXCEEDED', 'Course capacity exceeded', 409);
      }

      // Credit student tuition ledger
      const tuitionCost = course.credits * CONSTANTS.COST_PER_CREDIT;
      StudentTuitionLedgers.credit(targetStudentId, termId, tuitionCost);
    }

    res.apiResponse(enrollment, 'object', 201);
  } catch (err) {
    next(err);
  }
});

// GET /terms/:termId/courses/:courseId/enrollments - List enrollments for a course
router.get('/', authorize.professorOrRegistrar, async (req, res, next) => {
  try {
    const { termId, courseId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    // Validate UUID formats
    if (!validateUUID(termId) || !validateUUID(courseId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid ID format', 400);
    }

    // Check if term exists
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Find course
    const course = Courses.findById(courseId);
    if (!course || course.term_id !== termId) {
      return res.apiError('ERR_COURSE_NOT_FOUND', 'Course not found', 404);
    }

    // Check authorization
    if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.apiError('ERR_NOT_INSTRUCTOR', 'Not the course instructor', 403);
    }

    // Get enrollments
    let enrollments = Enrollments.findByCourseId(courseId);

    // Sort by creation date (FIFO for waitlist)
    enrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Apply pagination
    const total = enrollments.length;
    const startIndex = parseInt(offset);
    const endIndex = startIndex + parseInt(limit);
    const paginatedEnrollments = enrollments.slice(startIndex, endIndex);

    const response = {
      enrollments: paginatedEnrollments,
      total,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    res.apiResponse(response, 'object');
  } catch (err) {
    next(err);
  }
});

// GET /terms/:termId/courses/:courseId/enrollments/:enrollmentId - Get a specific enrollment
router.get('/:enrollmentId', authorize.anyRole, async (req, res, next) => {
  try {
    const { termId, courseId, enrollmentId } = req.params;

    // Validate UUID formats
    if (!validateUUID(termId) || !validateUUID(courseId) || !validateUUID(enrollmentId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid ID format', 400);
    }

    // Check if term exists
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Find course
    const course = Courses.findById(courseId);
    if (!course || course.term_id !== termId) {
      return res.apiError('ERR_COURSE_NOT_FOUND', 'Course not found', 404);
    }

    // Find enrollment
    const enrollment = Enrollments.findById(enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.apiError('ERR_ENROLLMENT_NOT_FOUND', 'Enrollment not found', 404);
    }

    // Check authorization
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      // Students can only see their own enrollments
      if (enrollment.student_id !== req.user.id) {
        return res.apiError('ERR_FORBIDDEN', 'Cannot access other students\' enrollments', 403);
      }
    } else if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR) {
      // Professors can only see enrollments for their courses
      if (course.professor_id !== req.user.id) {
        return res.apiError('ERR_NOT_INSTRUCTOR', 'Not the course instructor', 403);
      }
    }
    // Registrar can see any enrollment

    res.apiResponse(enrollment, 'object');
  } catch (err) {
    next(err);
  }
});

// PATCH /terms/:termId/courses/:courseId/enrollments/:enrollmentId:drop - Drop an enrollment
router.patch('/:enrollmentId\\:drop', authorize.anyRole, async (req, res, next) => {
  try {
    const { termId, courseId, enrollmentId } = req.params;

    // Validate UUID formats
    if (!validateUUID(termId) || !validateUUID(courseId) || !validateUUID(enrollmentId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid ID format', 400);
    }

    // Validate request body
    const { error, value } = revisionSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    const { revision } = value;

    // Check if term exists
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Check term state (cannot drop from concluded terms)
    if (term.state === CONSTANTS.TERM_STATES.CONCLUDED) {
      return res.apiError('ERR_TERM_NOT_ACTIVE', 'Cannot drop from concluded term', 409);
    }

    // Find course
    const course = Courses.findById(courseId);
    if (!course || course.term_id !== termId) {
      return res.apiError('ERR_COURSE_NOT_FOUND', 'Course not found', 404);
    }

    // Find enrollment
    const enrollment = Enrollments.findById(enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.apiError('ERR_ENROLLMENT_NOT_FOUND', 'Enrollment not found', 404);
    }

    // Check revision for optimistic locking
    if (enrollment.revision !== revision) {
      return res.apiError('ERR_REV_CONFLICT', 'Revision conflict', 409);
    }

    // Check authorization
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      // Students can only drop their own enrollments
      if (enrollment.student_id !== req.user.id) {
        return res.apiError('ERR_FORBIDDEN', 'Cannot drop other students\' enrollments', 403);
      }
    } else if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR) {
      // Professors can drop enrollments from their courses
      if (course.professor_id !== req.user.id) {
        return res.apiError('ERR_NOT_INSTRUCTOR', 'Not the course instructor', 403);
      }
    }
    // Registrar can drop any enrollment

    // Check enrollment state
    if (![CONSTANTS.ENROLLMENT_STATES.ENROLLED, CONSTANTS.ENROLLMENT_STATES.WAITLISTED].includes(enrollment.state)) {
      return res.apiError('ERR_ENROLLMENT_WRONG_STATE', 'Enrollment cannot be dropped in current state', 409);
    }

    // Check drop limit for students (only if student is initiating the drop)
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      const currentDropCount = countStudentDropsInTerm(enrollment.student_id, termId);
      if (currentDropCount >= CONSTANTS.MAX_DROP_COUNT_PER_TERM) {
        return res.apiError('ERR_TOO_MANY_DROPS', 'Maximum drops per term exceeded', 409);
      }
    }

    const wasEnrolled = enrollment.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED;

    // Update enrollment state
    const updatedEnrollment = Enrollments.update(enrollmentId, {
      state: CONSTANTS.ENROLLMENT_STATES.DROPPED
    });

    // Handle ledger updates if student was enrolled
    if (wasEnrolled) {
      // Credit seat ledger (free up seat)
      try {
        CourseSeatLedgers.credit(courseId, 1);
      } catch (err) {
        // Shouldn't happen but handle gracefully
      }

      // Debit student tuition ledger (refund)
      const refundAmount = course.credits * CONSTANTS.COST_PER_CREDIT;
      try {
        StudentTuitionLedgers.debit(enrollment.student_id, termId, refundAmount);
      } catch (err) {
        // Student may not have had charges
      }

      // Trigger waitlist promotion asynchronously
      setImmediate(() => {
        promoteFromWaitlist(courseId);
      });
    }

    // Apply drop penalty if this is student's third drop
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      const newDropCount = countStudentDropsInTerm(enrollment.student_id, termId);
      if (newDropCount === CONSTANTS.MAX_DROP_COUNT_PER_TERM) {
        // Apply penalty fee
        StudentTuitionLedgers.credit(enrollment.student_id, termId, CONSTANTS.DROP_PENALTY_FEE);
      }
    }

    res.apiResponse(updatedEnrollment, 'object');
  } catch (err) {
    next(err);
  }
});

module.exports = router;