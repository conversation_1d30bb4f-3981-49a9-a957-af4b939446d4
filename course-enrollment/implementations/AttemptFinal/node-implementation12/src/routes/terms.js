const express = require('express');
const Joi = require('joi');
const { authorize } = require('../middleware/auth');
const { Terms, Courses, Enrollments, CONSTANTS, validateUUID } = require('../models');

const router = express.Router();

// Validation schemas
const createTermSchema = Joi.object({
  name: Joi.string().min(1).max(100).required()
});

const revisionSchema = Joi.object({
  revision: Joi.number().integer().min(0).required()
});

// POST /terms - Create a new academic term
router.post('/', authorize.registrarOnly, async (req, res, next) => {
  try {
    // Validate request body
    const { error, value } = createTermSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    const { name } = value;

    // Check if term name already exists
    const existingTerm = Terms.findByName(name);
    if (existingTerm) {
      return res.apiError('ERR_TERM_NAME_NOT_UNIQUE', 'Term name already exists', 409);
    }

    // Create new term
    const term = Terms.create({
      name: name,
      created_by: req.user.id
    });

    res.apiResponse(term, 'object', 201);
  } catch (err) {
    next(err);
  }
});

// GET /terms/:termId - Retrieve details of an academic term
router.get('/:termId', authorize.anyRole, async (req, res, next) => {
  try {
    const { termId } = req.params;

    // Validate UUID format
    if (!validateUUID(termId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid term ID format', 400);
    }

    // Find term
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    res.apiResponse(term, 'object');
  } catch (err) {
    next(err);
  }
});

// PATCH /terms/:termId:open-registration - Open student registration
router.patch('/:termId\\:open-registration', authorize.registrarOnly, async (req, res, next) => {
  try {
    const { termId } = req.params;

    // Validate UUID format
    if (!validateUUID(termId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid term ID format', 400);
    }

    // Validate request body
    const { error, value } = revisionSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    const { revision } = value;

    // Find term
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Check revision for optimistic locking
    if (term.revision !== revision) {
      return res.apiError('ERR_REV_CONFLICT', 'Revision conflict', 409);
    }

    // Check current state
    if (term.state !== CONSTANTS.TERM_STATES.PLANNING) {
      return res.apiError('ERR_TERM_NOT_ACTIVE', 'Term must be in PLANNING state to open registration', 409);
    }

    // Update term state
    const updatedTerm = Terms.update(termId, {
      state: CONSTANTS.TERM_STATES.ENROLLMENT_OPEN
    });

    res.apiResponse(updatedTerm, 'object');
  } catch (err) {
    next(err);
  }
});

// PATCH /terms/:termId:close-registration - Close enrollment period
router.patch('/:termId\\:close-registration', authorize.registrarOnly, async (req, res, next) => {
  try {
    const { termId } = req.params;

    // Validate UUID format
    if (!validateUUID(termId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid term ID format', 400);
    }

    // Validate request body
    const { error, value } = revisionSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    const { revision } = value;

    // Find term
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Check revision for optimistic locking
    if (term.revision !== revision) {
      return res.apiError('ERR_REV_CONFLICT', 'Revision conflict', 409);
    }

    // Check current state
    if (term.state !== CONSTANTS.TERM_STATES.ENROLLMENT_OPEN) {
      return res.apiError('ERR_TERM_NOT_ACTIVE', 'Term must be in ENROLLMENT_OPEN state to close registration', 409);
    }

    // Update term state
    const updatedTerm = Terms.update(termId, {
      state: CONSTANTS.TERM_STATES.ENROLLMENT_CLOSED
    });

    // Transition all OPEN courses to IN_PROGRESS
    const termCourses = Courses.findByTermId(termId);
    termCourses.forEach(course => {
      if (course.state === CONSTANTS.COURSE_STATES.OPEN) {
        Courses.update(course.id, {
          state: CONSTANTS.COURSE_STATES.IN_PROGRESS
        });
      }
    });

    res.apiResponse(updatedTerm, 'object');
  } catch (err) {
    next(err);
  }
});

// PATCH /terms/:termId:conclude - Conclude the term
router.patch('/:termId\\:conclude', authorize.registrarOnly, async (req, res, next) => {
  try {
    const { termId } = req.params;

    // Validate UUID format
    if (!validateUUID(termId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid term ID format', 400);
    }

    // Validate request body
    const { error, value } = revisionSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_REQUEST', error.details[0].message, 400);
    }

    const { revision } = value;

    // Find term
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Check revision for optimistic locking
    if (term.revision !== revision) {
      return res.apiError('ERR_REV_CONFLICT', 'Revision conflict', 409);
    }

    // Check current state
    if (term.state !== CONSTANTS.TERM_STATES.ENROLLMENT_CLOSED) {
      return res.apiError('ERR_TERM_NOT_ACTIVE', 'Term must be in ENROLLMENT_CLOSED state to conclude', 409);
    }

    // Update term state
    const updatedTerm = Terms.update(termId, {
      state: CONSTANTS.TERM_STATES.CONCLUDED
    });

    // Transition all IN_PROGRESS courses to COMPLETED
    const termCourses = Courses.findByTermId(termId);
    termCourses.forEach(course => {
      if (course.state === CONSTANTS.COURSE_STATES.IN_PROGRESS) {
        Courses.update(course.id, {
          state: CONSTANTS.COURSE_STATES.COMPLETED
        });
      }
    });

    // Finalize all enrollments
    termCourses.forEach(course => {
      const courseEnrollments = Enrollments.findByCourseId(course.id);
      courseEnrollments.forEach(enrollment => {
        if (enrollment.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED) {
          Enrollments.update(enrollment.id, {
            state: CONSTANTS.ENROLLMENT_STATES.COMPLETED
          });
        } else if (enrollment.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED) {
          Enrollments.update(enrollment.id, {
            state: CONSTANTS.ENROLLMENT_STATES.DROPPED
          });
        }
      });
    });

    res.apiResponse(updatedTerm, 'object');
  } catch (err) {
    next(err);
  }
});

module.exports = router;