const express = require('express');
const Joi = require('joi');
const { authorize } = require('../middleware/auth');
const { Terms, StudentTuitionLedgers, Users, CONSTANTS, validateUUID } = require('../models');

const router = express.Router({ mergeParams: true });

// Validation schemas
const paymentSchema = Joi.object({
  amount: Joi.number().integer().min(1).required()
});

// POST /terms/:termId/students/:studentId:pay - Record a tuition payment
router.post('/:studentId\\:pay', authorize.studentOrRegistrar, async (req, res, next) => {
  try {
    const { termId, studentId } = req.params;

    // Validate UUID formats
    if (!validateUUID(termId) || !validateUUID(studentId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid ID format', 400);
    }

    // Validate request body
    const { error, value } = paymentSchema.validate(req.body);
    if (error) {
      return res.apiError('ERR_INVALID_PAYMENT_AMOUNT', error.details[0].message, 422);
    }

    const { amount } = value;

    // Check authorization
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      // Students can only pay for themselves
      if (studentId !== req.user.id) {
        return res.apiError('ERR_FORBIDDEN', 'Students can only pay their own tuition', 403);
      }
    } else if (req.user.role === CONSTANTS.USER_ROLES.REGISTRAR) {
      // Registrar can pay for any student, but student must exist
      const student = Users.findById(studentId);
      if (!student || student.role !== CONSTANTS.USER_ROLES.STUDENT) {
        return res.apiError('ERR_STUDENT_NOT_FOUND', 'Student not found', 404);
      }
    }

    // Check if term exists
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Get or create student tuition ledger
    let ledger = StudentTuitionLedgers.findByStudentAndTerm(studentId, termId);
    if (!ledger) {
      ledger = StudentTuitionLedgers.create(studentId, termId, 0);
    }

    // Check for overpayment
    if (amount > ledger.balance_cents) {
      return res.apiError('ERR_OVERPAY_NOT_ALLOWED', 'Payment amount exceeds outstanding balance', 422);
    }

    // Process payment (debit the ledger)
    try {
      const updatedLedger = StudentTuitionLedgers.debit(studentId, termId, amount);
      
      // Return payment confirmation with updated balance
      const response = {
        student_id: studentId,
        term_id: termId,
        payment_amount: amount,
        new_balance: updatedLedger.balance_cents,
        payment_processed_at: new Date().toISOString()
      };

      res.apiResponse(response, 'object');
    } catch (err) {
      if (err.message === 'ERR_OVERPAY_NOT_ALLOWED') {
        return res.apiError('ERR_OVERPAY_NOT_ALLOWED', 'Payment amount exceeds outstanding balance', 422);
      }
      throw err;
    }
  } catch (err) {
    next(err);
  }
});

// GET /terms/:termId/students/:studentId/balance - Get student's tuition balance (helper endpoint)
router.get('/:studentId/balance', authorize.studentOrRegistrar, async (req, res, next) => {
  try {
    const { termId, studentId } = req.params;

    // Validate UUID formats
    if (!validateUUID(termId) || !validateUUID(studentId)) {
      return res.apiError('ERR_INVALID_ID_FORMAT', 'Invalid ID format', 400);
    }

    // Check authorization
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      // Students can only see their own balance
      if (studentId !== req.user.id) {
        return res.apiError('ERR_FORBIDDEN', 'Students can only view their own balance', 403);
      }
    } else if (req.user.role === CONSTANTS.USER_ROLES.REGISTRAR) {
      // Registrar can view any student's balance, but student must exist
      const student = Users.findById(studentId);
      if (!student || student.role !== CONSTANTS.USER_ROLES.STUDENT) {
        return res.apiError('ERR_STUDENT_NOT_FOUND', 'Student not found', 404);
      }
    }

    // Check if term exists
    const term = Terms.findById(termId);
    if (!term) {
      return res.apiError('ERR_TERM_NOT_FOUND', 'Term not found', 404);
    }

    // Get student tuition ledger
    let ledger = StudentTuitionLedgers.findByStudentAndTerm(studentId, termId);
    if (!ledger) {
      ledger = StudentTuitionLedgers.create(studentId, termId, 0);
    }

    const response = {
      student_id: studentId,
      term_id: termId,
      balance_cents: ledger.balance_cents,
      balance_dollars: (ledger.balance_cents / 100).toFixed(2)
    };

    res.apiResponse(response, 'object');
  } catch (err) {
    next(err);
  }
});

module.exports = router;