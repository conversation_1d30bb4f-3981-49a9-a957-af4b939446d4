# University Course Registration & Enrollment API

A Node.js API implementation for managing academic course offerings and student enrollments within academic terms.

## Features

- **Term Management**: Create and manage academic terms with lifecycle states
- **Course Management**: Create, publish, and cancel courses with instructor assignment
- **Enrollment System**: Student enrollment with waitlist support and automatic promotion
- **Payment Processing**: Tuition payment tracking with balance management
- **RBAC Security**: Role-based access control (<PERSON>, Professor, Registrar)
- **Business Rules**: Credit limits, drop limits, capacity management, and penalties

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

## Running the Application

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

The server will start on port 3000 (or the port specified in the PORT environment variable).

## API Documentation

### Authentication

All requests must include these headers:
- `X-User-ID`: User's UUID
- `X-User-Role`: One of `STUDENT`, `PROFESSOR`, or `REGISTRAR`

### Response Format

All successful responses follow this format:
```json
{
  "meta": {
    "api_request_id": "req_RANDOMALPHANUM123456",
    "api_request_timestamp": "2025-05-24T10:30:00.123Z"
  },
  "response_type": "object" | "array",
  "data": {}
}
```

Error responses follow this format:
```json
{
  "meta": {
    "api_request_id": "req_RANDOMALPHANUM123456",
    "api_request_timestamp": "2025-05-24T10:30:00.123Z"
  },
  "response_type": "error",
  "data": {
    "error_id": "ERROR_CODE",
    "message": "Human readable error message"
  }
}
```

### Endpoints

#### Term Management
- `POST /terms` - Create a new academic term (Registrar only)
- `GET /terms/{termId}` - Get term details
- `PATCH /terms/{termId}:open-registration` - Open enrollment (Registrar only)
- `PATCH /terms/{termId}:close-registration` - Close enrollment (Registrar only)
- `PATCH /terms/{termId}:conclude` - Conclude term (Registrar only)

#### Course Management
- `POST /terms/{termId}/courses` - Create a course (Professor/Registrar)
- `GET /terms/{termId}/courses` - List courses (filtered by role)
- `GET /terms/{termId}/courses/{courseId}` - Get course details
- `PATCH /terms/{termId}/courses/{courseId}:publish` - Publish course (Professor/Registrar)
- `PATCH /terms/{termId}/courses/{courseId}:cancel` - Cancel course (Professor/Registrar)

#### Enrollment Management
- `POST /terms/{termId}/courses/{courseId}/enrollments` - Enroll in course (Student/Registrar)
- `GET /terms/{termId}/courses/{courseId}/enrollments` - List enrollments (Professor/Registrar)
- `GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}` - Get enrollment details
- `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop` - Drop enrollment

#### Payment Processing
- `POST /terms/{termId}/students/{studentId}:pay` - Record payment (Student/Registrar)
- `GET /terms/{termId}/students/{studentId}/balance` - Get balance (Student/Registrar)

## Business Rules

### Constants
- Maximum credits per term: 18
- Maximum courses per professor: 5
- Maximum drops per term: 3
- Cost per credit: $100.00 (10000 cents)
- Drop penalty fee: $50.00 (5000 cents)

### Term States
- `PLANNING` → `ENROLLMENT_OPEN` → `ENROLLMENT_CLOSED` → `CONCLUDED`

### Course States
- `DRAFT` → `OPEN` → `IN_PROGRESS` → `COMPLETED`
- Courses can be `CANCELLED` from any non-terminal state

### Enrollment States
- `ENROLLED` (has a seat)
- `WAITLISTED` (waiting for a seat)
- `DROPPED` (withdrawn)
- `COMPLETED` (finished course)

### Key Features
- **Automatic Waitlist Promotion**: When a seat opens, the longest-waiting student is automatically enrolled
- **Credit Limits**: Students cannot exceed 18 credits (Registrar can override)
- **Drop Penalties**: 3rd drop in a term incurs a $50 penalty fee
- **Capacity Management**: Courses have fixed capacity with seat tracking
- **Financial Tracking**: Automatic tuition charging and refunding

## Demo Users

The system includes demo users for testing:
- Registrar: `550e8400-e29b-41d4-a716-446655440001`
- Professors: `550e8400-e29b-41d4-a716-446655440002`, `550e8400-e29b-41d4-a716-446655440003`
- Students: `550e8400-e29b-41d4-a716-446655440004`, `550e8400-e29b-41d4-a716-446655440005`

## Example Usage

1. **Create a term** (as Registrar):
   ```bash
   curl -X POST http://localhost:3000/terms \
     -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440001" \
     -H "X-User-Role: REGISTRAR" \
     -H "Content-Type: application/json" \
     -d '{"name": "Fall 2025"}'
   ```

2. **Open enrollment**:
   ```bash
   curl -X PATCH http://localhost:3000/terms/{termId}:open-registration \
     -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440001" \
     -H "X-User-Role: REGISTRAR" \
     -H "Content-Type: application/json" \
     -d '{"revision": 0}'
   ```

3. **Create a course** (as Professor):
   ```bash
   curl -X POST http://localhost:3000/terms/{termId}/courses \
     -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440002" \
     -H "X-User-Role: PROFESSOR" \
     -H "Content-Type: application/json" \
     -d '{
       "code": "CS101",
       "title": "Introduction to Computer Science",
       "description": "Basic programming concepts",
       "credits": 3,
       "capacity": 30,
       "delivery_mode": "IN_PERSON",
       "location": "Room 101"
     }'
   ```

4. **Publish the course**:
   ```bash
   curl -X PATCH http://localhost:3000/terms/{termId}/courses/{courseId}:publish \
     -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440002" \
     -H "X-User-Role: PROFESSOR" \
     -H "Content-Type: application/json" \
     -d '{"revision": 0}'
   ```

5. **Enroll a student**:
   ```bash
   curl -X POST http://localhost:3000/terms/{termId}/courses/{courseId}/enrollments \
     -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440004" \
     -H "X-User-Role: STUDENT" \
     -H "Content-Type: application/json"
   ```

## Architecture

- **In-Memory Storage**: Uses Maps for data persistence (demo purposes)
- **Middleware-Based**: Authentication, validation, response formatting
- **Service Layer**: Business logic separation (waitlist promotion)
- **Event-Driven**: Asynchronous waitlist promotion on seat availability
- **Ledger System**: Consistent seat and financial tracking

## Error Handling

The API implements comprehensive error handling with specific error codes for all business rules and validation failures. See the PRD.md for the complete error catalog.

## Testing

Run tests with:
```bash
npm test
```

Note: Test suite is not implemented in this demo version.