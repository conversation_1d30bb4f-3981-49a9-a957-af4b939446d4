// This file contains the continuation of server.js
// Add these endpoints to the main server.js file:

// GET /terms/{termId}/courses/{courseId} - Get detailed info on a specific course
app.get('/terms/:termId/courses/:courseId', authenticate, (req, res) => {
  const { termId, courseId } = req.params;

  if (!isValidUUID(termId) || !isValidUUID(courseId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  const course = courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse('ERR_COURSE_NOT_FOUND', 'Course not found'));
  }

  // Role-based visibility
  if (req.auth.userRole === 'STUDENT') {
    if (!['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)) {
      return res.status(404).json(createErrorResponse('ERR_COURSE_NOT_FOUND', 'Course not found'));
    }
  } else if (req.auth.userRole === 'PROFESSOR') {
    if (course.professor_id !== req.auth.userId && !['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)) {
      return res.status(404).json(createErrorResponse('ERR_COURSE_NOT_FOUND', 'Course not found'));
    }
  }

  const enrichedCourse = { ...course };
  
  // Calculate derived fields
  const courseEnrollments = Array.from(enrollments.values()).filter(e => e.course_id === courseId);
  const enrolledCount = courseEnrollments.filter(e => e.state === 'ENROLLED').length;
  const waitlistCount = courseEnrollments.filter(e => e.state === 'WAITLISTED').length;
  
  enrichedCourse.available_seats = course.capacity - enrolledCount;

  // Add role-specific fields
  if (req.auth.userRole === 'STUDENT') {
    // Check if student is enrolled or waitlisted
    const studentEnrollment = courseEnrollments.find(e => e.student_id === req.auth.userId && ['ENROLLED', 'WAITLISTED'].includes(e.state));
    if (studentEnrollment) {
      enrichedCourse.is_enrolled = studentEnrollment.state === 'ENROLLED';
      enrichedCourse.is_waitlisted = studentEnrollment.state === 'WAITLISTED';
    }
  } else if (req.auth.userRole === 'PROFESSOR' && course.professor_id === req.auth.userId) {
    enrichedCourse.enrolled_count = enrolledCount;
    enrichedCourse.waitlist_count = waitlistCount;
    enrichedCourse.enrollments = courseEnrollments.map(e => ({
      id: e.id,
      student_id: e.student_id,
      state: e.state,
      created_at: e.created_at
    }));
  } else if (req.auth.userRole === 'REGISTRAR') {
    enrichedCourse.enrolled_count = enrolledCount;
    enrichedCourse.waitlist_count = waitlistCount;
    enrichedCourse.enrollments = courseEnrollments.map(e => ({
      id: e.id,
      student_id: e.student_id,
      state: e.state,
      created_at: e.created_at
    }));
  }

  res.json(createSuccessResponse(enrichedCourse));
});

// PATCH /terms/{termId}/courses/{courseId}:publish - Publish a draft course
app.patch('/terms/:termId/courses/:courseId\\:publish', authenticate, validateNoUnknownFields(['revision']), (req, res) => {
  const { termId, courseId } = req.params;

  if (!isValidUUID(termId) || !isValidUUID(courseId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  const course = courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse('ERR_COURSE_NOT_FOUND', 'Course not found'));
  }

  // Authorization check
  if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse('ERR_NOT_INSTRUCTOR', 'Only the course instructor can publish this course'));
  } else if (req.auth.userRole !== 'PROFESSOR' && req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Only professors and registrars can publish courses'));
  }

  // Validate course state
  if (course.state !== 'DRAFT') {
    return res.status(409).json(createErrorResponse('ERR_COURSE_WRONG_STATE', 'Course must be in DRAFT state to publish'));
  }

  // Validate term state
  if (term.state !== 'ENROLLMENT_OPEN') {
    return res.status(409).json(createErrorResponse('ERR_TERM_NOT_ACTIVE', 'Term must be open for enrollment to publish courses'));
  }

  // Check revision for optimistic concurrency
  if (req.body.revision !== undefined && req.body.revision !== course.revision) {
    return res.status(409).json(createErrorResponse('ERR_REV_CONFLICT', 'Course has been modified by another user'));
  }

  course.state = 'OPEN';
  course.revision++;
  course.published_at = new Date().toISOString();

  // Initialize seat ledger
  courseSeatLedgers.set(courseId, {
    course_id: courseId,
    seats_available: course.capacity
  });

  res.json(createSuccessResponse(course));
});

// PATCH /terms/{termId}/courses/{courseId}:cancel - Cancel a course
app.patch('/terms/:termId/courses/:courseId\\:cancel', authenticate, validateNoUnknownFields(['revision']), (req, res) => {
  const { termId, courseId } = req.params;

  if (!isValidUUID(termId) || !isValidUUID(courseId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  const course = courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse('ERR_COURSE_NOT_FOUND', 'Course not found'));
  }

  // Authorization check
  if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse('ERR_NOT_INSTRUCTOR', 'Only the course instructor can cancel this course'));
  } else if (req.auth.userRole !== 'PROFESSOR' && req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Only professors and registrars can cancel courses'));
  }

  // Validate course state
  if (!['DRAFT', 'OPEN', 'IN_PROGRESS'].includes(course.state)) {
    return res.status(409).json(createErrorResponse('ERR_COURSE_WRONG_STATE', 'Course cannot be cancelled in its current state'));
  }

  // Check revision for optimistic concurrency
  if (req.body.revision !== undefined && req.body.revision !== course.revision) {
    return res.status(409).json(createErrorResponse('ERR_REV_CONFLICT', 'Course has been modified by another user'));
  }

  course.state = 'CANCELLED';
  course.revision++;

  // Drop all enrollments for this course
  const courseEnrollments = Array.from(enrollments.values()).filter(e => e.course_id === courseId);
  for (const enrollment of courseEnrollments) {
    if (['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {
      // If enrolled, refund tuition
      if (enrollment.state === 'ENROLLED') {
        const ledgerKey = `${enrollment.student_id}-${termId}`;
        if (!studentTuitionLedgers.has(ledgerKey)) {
          studentTuitionLedgers.set(ledgerKey, { student_id: enrollment.student_id, term_id: termId, balance_cents: 0 });
        }
        const ledger = studentTuitionLedgers.get(ledgerKey);
        const courseCharge = course.credits * COST_PER_CREDIT;
        ledger.balance_cents = Math.max(0, ledger.balance_cents - courseCharge);
      }
      
      enrollment.state = 'DROPPED';
      enrollment.revision++;
    }
  }

  // Clear seat ledger
  courseSeatLedgers.delete(courseId);

  res.json(createSuccessResponse(course));
});

// ENROLLMENT & WAITLIST ENDPOINTS

// POST /terms/{termId}/courses/{courseId}/enrollments - Enroll in a course
app.post('/terms/:termId/courses/:courseId/enrollments', authenticate, validateNoUnknownFields(['student_id']), (req, res) => {
  const { termId, courseId } = req.params;
  const { student_id } = req.body;

  if (!isValidUUID(termId) || !isValidUUID(courseId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  const course = courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse('ERR_COURSE_NOT_FOUND', 'Course not found'));
  }

  // Validate term and course state
  if (term.state !== 'ENROLLMENT_OPEN') {
    return res.status(409).json(createErrorResponse('ERR_REGISTRATION_CLOSED', 'Registration is not open'));
  }

  if (course.state !== 'OPEN') {
    return res.status(409).json(createErrorResponse('ERR_COURSE_WRONG_STATE', 'Course is not open for enrollment'));
  }

  // Determine target student
  let targetStudentId;
  if (req.auth.userRole === 'STUDENT') {
    if (student_id && student_id !== req.auth.userId) {
      return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Students can only enroll themselves'));
    }
    targetStudentId = req.auth.userId;
  } else if (req.auth.userRole === 'REGISTRAR') {
    if (!student_id) {
      return res.status(400).json(createErrorResponse('ERR_MISSING_REQUIRED_FIELD', 'Student ID is required for registrar enrollment'));
    }
    if (!isValidUUID(student_id)) {
      return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid student ID format'));
    }
    targetStudentId = student_id;
  } else {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Only students and registrars can create enrollments'));
  }

  // Check for duplicate enrollment
  for (const enrollment of enrollments.values()) {
    if (enrollment.course_id === courseId && enrollment.student_id === targetStudentId && 
        ['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {
      return res.status(409).json(createErrorResponse('ERR_ALREADY_ENROLLED', 'Student is already enrolled or waitlisted in this course'));
    }
  }

  // Check credit limit for students (but not for registrar override)
  if (req.auth.userRole === 'STUDENT') {
    let currentCredits = 0;
    for (const enrollment of enrollments.values()) {
      if (enrollment.student_id === targetStudentId && enrollment.state === 'ENROLLED') {
        const enrolledCourse = courses.get(enrollment.course_id);
        if (enrolledCourse && enrolledCourse.term_id === termId) {
          currentCredits += enrolledCourse.credits;
        }
      }
    }
    
    if (currentCredits + course.credits > MAX_CREDITS_PER_TERM) {
      return res.status(409).json(createErrorResponse('ERR_CREDIT_LIMIT_EXCEEDED', 'Enrollment would exceed credit limit'));
    }
  }

  // Determine enrollment state based on seat availability
  const ledger = courseSeatLedgers.get(courseId);
  let enrollmentState;
  
  if (ledger && ledger.seats_available > 0) {
    enrollmentState = 'ENROLLED';
    // Debit seat ledger
    ledger.seats_available--;
    // Credit tuition ledger
    const ledgerKey = `${targetStudentId}-${termId}`;
    if (!studentTuitionLedgers.has(ledgerKey)) {
      studentTuitionLedgers.set(ledgerKey, { student_id: targetStudentId, term_id: termId, balance_cents: 0 });
    }
    const tuitionLedger = studentTuitionLedgers.get(ledgerKey);
    tuitionLedger.balance_cents += course.credits * COST_PER_CREDIT;
  } else {
    enrollmentState = 'WAITLISTED';
  }

  const enrollmentId = uuidv4();
  const newEnrollment = {
    id: enrollmentId,
    course_id: courseId,
    student_id: targetStudentId,
    term_id: termId,
    state: enrollmentState,
    created_at: new Date().toISOString(),
    revision: 0
  };

  enrollments.set(enrollmentId, newEnrollment);
  res.status(201).json(createSuccessResponse(newEnrollment));
});

// Waitlist promotion helper function
function promoteFromWaitlist(courseId) {
  const courseEnrollments = Array.from(enrollments.values())
    .filter(e => e.course_id === courseId && e.state === 'WAITLISTED')
    .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  if (courseEnrollments.length === 0) return;

  const ledger = courseSeatLedgers.get(courseId);
  if (!ledger || ledger.seats_available <= 0) return;

  const nextEnrollment = courseEnrollments[0];
  const course = courses.get(courseId);

  // Promote to enrolled
  nextEnrollment.state = 'ENROLLED';
  nextEnrollment.revision++;

  // Update ledgers
  ledger.seats_available--;
  const ledgerKey = `${nextEnrollment.student_id}-${nextEnrollment.term_id}`;
  if (!studentTuitionLedgers.has(ledgerKey)) {
    studentTuitionLedgers.set(ledgerKey, { student_id: nextEnrollment.student_id, term_id: nextEnrollment.term_id, balance_cents: 0 });
  }
  const tuitionLedger = studentTuitionLedgers.get(ledgerKey);
  tuitionLedger.balance_cents += course.credits * COST_PER_CREDIT;
}

// GET /terms/{termId}/courses/{courseId}/enrollments - List enrollments for a course
app.get('/terms/:termId/courses/:courseId/enrollments', authenticate, (req, res) => {
  const { termId, courseId } = req.params;
  const { limit = 50, offset = 0 } = req.query;

  if (!isValidUUID(termId) || !isValidUUID(courseId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  const course = courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse('ERR_COURSE_NOT_FOUND', 'Course not found'));
  }

  // Authorization check
  if (req.auth.userRole === 'STUDENT') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Students cannot view course rosters'));
  } else if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse('ERR_NOT_INSTRUCTOR', 'Professors can only view rosters for their own courses'));
  }

  let courseEnrollments = Array.from(enrollments.values()).filter(e => e.course_id === courseId);
  
  // Sort by creation time
  courseEnrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  // Apply pagination
  const total = courseEnrollments.length;
  const paginatedEnrollments = courseEnrollments.slice(parseInt(offset), parseInt(offset) + parseInt(limit));

  const response = createSuccessResponse(paginatedEnrollments, 'array');
  response.meta.total = total;
  response.meta.limit = parseInt(limit);
  response.meta.offset = parseInt(offset);

  res.json(response);
});

// GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId} - Get specific enrollment
app.get('/terms/:termId/courses/:courseId/enrollments/:enrollmentId', authenticate, (req, res) => {
  const { termId, courseId, enrollmentId } = req.params;

  if (!isValidUUID(termId) || !isValidUUID(courseId) || !isValidUUID(enrollmentId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  const course = courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse('ERR_COURSE_NOT_FOUND', 'Course not found'));
  }

  const enrollment = enrollments.get(enrollmentId);
  if (!enrollment || enrollment.course_id !== courseId) {
    return res.status(404).json(createErrorResponse('ERR_ENROLLMENT_NOT_FOUND', 'Enrollment not found'));
  }

  // Authorization check
  if (req.auth.userRole === 'STUDENT' && enrollment.student_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Students can only view their own enrollments'));
  } else if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse('ERR_NOT_INSTRUCTOR', 'Professors can only view enrollments for their own courses'));
  }

  res.json(createSuccessResponse(enrollment));
});

// PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop - Drop enrollment
app.patch('/terms/:termId/courses/:courseId/enrollments/:enrollmentId\\:drop', authenticate, validateNoUnknownFields(['revision']), (req, res) => {
  const { termId, courseId, enrollmentId } = req.params;

  if (!isValidUUID(termId) || !isValidUUID(courseId) || !isValidUUID(enrollmentId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  if (term.state === 'CONCLUDED') {
    return res.status(409).json(createErrorResponse('ERR_TERM_NOT_ACTIVE', 'Cannot drop from concluded term'));
  }

  const course = courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse('ERR_COURSE_NOT_FOUND', 'Course not found'));
  }

  const enrollment = enrollments.get(enrollmentId);
  if (!enrollment || enrollment.course_id !== courseId) {
    return res.status(404).json(createErrorResponse('ERR_ENROLLMENT_NOT_FOUND', 'Enrollment not found'));
  }

  // Authorization check
  if (req.auth.userRole === 'STUDENT' && enrollment.student_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Students can only drop their own enrollments'));
  } else if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse('ERR_NOT_INSTRUCTOR', 'Professors can only drop enrollments from their own courses'));
  } else if (req.auth.userRole !== 'STUDENT' && req.auth.userRole !== 'PROFESSOR' && req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Unauthorized to drop enrollments'));
  }

  // Validate enrollment state
  if (!['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {
    return res.status(409).json(createErrorResponse('ERR_ENROLLMENT_WRONG_STATE', 'Enrollment cannot be dropped in its current state'));
  }

  // Check revision for optimistic concurrency
  if (req.body.revision !== undefined && req.body.revision !== enrollment.revision) {
    return res.status(409).json(createErrorResponse('ERR_REV_CONFLICT', 'Enrollment has been modified by another user'));
  }

  // Check drop count limit for students
  if (req.auth.userRole === 'STUDENT') {
    let dropCount = 0;
    for (const e of enrollments.values()) {
      if (e.student_id === enrollment.student_id && e.term_id === termId && e.state === 'DROPPED') {
        dropCount++;
      }
    }
    
    if (dropCount >= MAX_DROP_COUNT_PER_TERM) {
      return res.status(409).json(createErrorResponse('ERR_TOO_MANY_DROPS', 'Student has reached maximum number of drops for this term'));
    }
  }

  const wasEnrolled = enrollment.state === 'ENROLLED';
  enrollment.state = 'DROPPED';
  enrollment.revision++;

  // Handle ledger updates
  if (wasEnrolled) {
    // Credit seat ledger (free up seat)
    const seatLedger = courseSeatLedgers.get(courseId);
    if (seatLedger) {
      seatLedger.seats_available++;
    }

    // Debit tuition ledger (refund)
    const ledgerKey = `${enrollment.student_id}-${termId}`;
    if (studentTuitionLedgers.has(ledgerKey)) {
      const tuitionLedger = studentTuitionLedgers.get(ledgerKey);
      const courseCharge = course.credits * COST_PER_CREDIT;
      tuitionLedger.balance_cents = Math.max(0, tuitionLedger.balance_cents - courseCharge);
    }

    // Promote from waitlist
    setTimeout(() => promoteFromWaitlist(courseId), 0);
  }

  // Apply drop penalty for student's 3rd drop
  if (req.auth.userRole === 'STUDENT') {
    let dropCount = 0;
    for (const e of enrollments.values()) {
      if (e.student_id === enrollment.student_id && e.term_id === termId && e.state === 'DROPPED') {
        dropCount++;
      }
    }
    
    if (dropCount === 3) {
      const ledgerKey = `${enrollment.student_id}-${termId}`;
      if (!studentTuitionLedgers.has(ledgerKey)) {
        studentTuitionLedgers.set(ledgerKey, { student_id: enrollment.student_id, term_id: termId, balance_cents: 0 });
      }
      const tuitionLedger = studentTuitionLedgers.get(ledgerKey);
      tuitionLedger.balance_cents += DROP_PENALTY_FEE;
    }
  }

  res.json(createSuccessResponse(enrollment));
});

// FINANCIAL OPERATIONS ENDPOINTS

// POST /terms/{termId}/students/{studentId}:pay - Record tuition payment
app.post('/terms/:termId/students/:studentId\\:pay', authenticate, validateNoUnknownFields(['amount']), (req, res) => {
  const { termId, studentId } = req.params;
  const { amount } = req.body;

  if (!isValidUUID(termId) || !isValidUUID(studentId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  // Authorization check
  if (req.auth.userRole === 'STUDENT' && req.auth.userId !== studentId) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Students can only pay their own tuition'));
  } else if (req.auth.userRole !== 'STUDENT' && req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Only students and registrars can record payments'));
  }

  // Validate amount
  if (!amount || !Number.isInteger(amount) || amount <= 0) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_PAYMENT_AMOUNT', 'Payment amount must be a positive integer (cents)'));
  }

  // Get or create tuition ledger
  const ledgerKey = `${studentId}-${termId}`;
  if (!studentTuitionLedgers.has(ledgerKey)) {
    studentTuitionLedgers.set(ledgerKey, { student_id: studentId, term_id: termId, balance_cents: 0 });
  }
  const ledger = studentTuitionLedgers.get(ledgerKey);

  // Check for overpayment
  if (amount > ledger.balance_cents) {
    return res.status(422).json(createErrorResponse('ERR_OVERPAY_NOT_ALLOWED', 'Payment amount exceeds outstanding balance'));
  }

  // Apply payment (debit ledger)
  ledger.balance_cents -= amount;

  const response = {
    student_id: studentId,
    term_id: termId,
    payment_amount: amount,
    new_balance: ledger.balance_cents
  };

  res.json(createSuccessResponse(response));
});