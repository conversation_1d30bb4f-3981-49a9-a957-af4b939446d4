const express = require('express');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json());

// Constants
const MAX_CREDITS_PER_TERM = 18;
const MAX_COURSES_PER_PROF = 5;
const MAX_DROP_COUNT_PER_TERM = 3;
const COST_PER_CREDIT = 10000; // $100.00 in cents
const DROP_PENALTY_FEE = 5000; // $50.00 in cents

// In-memory data stores
const terms = new Map();
const courses = new Map();
const enrollments = new Map();
const courseSeatLedgers = new Map();
const studentTuitionLedgers = new Map();
const users = new Map(); // For user validation

// Helper function to generate API request metadata
function generateRequestMeta() {
  return {
    api_request_id: `req_${Math.random().toString(36).substr(2, 16).toUpperCase()}`,
    api_request_timestamp: new Date().toISOString()
  };
}

// Helper function to create success response
function createSuccessResponse(data, responseType = 'object') {
  return {
    meta: generateRequestMeta(),
    response_type: responseType,
    data: data || {}
  };
}

// Helper function to create error response
function createErrorResponse(errorId, message) {
  return {
    meta: generateRequestMeta(),
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };
}

// UUID validation
function isValidUUID(id) {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidRegex.test(id);
}

// Course code validation
function isValidCourseCode(code) {
  const codeRegex = /^[A-Z]{2,4}\d{3}$/;
  return codeRegex.test(code);
}

// Authentication middleware
function authenticate(req, res, next) {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  if (!userId || !userRole) {
    return res.status(400).json(createErrorResponse('ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER', 'Missing X-User-ID or X-User-Role header'));
  }

  if (!isValidUUID(userId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid UUID format for X-User-ID'));
  }

  const validRoles = ['STUDENT', 'PROFESSOR', 'REGISTRAR'];
  if (!validRoles.includes(userRole)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ENUM_VALUE', 'Invalid user role'));
  }

  req.auth = { userId, userRole };
  next();
}

// Validation middleware for unknown fields
function validateNoUnknownFields(allowedFields) {
  return (req, res, next) => {
    if (req.body && typeof req.body === 'object') {
      const bodyKeys = Object.keys(req.body);
      const unknownFields = bodyKeys.filter(key => !allowedFields.includes(key));
      if (unknownFields.length > 0) {
        return res.status(400).json(createErrorResponse('ERR_UNKNOWN_FIELD', `Unknown field(s): ${unknownFields.join(', ')}`));
      }
    }
    next();
  };
}

// Initialize some users for testing
users.set('student1', { id: 'student1', role: 'STUDENT' });
users.set('professor1', { id: 'professor1', role: 'PROFESSOR' });
users.set('registrar1', { id: 'registrar1', role: 'REGISTRAR' });

// TERM MANAGEMENT ENDPOINTS

// POST /terms - Create a new academic term
app.post('/terms', authenticate, validateNoUnknownFields(['name', 'revision']), (req, res) => {
  if (req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Only registrars can create terms'));
  }

  const { name } = req.body;

  if (!name || typeof name !== 'string' || name.trim() === '') {
    return res.status(400).json(createErrorResponse('ERR_MISSING_REQUIRED_FIELD', 'Term name is required'));
  }

  // Check for duplicate term name
  for (const term of terms.values()) {
    if (term.name === name.trim()) {
      return res.status(409).json(createErrorResponse('ERR_TERM_NAME_NOT_UNIQUE', 'Term name must be unique'));
    }
  }

  const termId = uuidv4();
  const newTerm = {
    id: termId,
    name: name.trim(),
    state: 'PLANNING',
    created_by: req.auth.userId,
    created_at: new Date().toISOString(),
    revision: 0
  };

  terms.set(termId, newTerm);
  res.status(201).json(createSuccessResponse(newTerm));
});

// GET /terms/{termId} - Retrieve details of an academic term
app.get('/terms/:termId', authenticate, (req, res) => {
  const { termId } = req.params;

  if (!isValidUUID(termId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid term ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  res.json(createSuccessResponse(term));
});

// PATCH /terms/{termId}:open-registration - Open student registration
app.patch('/terms/:termId\\:open-registration', authenticate, validateNoUnknownFields(['revision']), (req, res) => {
  if (req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Only registrars can open registration'));
  }

  const { termId } = req.params;

  if (!isValidUUID(termId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid term ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  if (term.state !== 'PLANNING') {
    return res.status(409).json(createErrorResponse('ERR_TERM_NOT_ACTIVE', 'Term must be in PLANNING state to open registration'));
  }

  // Check revision for optimistic concurrency
  if (req.body.revision !== undefined && req.body.revision !== term.revision) {
    return res.status(409).json(createErrorResponse('ERR_REV_CONFLICT', 'Term has been modified by another user'));
  }

  term.state = 'ENROLLMENT_OPEN';
  term.revision++;

  res.json(createSuccessResponse(term));
});

// PATCH /terms/{termId}:close-registration - Close enrollment period
app.patch('/terms/:termId\\:close-registration', authenticate, validateNoUnknownFields(['revision']), (req, res) => {
  if (req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Only registrars can close registration'));
  }

  const { termId } = req.params;

  if (!isValidUUID(termId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid term ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  if (term.state !== 'ENROLLMENT_OPEN') {
    return res.status(409).json(createErrorResponse('ERR_TERM_NOT_ACTIVE', 'Term must be in ENROLLMENT_OPEN state to close registration'));
  }

  // Check revision for optimistic concurrency
  if (req.body.revision !== undefined && req.body.revision !== term.revision) {
    return res.status(409).json(createErrorResponse('ERR_REV_CONFLICT', 'Term has been modified by another user'));
  }

  term.state = 'ENROLLMENT_CLOSED';
  term.revision++;

  // Transition all OPEN courses to IN_PROGRESS
  for (const course of courses.values()) {
    if (course.term_id === termId && course.state === 'OPEN') {
      course.state = 'IN_PROGRESS';
      course.revision++;
    }
  }

  res.json(createSuccessResponse(term));
});

// PATCH /terms/{termId}:conclude - Conclude the term
app.patch('/terms/:termId\\:conclude', authenticate, validateNoUnknownFields(['revision']), (req, res) => {
  if (req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Only registrars can conclude terms'));
  }

  const { termId } = req.params;

  if (!isValidUUID(termId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid term ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  if (term.state !== 'ENROLLMENT_CLOSED') {
    return res.status(409).json(createErrorResponse('ERR_TERM_NOT_ACTIVE', 'Term must be in ENROLLMENT_CLOSED state to conclude'));
  }

  // Check revision for optimistic concurrency
  if (req.body.revision !== undefined && req.body.revision !== term.revision) {
    return res.status(409).json(createErrorResponse('ERR_REV_CONFLICT', 'Term has been modified by another user'));
  }

  term.state = 'CONCLUDED';
  term.revision++;

  // Finalize all courses and enrollments
  for (const course of courses.values()) {
    if (course.term_id === termId && course.state === 'IN_PROGRESS') {
      course.state = 'COMPLETED';
      course.revision++;
    }
  }

  for (const enrollment of enrollments.values()) {
    if (enrollment.term_id === termId) {
      if (enrollment.state === 'ENROLLED') {
        enrollment.state = 'COMPLETED';
      } else if (enrollment.state === 'WAITLISTED') {
        enrollment.state = 'DROPPED';
      }
      enrollment.revision++;
    }
  }

  res.json(createSuccessResponse(term));
});

// COURSE MANAGEMENT ENDPOINTS

// POST /terms/{termId}/courses - Create a new course
app.post('/terms/:termId/courses', authenticate, validateNoUnknownFields(['code', 'title', 'description', 'credits', 'capacity', 'professor_id', 'delivery_mode', 'location', 'online_link']), (req, res) => {
  const { termId } = req.params;
  const { code, title, description, credits, capacity, professor_id, delivery_mode, location, online_link } = req.body;

  if (!isValidUUID(termId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid term ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  // Authorization check
  if (req.auth.userRole !== 'PROFESSOR' && req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse('ERR_UNAUTHORIZED_ROLE', 'Only professors and registrars can create courses'));
  }

  // Validate required fields
  if (!code || !title || credits === undefined || capacity === undefined || !delivery_mode) {
    return res.status(400).json(createErrorResponse('ERR_MISSING_REQUIRED_FIELD', 'Missing required course fields'));
  }

  // Validate course code format
  if (!isValidCourseCode(code)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_COURSE_CODE', 'Course code must be 2-4 uppercase letters followed by 3 digits'));
  }

  // Check for duplicate course code within term
  for (const course of courses.values()) {
    if (course.term_id === termId && course.code === code) {
      return res.status(409).json(createErrorResponse('ERR_COURSE_CODE_NOT_UNIQUE', 'Course code must be unique within the term'));
    }
  }

  // Validate field lengths
  if (title.length === 0 || title.length > 100) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_FIELD_LENGTH', 'Title must be 1-100 characters'));
  }

  if (description && description.length > 1000) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_FIELD_LENGTH', 'Description must be at most 1000 characters'));
  }

  // Validate credits
  if (credits < 1 || credits > 5 || !Number.isInteger(credits)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_CREDITS', 'Credits must be an integer between 1 and 5'));
  }

  // Validate capacity
  if (capacity < 1 || capacity > 500 || !Number.isInteger(capacity)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_CAPACITY', 'Capacity must be an integer between 1 and 500'));
  }

  // Validate delivery mode and conditional fields
  const validDeliveryModes = ['IN_PERSON', 'ONLINE', 'HYBRID'];
  if (!validDeliveryModes.includes(delivery_mode)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ENUM_VALUE', 'Invalid delivery mode'));
  }

  if (delivery_mode === 'IN_PERSON') {
    if (!location || location.trim() === '') {
      return res.status(400).json(createErrorResponse('ERR_CONDITIONAL_FIELD_REQUIRED', 'Location is required for in-person courses'));
    }
    if (online_link) {
      return res.status(400).json(createErrorResponse('ERR_FIELD_CONFLICT', 'Online link should not be provided for in-person courses'));
    }
  } else if (delivery_mode === 'ONLINE') {
    if (!online_link || online_link.trim() === '') {
      return res.status(400).json(createErrorResponse('ERR_CONDITIONAL_FIELD_REQUIRED', 'Online link is required for online courses'));
    }
    if (location) {
      return res.status(400).json(createErrorResponse('ERR_FIELD_CONFLICT', 'Location should not be provided for online courses'));
    }
  } else if (delivery_mode === 'HYBRID') {
    if ((!location || location.trim() === '') && (!online_link || online_link.trim() === '')) {
      return res.status(400).json(createErrorResponse('ERR_CONDITIONAL_FIELD_REQUIRED', 'At least one of location or online link is required for hybrid courses'));
    }
  }

  // Determine instructor
  let instructorId;
  if (req.auth.userRole === 'REGISTRAR') {
    if (!professor_id) {
      return res.status(400).json(createErrorResponse('ERR_MISSING_REQUIRED_FIELD', 'Professor ID is required when registrar creates course'));
    }
    if (!isValidUUID(professor_id)) {
      return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid professor ID format'));
    }
    // In a real system, we'd validate the professor exists and has the right role
    instructorId = professor_id;
  } else { // PROFESSOR
    if (professor_id && professor_id !== req.auth.userId) {
      return res.status(400).json(createErrorResponse('ERR_FIELD_CONFLICT', 'Professor cannot create course for another instructor'));
    }
    instructorId = req.auth.userId;
  }

  // Check professor course limit
  let profCourseCount = 0;
  for (const course of courses.values()) {
    if (course.term_id === termId && course.professor_id === instructorId) {
      profCourseCount++;
    }
  }

  if (profCourseCount >= MAX_COURSES_PER_PROF) {
    return res.status(409).json(createErrorResponse('ERR_MAX_COURSES_REACHED', 'Professor already teaches maximum number of courses in this term'));
  }

  const courseId = uuidv4();
  const newCourse = {
    id: courseId,
    term_id: termId,
    code,
    title,
    description: description || '',
    credits,
    capacity,
    professor_id: instructorId,
    delivery_mode,
    location: delivery_mode === 'ONLINE' ? undefined : (location || ''),
    online_link: delivery_mode === 'IN_PERSON' ? undefined : (online_link || ''),
    state: 'DRAFT',
    created_at: new Date().toISOString(),
    revision: 0
  };

  // Remove undefined fields
  Object.keys(newCourse).forEach(key => newCourse[key] === undefined && delete newCourse[key]);

  courses.set(courseId, newCourse);
  res.status(201).json(createSuccessResponse(newCourse));
});

// GET /terms/{termId}/courses - List courses in the term
app.get('/terms/:termId/courses', authenticate, (req, res) => {
  const { termId } = req.params;
  const { limit = 50, offset = 0, state, professor_id } = req.query;

  if (!isValidUUID(termId)) {
    return res.status(400).json(createErrorResponse('ERR_INVALID_ID_FORMAT', 'Invalid term ID format'));
  }

  const term = terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse('ERR_TERM_NOT_FOUND', 'Term not found'));
  }

  let filteredCourses = Array.from(courses.values()).filter(course => course.term_id === termId);

  // Apply role-based filtering
  if (req.auth.userRole === 'STUDENT') {
    // Students only see published/active courses
    filteredCourses = filteredCourses.filter(course => 
      ['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)
    );
  } else if (req.auth.userRole === 'PROFESSOR') {
    // Professors see all their own courses and published courses by others
    filteredCourses = filteredCourses.filter(course => 
      course.professor_id === req.auth.userId || 
      ['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)
    );
  }
  // Registrars see all courses (no additional filtering)

  // Apply query filters
  if (state) {
    filteredCourses = filteredCourses.filter(course => course.state === state);
  }
  if (professor_id) {
    filteredCourses = filteredCourses.filter(course => course.professor_id === professor_id);
  }

  // Sort by creation time
  filteredCourses.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  // Apply pagination
  const total = filteredCourses.length;
  const paginatedCourses = filteredCourses.slice(parseInt(offset), parseInt(offset) + parseInt(limit));

  // Add derived fields based on role
  const enrichedCourses = paginatedCourses.map(course => {
    const enriched = { ...course };
    
    // Calculate enrolled and waitlisted counts
    const courseEnrollments = Array.from(enrollments.values()).filter(e => e.course_id === course.id);
    const enrolledCount = courseEnrollments.filter(e => e.state === 'ENROLLED').length;
    const waitlistCount = courseEnrollments.filter(e => e.state === 'WAITLISTED').length;
    
    enriched.available_seats = course.capacity - enrolledCount;
    
    if (req.auth.userRole === 'PROFESSOR' && course.professor_id === req.auth.userId) {
      enriched.enrolled_count = enrolledCount;
      enriched.waitlist_count = waitlistCount;
    } else if (req.auth.userRole === 'REGISTRAR') {
      enriched.enrolled_count = enrolledCount;
      enriched.waitlist_count = waitlistCount;
    }
    
    return enriched;
  });

  const response = createSuccessResponse(enrichedCourses, 'array');
  response.meta.total = total;
  response.meta.limit = parseInt(limit);
  response.meta.offset = parseInt(offset);

  res.json(response);
});

// More endpoints will be implemented...

app.listen(PORT, () => {
  console.log(`University Course Registration API running on port ${PORT}`);
});