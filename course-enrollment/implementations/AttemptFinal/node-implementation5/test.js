// Basic smoke test to verify the API works
const request = require('supertest');
const app = require('./server');

// Mock test data
const registrarHeaders = {
  'X-User-ID': 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'X-User-Role': 'REGISTRAR'
};

const professorHeaders = {
  'X-User-ID': 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 
  'X-User-Role': 'PROFESSOR'
};

const studentHeaders = {
  'X-User-ID': 'cccccccc-cccc-cccc-cccc-cccccccccccc',
  'X-User-Role': 'STUDENT'
};

async function runTests() {
  console.log('Running basic API tests...\n');

  try {
    // Test 1: Create a term
    console.log('1. Creating a term...');
    const termResponse = await request(app)
      .post('/terms')
      .set(registrarHeaders)
      .send({ name: 'Fall 2025' });
    
    if (termResponse.status !== 201) {
      throw new Error(`Expected 201, got ${termResponse.status}`);
    }
    
    const termId = termResponse.body.data.id;
    console.log(`✓ Term created with ID: ${termId}`);

    // Test 2: Open term for registration
    console.log('2. Opening term for registration...');
    const openResponse = await request(app)
      .patch(`/terms/${termId}:open-registration`)
      .set(registrarHeaders)
      .send({});
    
    if (openResponse.status !== 200) {
      throw new Error(`Expected 200, got ${openResponse.status}`);
    }
    console.log('✓ Term opened for registration');

    // Test 3: Create a course
    console.log('3. Creating a course...');
    const courseResponse = await request(app)
      .post(`/terms/${termId}/courses`)
      .set(professorHeaders)
      .send({
        code: 'CS101',
        title: 'Introduction to Computer Science',
        description: 'A foundational course in computer science',
        credits: 3,
        capacity: 25,
        delivery_mode: 'IN_PERSON',
        location: 'Room 101'
      });
    
    if (courseResponse.status !== 201) {
      throw new Error(`Expected 201, got ${courseResponse.status}`);
    }
    
    const courseId = courseResponse.body.data.id;
    console.log(`✓ Course created with ID: ${courseId}`);

    // Test 4: Publish the course
    console.log('4. Publishing the course...');
    const publishResponse = await request(app)
      .patch(`/terms/${termId}/courses/${courseId}:publish`)
      .set(professorHeaders)
      .send({});
    
    if (publishResponse.status !== 200) {
      throw new Error(`Expected 200, got ${publishResponse.status}`);
    }
    console.log('✓ Course published');

    // Test 5: Student enrolls in course
    console.log('5. Student enrolling in course...');
    const enrollResponse = await request(app)
      .post(`/terms/${termId}/courses/${courseId}/enrollments`)
      .set(studentHeaders)
      .send({});
    
    if (enrollResponse.status !== 201) {
      throw new Error(`Expected 201, got ${enrollResponse.status}`);
    }
    
    const enrollmentId = enrollResponse.body.data.id;
    console.log(`✓ Student enrolled with enrollment ID: ${enrollmentId}`);

    // Test 6: Get course details
    console.log('6. Getting course details...');
    const courseDetailsResponse = await request(app)
      .get(`/terms/${termId}/courses/${courseId}`)
      .set(studentHeaders);
    
    if (courseDetailsResponse.status !== 200) {
      throw new Error(`Expected 200, got ${courseDetailsResponse.status}`);
    }
    console.log('✓ Course details retrieved');

    console.log('\n🎉 All basic tests passed!');
    console.log('\nThe University Course Registration API is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Only run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };