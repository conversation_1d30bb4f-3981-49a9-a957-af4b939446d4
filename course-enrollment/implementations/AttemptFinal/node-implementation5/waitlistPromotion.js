const { 
  Course,
  Enrollment, 
  CourseSeatLedger,
  StudentTuitionLedger,
  ENROLLMENT_STATES,
  COST_PER_CREDIT,
  storage
} = require('./models');

function promoteFromWaitlist(courseId) {
  try {
    // Get course and seat ledger
    const course = Course.findById(courseId);
    if (!course) {
      console.error(`Course not found for waitlist promotion: ${courseId}`);
      return;
    }

    const seatLedger = CourseSeatLedger.findByCourseId(courseId);
    if (!seatLedger) {
      console.error(`Seat ledger not found for course: ${courseId}`);
      return;
    }

    // Continue promoting while we have available seats and waitlisted students
    while (seatLedger.seats_available > 0) {
      // Find the next waitlisted student (earliest by created_at)
      const nextStudent = Enrollment.getNextWaitlistedStudent(courseId);
      if (!nextStudent) {
        // No more waitlisted students
        break;
      }

      try {
        // Promote the student
        console.log(`Promoting student ${nextStudent.student_id} from waitlist for course ${courseId}`);
        
        // Update enrollment state
        nextStudent.update({ state: ENROLLMENT_STATES.ENROLLED });
        
        // Update seat ledger
        seatLedger.debit(1);
        
        // Charge tuition
        const tuitionLedger = StudentTuitionLedger.getOrCreate(nextStudent.student_id, course.term_id);
        tuitionLedger.credit(course.credits * COST_PER_CREDIT);
        
        // Log the promotion event
        storage.auditLog.push({
          event: 'waitlist_promoted',
          timestamp: new Date().toISOString(),
          enrollment_id: nextStudent.id,
          student_id: nextStudent.student_id,
          course_id: courseId,
          previous_state: ENROLLMENT_STATES.WAITLISTED,
          new_state: ENROLLMENT_STATES.ENROLLED
        });
        
      } catch (promotionError) {
        console.error(`Error promoting student ${nextStudent.student_id}:`, promotionError);
        // Continue with next student if one promotion fails
        break;
      }
    }
    
  } catch (error) {
    console.error('Error in waitlist promotion:', error);
  }
}

// Function to manually trigger waitlist promotion (useful for testing)
function triggerWaitlistPromotion(courseId) {
  promoteFromWaitlist(courseId);
}

module.exports = {
  promoteFromWaitlist,
  triggerWaitlistPromotion
};