const express = require('express');
const router = express.Router({ mergeParams: true });
const { middleware } = require('../middleware');
const { APIError } = require('../errorHandler');
const { createSuccessResponse, createPaginatedResponse, sanitizeForRole, applyPagination, sortByCreatedAt } = require('../utils');
const { 
  Term,
  Course, 
  Enrollment,
  CourseSeatLedger,
  TERM_STATES,
  COURSE_STATES,
  ENROLLMENT_STATES,
  DELIVERY_MODES,
  USER_ROLES,
  MAX_COURSES_PER_PROF,
  validateCourseCode
} = require('../models');

// POST /terms/:termId/courses - Create a new course
router.post('/',
  middleware.requireRole(USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR),
  middleware.validateUUIDs('termId'),
  middleware.rejectUnknownFields([
    'code', 'title', 'description', 'credits', 'capacity', 
    'professor_id', 'delivery_mode', 'location', 'online_link'
  ]),
  (req, res, next) => {
    try {
      const { termId } = req.params;
      const { 
        code, title, description, credits, capacity, 
        professor_id, delivery_mode, location, online_link 
      } = req.body;

      // Validate term exists
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      // Validate required fields
      if (!code || !title || !credits || !capacity || !delivery_mode) {
        throw new APIError('ERR_MISSING_REQUIRED_FIELD', 'Missing required fields');
      }

      // Validate course code format
      if (!validateCourseCode(code)) {
        throw new APIError('ERR_INVALID_COURSE_CODE', 'Course code must be 2-4 uppercase letters followed by 3 digits');
      }

      // Check code uniqueness within term
      const existingCourse = Course.findByTermAndCode(termId, code);
      if (existingCourse) {
        throw new APIError('ERR_COURSE_CODE_NOT_UNIQUE', 'Course code already exists in this term');
      }

      // Validate title length
      if (title.length > 100) {
        throw new APIError('ERR_INVALID_FIELD_LENGTH', 'Title cannot exceed 100 characters');
      }

      // Validate description length
      if (description && description.length > 1000) {
        throw new APIError('ERR_INVALID_FIELD_LENGTH', 'Description cannot exceed 1000 characters');
      }

      // Validate credits
      if (!Number.isInteger(credits) || credits < 1 || credits > 5) {
        throw new APIError('ERR_INVALID_CREDITS', 'Credits must be an integer between 1 and 5');
      }

      // Validate capacity
      if (!Number.isInteger(capacity) || capacity < 1 || capacity > 500) {
        throw new APIError('ERR_INVALID_CAPACITY', 'Capacity must be an integer between 1 and 500');
      }

      // Validate delivery mode
      if (!Object.values(DELIVERY_MODES).includes(delivery_mode)) {
        throw new APIError('ERR_INVALID_ENUM_VALUE', 'Invalid delivery mode');
      }

      // Validate delivery mode conditional fields
      if (delivery_mode === DELIVERY_MODES.IN_PERSON) {
        if (!location) {
          throw new APIError('ERR_CONDITIONAL_FIELD_REQUIRED', 'Location is required for in-person courses');
        }
        if (online_link) {
          throw new APIError('ERR_FIELD_CONFLICT', 'Online link not allowed for in-person courses');
        }
      } else if (delivery_mode === DELIVERY_MODES.ONLINE) {
        if (!online_link) {
          throw new APIError('ERR_CONDITIONAL_FIELD_REQUIRED', 'Online link is required for online courses');
        }
        if (location) {
          throw new APIError('ERR_FIELD_CONFLICT', 'Location not allowed for online courses');
        }
      } else if (delivery_mode === DELIVERY_MODES.HYBRID) {
        if (!location && !online_link) {
          throw new APIError('ERR_CONDITIONAL_FIELD_REQUIRED', 'Either location or online link required for hybrid courses');
        }
      }

      // Determine professor ID
      let instructorId;
      if (req.user.role === USER_ROLES.REGISTRAR) {
        if (!professor_id) {
          throw new APIError('ERR_MISSING_REQUIRED_FIELD', 'Professor ID is required when creating as Registrar');
        }
        instructorId = professor_id;
        // Note: In a real system, we'd validate that professor_id exists and has PROFESSOR role
      } else if (req.user.role === USER_ROLES.PROFESSOR) {
        if (professor_id && professor_id !== req.user.id) {
          throw new APIError('ERR_FIELD_CONFLICT', 'Professors can only create courses for themselves');
        }
        instructorId = req.user.id;
      }

      // Check professor course limit
      const professorCourseCount = Course.countByProfessorAndTerm(instructorId, termId);
      if (professorCourseCount >= MAX_COURSES_PER_PROF) {
        throw new APIError('ERR_MAX_COURSES_REACHED', 'Professor has reached maximum course limit');
      }

      // Create course
      const course = Course.create(termId, {
        code,
        title,
        description,
        credits,
        capacity,
        professor_id: instructorId,
        delivery_mode,
        location,
        online_link
      });

      res.status(201).json(createSuccessResponse(course));
    } catch (error) {
      next(error);
    }
  }
);

// GET /terms/:termId/courses - List courses in term
router.get('/',
  middleware.validateUUIDs('termId'),
  middleware.validatePagination,
  (req, res, next) => {
    try {
      const { termId } = req.params;
      const { state, professor_id } = req.query;

      // Validate term exists
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      let courses = Course.findByTermId(termId);

      // Apply filters
      if (state) {
        if (!Object.values(COURSE_STATES).includes(state)) {
          throw new APIError('ERR_INVALID_ENUM_VALUE', 'Invalid course state');
        }
        courses = courses.filter(course => course.state === state);
      }

      if (professor_id) {
        courses = courses.filter(course => course.professor_id === professor_id);
      }

      // Role-based filtering
      if (req.user.role === USER_ROLES.STUDENT) {
        // Students only see published courses (OPEN, IN_PROGRESS, COMPLETED)
        courses = courses.filter(course => 
          [COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)
        );
      } else if (req.user.role === USER_ROLES.PROFESSOR) {
        // Professors see all their own courses, plus published courses from others
        courses = courses.filter(course => 
          course.professor_id === req.user.id || 
          [COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)
        );
      }
      // Registrars see all courses

      // Sort by creation time
      courses = sortByCreatedAt(courses);

      // Add derived fields and sanitize
      const enrichedCourses = courses.map(course => {
        const enriched = {
          ...course,
          enrolled_count: course.getEnrolledCount(),
          waitlist_count: course.getWaitlistCount(),
          available_seats: course.getAvailableSeats()
        };

        // Sanitize based on role
        const isOwn = req.user.role === USER_ROLES.PROFESSOR && course.professor_id === req.user.id;
        return sanitizeForRole(enriched, req.user.role, { type: 'course', isOwn });
      }).filter(course => course !== null);

      // Apply pagination
      const paginatedCourses = applyPagination(enrichedCourses, req.pagination);

      res.json(createPaginatedResponse(paginatedCourses, req.pagination, enrichedCourses.length));
    } catch (error) {
      next(error);
    }
  }
);

// GET /terms/:termId/courses/:courseId - Get course details
router.get('/:courseId',
  middleware.validateUUIDs('termId', 'courseId'),
  (req, res, next) => {
    try {
      const { termId, courseId } = req.params;

      // Validate term exists
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      const course = Course.findById(courseId);
      if (!course || course.term_id !== termId) {
        throw new APIError('ERR_COURSE_NOT_FOUND', 'Course not found');
      }

      // Role-based visibility
      const isOwn = req.user.role === USER_ROLES.PROFESSOR && course.professor_id === req.user.id;
      const isRegistrar = req.user.role === USER_ROLES.REGISTRAR;
      
      if (req.user.role === USER_ROLES.STUDENT) {
        // Students can only see published courses or courses they're involved in
        if (![COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)) {
          // Check if student is enrolled/waitlisted
          const enrollment = Enrollment.findByStudentAndCourse(req.user.id, courseId);
          if (!enrollment) {
            throw new APIError('ERR_COURSE_NOT_FOUND', 'Course not found');
          }
        }
      } else if (req.user.role === USER_ROLES.PROFESSOR && !isOwn) {
        // Professors can only see their own unpublished courses
        if (![COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)) {
          throw new APIError('ERR_COURSE_NOT_FOUND', 'Course not found');
        }
      }

      // Enrich with derived fields
      const enrichedCourse = {
        ...course,
        enrolled_count: course.getEnrolledCount(),
        waitlist_count: course.getWaitlistCount(),
        available_seats: course.getAvailableSeats()
      };

      // Add student's enrollment status if student
      if (req.user.role === USER_ROLES.STUDENT) {
        const enrollment = Enrollment.findByStudentAndCourse(req.user.id, courseId);
        enrichedCourse.is_enrolled = enrollment?.state === ENROLLMENT_STATES.ENROLLED || false;
        enrichedCourse.is_waitlisted = enrollment?.state === ENROLLMENT_STATES.WAITLISTED || false;
      }

      // Add enrollments for instructor/registrar
      if (isOwn || isRegistrar) {
        const enrollments = Enrollment.findByCourseId(courseId);
        enrichedCourse.enrollments = enrollments;
      }

      // Sanitize based on role
      const sanitized = sanitizeForRole(enrichedCourse, req.user.role, { type: 'course', isOwn });

      res.json(createSuccessResponse(sanitized));
    } catch (error) {
      next(error);
    }
  }
);

// PATCH /terms/:termId/courses/:courseId:publish - Publish a course
router.patch('/:courseId\\:publish',
  middleware.requireRole(USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR),
  middleware.validateUUIDs('termId', 'courseId'),
  middleware.rejectUnknownFields(['revision']),
  (req, res, next) => {
    try {
      const { termId, courseId } = req.params;
      const { revision } = req.body;

      // Validate term exists
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      const course = Course.findById(courseId);
      if (!course || course.term_id !== termId) {
        throw new APIError('ERR_COURSE_NOT_FOUND', 'Course not found');
      }

      // Check authorization
      if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
        throw new APIError('ERR_NOT_INSTRUCTOR', 'Only the course instructor can publish this course');
      }

      // Check revision for optimistic locking
      if (revision !== undefined && course.revision !== revision) {
        throw new APIError('ERR_REV_CONFLICT', 'Course has been modified by another user');
      }

      // Validate course state
      if (course.state !== COURSE_STATES.DRAFT) {
        throw new APIError('ERR_COURSE_WRONG_STATE', 'Course must be in DRAFT state to publish');
      }

      // Validate term state
      if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
        throw new APIError('ERR_TERM_NOT_ACTIVE', 'Term must be open for enrollment to publish courses');
      }

      // Update course state
      course.update({ state: COURSE_STATES.OPEN });

      // Initialize seat ledger
      CourseSeatLedger.create(courseId, course.capacity);

      res.json(createSuccessResponse(course));
    } catch (error) {
      next(error);
    }
  }
);

// PATCH /terms/:termId/courses/:courseId:cancel - Cancel a course
router.patch('/:courseId\\:cancel',
  middleware.requireRole(USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR),
  middleware.validateUUIDs('termId', 'courseId'),
  middleware.rejectUnknownFields(['revision']),
  (req, res, next) => {
    try {
      const { termId, courseId } = req.params;
      const { revision } = req.body;

      // Validate term exists
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      const course = Course.findById(courseId);
      if (!course || course.term_id !== termId) {
        throw new APIError('ERR_COURSE_NOT_FOUND', 'Course not found');
      }

      // Check authorization
      if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
        throw new APIError('ERR_NOT_INSTRUCTOR', 'Only the course instructor can cancel this course');
      }

      // Check revision for optimistic locking
      if (revision !== undefined && course.revision !== revision) {
        throw new APIError('ERR_REV_CONFLICT', 'Course has been modified by another user');
      }

      // Validate course state
      if ([COURSE_STATES.COMPLETED, COURSE_STATES.CANCELLED].includes(course.state)) {
        throw new APIError('ERR_COURSE_WRONG_STATE', 'Cannot cancel a completed or already cancelled course');
      }

      // Update course state
      course.update({ state: COURSE_STATES.CANCELLED });

      // Drop all enrollments and handle refunds
      const { StudentTuitionLedger, COST_PER_CREDIT } = require('../models');
      const enrollments = Enrollment.findByCourseId(courseId);
      
      enrollments.forEach(enrollment => {
        if ([ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED].includes(enrollment.state)) {
          const previousState = enrollment.state;
          enrollment.update({ state: ENROLLMENT_STATES.DROPPED });
          
          // Refund tuition for enrolled students
          if (previousState === ENROLLMENT_STATES.ENROLLED) {
            const tuitionLedger = StudentTuitionLedger.getOrCreate(enrollment.student_id, termId);
            const refundAmount = course.credits * COST_PER_CREDIT;
            tuitionLedger.debit(refundAmount);
          }
        }
      });

      res.json(createSuccessResponse(course));
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;