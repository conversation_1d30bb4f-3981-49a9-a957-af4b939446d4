const express = require('express');
const router = express.Router({ mergeParams: true });
const { middleware } = require('../middleware');
const { APIError } = require('../errorHandler');
const { createSuccessResponse, createPaginatedResponse, sanitizeForRole, applyPagination, sortByCreatedAt, calculateStudentCredits } = require('../utils');
const { promoteFromWaitlist } = require('../waitlistPromotion');
const { 
  Term,
  Course, 
  Enrollment,
  CourseSeatLedger,
  StudentTuitionLedger,
  TERM_STATES,
  COURSE_STATES,
  ENROLLMENT_STATES,
  USER_ROLES,
  MAX_CREDITS_PER_TERM,
  MAX_DROP_COUNT_PER_TERM,
  COST_PER_CREDIT,
  DROP_PENALTY_FEE
} = require('../models');

// POST /terms/:termId/courses/:courseId/enrollments - Enroll in a course
router.post('/',
  middleware.requireRole(USER_ROLES.STUDENT, USER_ROLES.REGISTRAR),
  middleware.validateUUIDs('termId', 'courseId'),
  middleware.rejectUnknownFields(['student_id']),
  (req, res, next) => {
    try {
      const { termId, courseId } = req.params;
      const { student_id } = req.body;

      // Validate term exists and is active
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
        throw new APIError('ERR_REGISTRATION_CLOSED', 'Registration is not open for this term');
      }

      // Validate course exists and is open
      const course = Course.findById(courseId);
      if (!course || course.term_id !== termId) {
        throw new APIError('ERR_COURSE_NOT_FOUND', 'Course not found');
      }

      if (course.state !== COURSE_STATES.OPEN) {
        throw new APIError('ERR_COURSE_WRONG_STATE', 'Course is not open for enrollment');
      }

      // Determine student ID
      let studentId;
      if (req.user.role === USER_ROLES.STUDENT) {
        if (student_id && student_id !== req.user.id) {
          throw new APIError('ERR_FORBIDDEN', 'Students can only enroll themselves');
        }
        studentId = req.user.id;
      } else if (req.user.role === USER_ROLES.REGISTRAR) {
        if (!student_id) {
          throw new APIError('ERR_MISSING_REQUIRED_FIELD', 'Student ID is required when enrolling as Registrar');
        }
        studentId = student_id;
        // Note: In a real system, we'd validate that student_id exists and has STUDENT role
      }

      // Check for duplicate enrollment
      const existingEnrollment = Enrollment.findByStudentAndCourse(studentId, courseId);
      if (existingEnrollment && [ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED].includes(existingEnrollment.state)) {
        throw new APIError('ERR_ALREADY_ENROLLED', 'Student is already enrolled or waitlisted in this course');
      }

      // Check credit limit for students (not for Registrar overrides)
      if (req.user.role === USER_ROLES.STUDENT) {
        const currentCredits = calculateStudentCredits(studentId, termId);
        if (currentCredits + course.credits > MAX_CREDITS_PER_TERM) {
          throw new APIError('ERR_CREDIT_LIMIT_EXCEEDED', 'Enrollment would exceed credit limit');
        }
      }

      // Check seat availability
      const seatLedger = CourseSeatLedger.findByCourseId(courseId);
      let enrollmentState;
      
      if (seatLedger && seatLedger.seats_available > 0) {
        // Seat available - enroll student
        enrollmentState = ENROLLMENT_STATES.ENROLLED;
        
        // Update ledgers
        seatLedger.debit(1);
        const tuitionLedger = StudentTuitionLedger.getOrCreate(studentId, termId);
        tuitionLedger.credit(course.credits * COST_PER_CREDIT);
      } else {
        // No seat available - waitlist student
        enrollmentState = ENROLLMENT_STATES.WAITLISTED;
      }

      // Create enrollment
      const enrollment = Enrollment.create(courseId, studentId, enrollmentState);

      res.status(201).json(createSuccessResponse(enrollment));
    } catch (error) {
      next(error);
    }
  }
);

// GET /terms/:termId/courses/:courseId/enrollments - List enrollments for a course
router.get('/',
  middleware.requireRole(USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR),
  middleware.validateUUIDs('termId', 'courseId'),
  middleware.validatePagination,
  (req, res, next) => {
    try {
      const { termId, courseId } = req.params;
      const { state } = req.query;

      // Validate term exists
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      // Validate course exists
      const course = Course.findById(courseId);
      if (!course || course.term_id !== termId) {
        throw new APIError('ERR_COURSE_NOT_FOUND', 'Course not found');
      }

      // Check authorization
      if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
        throw new APIError('ERR_NOT_INSTRUCTOR', 'Only the course instructor can view enrollments');
      }

      let enrollments = Enrollment.findByCourseId(courseId);

      // Apply state filter
      if (state) {
        if (!Object.values(ENROLLMENT_STATES).includes(state)) {
          throw new APIError('ERR_INVALID_ENUM_VALUE', 'Invalid enrollment state');
        }
        enrollments = enrollments.filter(enrollment => enrollment.state === state);
      }

      // Sort by creation time (waitlist order)
      enrollments = sortByCreatedAt(enrollments);

      // Apply pagination
      const paginatedEnrollments = applyPagination(enrollments, req.pagination);

      res.json(createPaginatedResponse(paginatedEnrollments, req.pagination, enrollments.length));
    } catch (error) {
      next(error);
    }
  }
);

// GET /terms/:termId/courses/:courseId/enrollments/:enrollmentId - Get specific enrollment
router.get('/:enrollmentId',
  middleware.validateUUIDs('termId', 'courseId', 'enrollmentId'),
  (req, res, next) => {
    try {
      const { termId, courseId, enrollmentId } = req.params;

      // Validate term exists
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      // Validate course exists
      const course = Course.findById(courseId);
      if (!course || course.term_id !== termId) {
        throw new APIError('ERR_COURSE_NOT_FOUND', 'Course not found');
      }

      // Validate enrollment exists and belongs to course
      const enrollment = Enrollment.findById(enrollmentId);
      if (!enrollment || enrollment.course_id !== courseId) {
        throw new APIError('ERR_ENROLLMENT_NOT_FOUND', 'Enrollment not found');
      }

      // Check authorization
      const canAccess = 
        req.user.role === USER_ROLES.REGISTRAR ||
        (req.user.role === USER_ROLES.PROFESSOR && course.professor_id === req.user.id) ||
        (req.user.role === USER_ROLES.STUDENT && enrollment.student_id === req.user.id);

      if (!canAccess) {
        throw new APIError('ERR_FORBIDDEN', 'Access denied to this enrollment');
      }

      // Sanitize based on role
      const isOwn = req.user.role === USER_ROLES.STUDENT && enrollment.student_id === req.user.id;
      const sanitized = sanitizeForRole(enrollment, req.user.role, { type: 'enrollment', isOwn, canAccess });

      if (!sanitized) {
        throw new APIError('ERR_FORBIDDEN', 'Access denied to this enrollment');
      }

      res.json(createSuccessResponse(sanitized));
    } catch (error) {
      next(error);
    }
  }
);

// PATCH /terms/:termId/courses/:courseId/enrollments/:enrollmentId:drop - Drop enrollment
router.patch('/:enrollmentId\\:drop',
  middleware.validateUUIDs('termId', 'courseId', 'enrollmentId'),
  middleware.rejectUnknownFields(['revision']),
  (req, res, next) => {
    try {
      const { termId, courseId, enrollmentId } = req.params;
      const { revision } = req.body;

      // Validate term exists and is active
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      if (term.state === TERM_STATES.CONCLUDED) {
        throw new APIError('ERR_TERM_NOT_ACTIVE', 'Cannot drop from a concluded term');
      }

      // Validate course exists
      const course = Course.findById(courseId);
      if (!course || course.term_id !== termId) {
        throw new APIError('ERR_COURSE_NOT_FOUND', 'Course not found');
      }

      // Validate enrollment exists and belongs to course
      const enrollment = Enrollment.findById(enrollmentId);
      if (!enrollment || enrollment.course_id !== courseId) {
        throw new APIError('ERR_ENROLLMENT_NOT_FOUND', 'Enrollment not found');
      }

      // Check authorization
      const canDrop = 
        req.user.role === USER_ROLES.REGISTRAR ||
        (req.user.role === USER_ROLES.PROFESSOR && course.professor_id === req.user.id) ||
        (req.user.role === USER_ROLES.STUDENT && enrollment.student_id === req.user.id);

      if (!canDrop) {
        throw new APIError('ERR_FORBIDDEN', 'Access denied to drop this enrollment');
      }

      // Check revision for optimistic locking
      if (revision !== undefined && enrollment.revision !== revision) {
        throw new APIError('ERR_REV_CONFLICT', 'Enrollment has been modified by another user');
      }

      // Validate enrollment state
      if (![ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED].includes(enrollment.state)) {
        throw new APIError('ERR_ENROLLMENT_WRONG_STATE', 'Cannot drop an enrollment that is already dropped or completed');
      }

      // Check drop count limit for students
      if (req.user.role === USER_ROLES.STUDENT && enrollment.student_id === req.user.id) {
        const currentDropCount = Enrollment.countDropsByStudentAndTerm(enrollment.student_id, termId);
        if (currentDropCount >= MAX_DROP_COUNT_PER_TERM) {
          throw new APIError('ERR_TOO_MANY_DROPS', 'Student has reached maximum drop limit');
        }
      }

      const previousState = enrollment.state;
      
      // Update enrollment state
      enrollment.update({ state: ENROLLMENT_STATES.DROPPED });

      // Handle ledger updates based on previous state
      if (previousState === ENROLLMENT_STATES.ENROLLED) {
        // Free up seat and refund tuition
        const seatLedger = CourseSeatLedger.findByCourseId(courseId);
        if (seatLedger) {
          seatLedger.credit(1);
        }
        
        const tuitionLedger = StudentTuitionLedger.findByStudentAndTerm(enrollment.student_id, termId);
        if (tuitionLedger) {
          const refundAmount = course.credits * COST_PER_CREDIT;
          tuitionLedger.debit(refundAmount);
        }

        // Trigger waitlist promotion
        setImmediate(() => {
          promoteFromWaitlist(courseId);
        });
      }

      // Apply drop penalty for student's third drop
      if (req.user.role === USER_ROLES.STUDENT && enrollment.student_id === req.user.id) {
        const newDropCount = Enrollment.countDropsByStudentAndTerm(enrollment.student_id, termId);
        if (newDropCount === MAX_DROP_COUNT_PER_TERM) {
          const tuitionLedger = StudentTuitionLedger.getOrCreate(enrollment.student_id, termId);
          tuitionLedger.credit(DROP_PENALTY_FEE);
        }
      }

      res.json(createSuccessResponse(enrollment));
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;