const express = require('express');
const router = express.Router({ mergeParams: true });
const { middleware } = require('../middleware');
const { APIError } = require('../errorHandler');
const { createSuccessResponse } = require('../utils');
const { 
  Term,
  StudentTuitionLedger,
  USER_ROLES
} = require('../models');

// POST /terms/:termId/students/:studentId:pay - Record a tuition payment
router.post('\\:pay',
  middleware.requireRole(USER_ROLES.STUDENT, USER_ROLES.REGISTRAR),
  middleware.validateUUIDs('termId', 'studentId'),
  middleware.rejectUnknownFields(['amount']),
  (req, res, next) => {
    try {
      const { termId, studentId } = req.params;
      const { amount } = req.body;

      // Validate term exists
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      // Check authorization
      if (req.user.role === USER_ROLES.STUDENT && studentId !== req.user.id) {
        throw new APIError('ERR_FORBIDDEN', 'Students can only pay their own tuition');
      }
      // Note: In a real system, we'd validate that studentId exists and has STUDENT role for Registrar

      // Validate amount
      if (!amount || !Number.isInteger(amount) || amount <= 0) {
        throw new APIError('ERR_INVALID_PAYMENT_AMOUNT', 'Payment amount must be a positive integer (cents)');
      }

      // Get or create tuition ledger
      const tuitionLedger = StudentTuitionLedger.getOrCreate(studentId, termId);

      // Check for overpayment
      if (amount > tuitionLedger.balance_cents) {
        throw new APIError('ERR_OVERPAY_NOT_ALLOWED', 'Payment amount exceeds outstanding balance');
      }

      // Process payment (debit ledger)
      const previousBalance = tuitionLedger.balance_cents;
      tuitionLedger.debit(amount);
      const newBalance = tuitionLedger.balance_cents;

      // Create response with payment details
      const paymentResponse = {
        student_id: studentId,
        term_id: termId,
        amount_paid: amount,
        previous_balance: previousBalance,
        new_balance: newBalance,
        payment_timestamp: new Date().toISOString()
      };

      res.json(createSuccessResponse(paymentResponse));
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;