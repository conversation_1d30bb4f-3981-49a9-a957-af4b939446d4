const express = require('express');
const router = express.Router();
const { middleware } = require('../middleware');
const { APIError, createErrorResponse } = require('../errorHandler');
const { createSuccessResponse } = require('../utils');
const { 
  Term, 
  Course, 
  Enrollment,
  TERM_STATES, 
  COURSE_STATES,
  ENROLLMENT_STATES,
  USER_ROLES 
} = require('../models');

// POST /terms - Create a new academic term
router.post('/', 
  middleware.requireRole(USER_ROLES.REGISTRAR),
  middleware.rejectUnknownFields(['name']),
  (req, res, next) => {
    try {
      const { name } = req.body;

      // Validate required field
      if (!name || typeof name !== 'string' || name.trim().length === 0) {
        throw new APIError('ERR_MISSING_REQUIRED_FIELD', 'Term name is required');
      }

      // Check uniqueness
      const existingTerm = Term.findByName(name.trim());
      if (existingTerm) {
        throw new APIError('ERR_TERM_NAME_NOT_UNIQUE', 'Term name already exists');
      }

      // Create term
      const term = Term.create(name.trim(), req.user.id);
      
      res.status(201).json(createSuccessResponse(term));
    } catch (error) {
      next(error);
    }
  }
);

// GET /terms/:termId - Retrieve details of an academic term
router.get('/:termId',
  middleware.validateUUIDs('termId'),
  (req, res, next) => {
    try {
      const { termId } = req.params;
      
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      res.json(createSuccessResponse(term));
    } catch (error) {
      next(error);
    }
  }
);

// PATCH /terms/:termId:open-registration - Open student registration
router.patch('/:termId\\:open-registration',
  middleware.requireRole(USER_ROLES.REGISTRAR),
  middleware.validateUUIDs('termId'),
  middleware.rejectUnknownFields(['revision']),
  (req, res, next) => {
    try {
      const { termId } = req.params;
      const { revision } = req.body;
      
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      // Check revision for optimistic locking
      if (revision !== undefined && term.revision !== revision) {
        throw new APIError('ERR_REV_CONFLICT', 'Term has been modified by another user');
      }

      // Validate current state
      if (term.state !== TERM_STATES.PLANNING) {
        throw new APIError('ERR_TERM_NOT_ACTIVE', 'Term must be in PLANNING state to open registration');
      }

      // Update term state
      term.update({ state: TERM_STATES.ENROLLMENT_OPEN });

      res.json(createSuccessResponse(term));
    } catch (error) {
      next(error);
    }
  }
);

// PATCH /terms/:termId:close-registration - Close enrollment period
router.patch('/:termId\\:close-registration',
  middleware.requireRole(USER_ROLES.REGISTRAR),
  middleware.validateUUIDs('termId'),
  middleware.rejectUnknownFields(['revision']),
  (req, res, next) => {
    try {
      const { termId } = req.params;
      const { revision } = req.body;
      
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      // Check revision for optimistic locking
      if (revision !== undefined && term.revision !== revision) {
        throw new APIError('ERR_REV_CONFLICT', 'Term has been modified by another user');
      }

      // Validate current state
      if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
        throw new APIError('ERR_TERM_NOT_ACTIVE', 'Term must be in ENROLLMENT_OPEN state to close registration');
      }

      // Update term state
      term.update({ state: TERM_STATES.ENROLLMENT_CLOSED });

      // Cascade update: Move all OPEN courses to IN_PROGRESS
      const courses = Course.findByTermId(termId);
      courses.forEach(course => {
        if (course.state === COURSE_STATES.OPEN) {
          course.update({ state: COURSE_STATES.IN_PROGRESS });
        }
      });

      res.json(createSuccessResponse(term));
    } catch (error) {
      next(error);
    }
  }
);

// PATCH /terms/:termId:conclude - Conclude the term
router.patch('/:termId\\:conclude',
  middleware.requireRole(USER_ROLES.REGISTRAR),
  middleware.validateUUIDs('termId'),
  middleware.rejectUnknownFields(['revision']),
  (req, res, next) => {
    try {
      const { termId } = req.params;
      const { revision } = req.body;
      
      const term = Term.findById(termId);
      if (!term) {
        throw new APIError('ERR_TERM_NOT_FOUND', 'Term not found');
      }

      // Check revision for optimistic locking
      if (revision !== undefined && term.revision !== revision) {
        throw new APIError('ERR_REV_CONFLICT', 'Term has been modified by another user');
      }

      // Validate current state
      if (term.state !== TERM_STATES.ENROLLMENT_CLOSED) {
        throw new APIError('ERR_TERM_NOT_ACTIVE', 'Term must be in ENROLLMENT_CLOSED state to conclude');
      }

      // Update term state
      term.update({ state: TERM_STATES.CONCLUDED });

      // Cascade update: Complete all IN_PROGRESS courses and finalize enrollments
      const courses = Course.findByTermId(termId);
      courses.forEach(course => {
        if (course.state === COURSE_STATES.IN_PROGRESS) {
          course.update({ state: COURSE_STATES.COMPLETED });
        }
        
        // Finalize enrollments for this course
        const enrollments = Enrollment.findByCourseId(course.id);
        enrollments.forEach(enrollment => {
          if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
            enrollment.update({ state: ENROLLMENT_STATES.COMPLETED });
          } else if (enrollment.state === ENROLLMENT_STATES.WAITLISTED) {
            enrollment.update({ state: ENROLLMENT_STATES.DROPPED });
          }
        });
      });

      res.json(createSuccessResponse(term));
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;