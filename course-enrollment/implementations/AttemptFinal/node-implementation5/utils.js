const { generateRequestId, getCurrentTimestamp } = require('./models');

function createSuccessResponse(data, responseType = 'object') {
  return {
    meta: {
      api_request_id: generateRequestId(),
      api_request_timestamp: getCurrentTimestamp()
    },
    response_type: responseType,
    data: data || {}
  };
}

function createPaginatedResponse(data, pagination, total = null) {
  const response = createSuccessResponse(data, 'array');
  response.meta.pagination = {
    limit: pagination.limit,
    offset: pagination.offset,
    ...(total !== null && { total })
  };
  return response;
}

function sanitizeForRole(object, role, context = {}) {
  if (!object) return object;
  
  // Create a copy to avoid mutating the original
  const sanitized = { ...object };
  
  // Remove sensitive fields based on role and context
  if (role === 'STUDENT') {
    // Students see limited information
    if (context.type === 'course') {
      delete sanitized.enrolled_count;
      delete sanitized.waitlist_count;
      if (!context.isOwn) {
        delete sanitized.enrollments;
      }
    }
    if (context.type === 'enrollment' && !context.isOwn) {
      return null; // Students can't see other students' enrollments
    }
  }
  
  if (role === 'PROFESSOR') {
    // Professors see their own courses with full details, others with limited info
    if (context.type === 'course' && !context.isOwn) {
      delete sanitized.enrollments;
      delete sanitized.enrolled_count;
      delete sanitized.waitlist_count;
    }
    if (context.type === 'enrollment' && !context.canAccess) {
      return null;
    }
  }
  
  // Registrar sees everything - no sanitization needed
  
  return sanitized;
}

function applyPagination(array, pagination) {
  const { limit, offset } = pagination;
  return array.slice(offset, offset + limit);
}

function sortByCreatedAt(array, ascending = true) {
  return array.sort((a, b) => {
    const dateA = new Date(a.created_at);
    const dateB = new Date(b.created_at);
    return ascending ? dateA - dateB : dateB - dateA;
  });
}

function calculateStudentCredits(studentId, termId, excludeStates = []) {
  const { Enrollment, Course, ENROLLMENT_STATES } = require('./models');
  
  let totalCredits = 0;
  const enrollments = Enrollment.findByStudentAndTerm(studentId, termId);
  
  for (const enrollment of enrollments) {
    if (!excludeStates.includes(enrollment.state) && enrollment.state === ENROLLMENT_STATES.ENROLLED) {
      const course = Course.findById(enrollment.course_id);
      if (course) {
        totalCredits += course.credits;
      }
    }
  }
  
  return totalCredits;
}

module.exports = {
  createSuccessResponse,
  createPaginatedResponse,
  sanitizeForRole,
  applyPagination,
  sortByCreatedAt,
  calculateStudentCredits
};