# University Course Registration & Enrollment API

A complete Node.js implementation of the University Course Registration & Enrollment API based on the provided PRD.

## Features

- **Term Management**: Create and manage academic terms with state transitions
- **Course Management**: Create, publish, and cancel courses with role-based access
- **Enrollment System**: Student enrollment with waitlist support and automatic promotion
- **Financial Operations**: Tuition payment processing with ledger management
- **Role-Based Access Control**: Support for Student, Professor, and Registrar roles
- **Business Rules Enforcement**: Credit limits, drop limits, capacity management
- **Standard Response Format**: Consistent API responses with error handling

## Installation

```bash
npm install
```

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

The API will run on port 3000 by default.

## API Endpoints

### Authentication
All requests require the following headers:
- `X-User-ID`: UUID of the user
- `X-User-Role`: One of `STUDENT`, `PROFESSOR`, or `REGISTRAR`

### Term Management
- `POST /terms` - Create a new academic term (Registrar only)
- `GET /terms/{termId}` - Get term details
- `PATCH /terms/{termId}:open-registration` - Open enrollment (Registrar only)
- `PATCH /terms/{termId}:close-registration` - Close enrollment (Registrar only)
- `PATCH /terms/{termId}:conclude` - Conclude term (Registrar only)

### Course Management
- `POST /terms/{termId}/courses` - Create a course (Professor/Registrar)
- `GET /terms/{termId}/courses` - List courses (with role-based filtering)
- `GET /terms/{termId}/courses/{courseId}` - Get course details
- `PATCH /terms/{termId}/courses/{courseId}:publish` - Publish course (Professor/Registrar)
- `PATCH /terms/{termId}/courses/{courseId}:cancel` - Cancel course (Professor/Registrar)

### Enrollment Management
- `POST /terms/{termId}/courses/{courseId}/enrollments` - Enroll in course (Student/Registrar)
- `GET /terms/{termId}/courses/{courseId}/enrollments` - List enrollments (Professor/Registrar)
- `GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}` - Get enrollment details
- `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop` - Drop enrollment

### Financial Operations
- `POST /terms/{termId}/students/{studentId}:pay` - Record payment (Student/Registrar)

## Business Rules

### System Constants
- Max credits per term: 18
- Max courses per professor: 5
- Max drops per student per term: 3
- Cost per credit: $100.00 (10000 cents)
- Drop penalty fee: $50.00 (5000 cents)

### Term States
- `PLANNING` → `ENROLLMENT_OPEN` → `ENROLLMENT_CLOSED` → `CONCLUDED`

### Course States
- `DRAFT` → `OPEN` → `IN_PROGRESS` → `COMPLETED`
- Can transition to `CANCELLED` from any non-terminal state

### Enrollment States
- `ENROLLED` (has a seat)
- `WAITLISTED` (waiting for a seat)
- `DROPPED` (withdrawn)
- `COMPLETED` (finished the course)

## Data Storage

This implementation uses in-memory storage for simplicity. In a production environment, you would replace the storage layer with a proper database.

## Error Handling

The API returns standardized error responses with specific error codes as defined in the PRD. All errors include:
- `api_request_id`
- `api_request_timestamp`
- `error_id`
- `message`

## Testing

```bash
npm test
```

## Architecture

The implementation follows a modular structure:
- `models/` - Data models and business logic
- `routes/` - Express route handlers
- `middleware.js` - Authentication and validation middleware
- `errorHandler.js` - Centralized error handling
- `utils.js` - Utility functions
- `waitlistPromotion.js` - Waitlist promotion logic
- `server.js` - Main application entry point