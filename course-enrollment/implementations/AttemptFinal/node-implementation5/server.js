const express = require('express');
const uuid = require('uuid');
const { middleware } = require('./middleware');
const { errorHandler } = require('./errorHandler');
const termRoutes = require('./routes/terms');
const courseRoutes = require('./routes/courses');
const enrollmentRoutes = require('./routes/enrollments');
const paymentRoutes = require('./routes/payments');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(middleware.validateUserContext);
app.use(middleware.sanitizeInput);

// Routes
app.use('/terms', termRoutes);
app.use('/terms/:termId/courses', courseRoutes);
app.use('/terms/:termId/courses/:courseId/enrollments', enrollmentRoutes);
app.use('/terms/:termId/students/:studentId', paymentRoutes);

// Error handling
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`University Course Registration API running on port ${PORT}`);
});

module.exports = app;