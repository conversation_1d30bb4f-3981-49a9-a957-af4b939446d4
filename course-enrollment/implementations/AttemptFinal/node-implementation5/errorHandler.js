const { generateRequestId, getCurrentTimestamp } = require('./models');

const ERROR_CODES = {
  // 400 Bad Request
  ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER: { status: 400, message: 'Missing or invalid user context headers' },
  ERR_INVALID_ID_FORMAT: { status: 400, message: 'Invalid UUID format' },
  ERR_INVALID_COURSE_CODE: { status: 400, message: 'Invalid course code format' },
  ERR_INVALID_FIELD_LENGTH: { status: 400, message: 'Invalid field length' },
  ERR_INVALID_CREDITS: { status: 400, message: 'Invalid credits value' },
  ERR_INVALID_CAPACITY: { status: 400, message: 'Invalid capacity value' },
  ERR_INVALID_ENUM_VALUE: { status: 400, message: 'Invalid enum value' },
  ERR_MISSING_REQUIRED_FIELD: { status: 400, message: 'Missing required field' },
  ERR_CONDITIONAL_FIELD_REQUIRED: { status: 400, message: 'Conditional field required' },
  ERR_FIELD_CONFLICT: { status: 400, message: 'Field conflict' },
  ERR_UNKNOWN_FIELD: { status: 400, message: 'Unknown field' },

  // 403 Forbidden
  ERR_UNAUTHORIZED_ROLE: { status: 403, message: 'Unauthorized role' },
  ERR_NOT_INSTRUCTOR: { status: 403, message: 'Not the course instructor' },
  ERR_PERMISSION_DENIED: { status: 403, message: 'Permission denied' },
  ERR_FORBIDDEN: { status: 403, message: 'Forbidden' },

  // 404 Not Found
  ERR_TERM_NOT_FOUND: { status: 404, message: 'Term not found' },
  ERR_COURSE_NOT_FOUND: { status: 404, message: 'Course not found' },
  ERR_ENROLLMENT_NOT_FOUND: { status: 404, message: 'Enrollment not found' },
  ERR_STUDENT_NOT_FOUND: { status: 404, message: 'Student not found' },
  ERR_TERM_CLOSED: { status: 404, message: 'Term is closed' },

  // 409 Conflict
  ERR_COURSE_CODE_NOT_UNIQUE: { status: 409, message: 'Course code is not unique' },
  ERR_COURSE_WRONG_STATE: { status: 409, message: 'Course is in wrong state' },
  ERR_ENROLLMENT_WRONG_STATE: { status: 409, message: 'Enrollment is in wrong state' },
  ERR_TERM_NOT_ACTIVE: { status: 409, message: 'Term is not active' },
  ERR_REGISTRATION_CLOSED: { status: 409, message: 'Registration is closed' },
  ERR_COURSE_FULL: { status: 409, message: 'Course is full' },
  ERR_CAPACITY_EXCEEDED: { status: 409, message: 'Capacity exceeded' },
  ERR_ALREADY_ENROLLED: { status: 409, message: 'Already enrolled' },
  ERR_NOT_ENROLLED: { status: 409, message: 'Not enrolled' },
  ERR_MAX_COURSES_REACHED: { status: 409, message: 'Maximum courses reached' },
  ERR_CREDIT_LIMIT_EXCEEDED: { status: 409, message: 'Credit limit exceeded' },
  ERR_TOO_MANY_DROPS: { status: 409, message: 'Too many drops' },
  ERR_ILLEGAL_COURSE_STATE_TRANSITION: { status: 409, message: 'Illegal course state transition' },
  ERR_ILLEGAL_ENROLLMENT_STATE: { status: 409, message: 'Illegal enrollment state' },
  ERR_TERM_NAME_NOT_UNIQUE: { status: 409, message: 'Term name is not unique' },
  ERR_REV_CONFLICT: { status: 409, message: 'Revision conflict' },

  // 422 Unprocessable Entity
  ERR_INVALID_INSTRUCTOR: { status: 422, message: 'Invalid instructor' },
  ERR_OVERPAY_NOT_ALLOWED: { status: 422, message: 'Overpay not allowed' },
  ERR_INVALID_PAYMENT_AMOUNT: { status: 422, message: 'Invalid payment amount' },
  ERR_LEDGER_INVALID_OP: { status: 422, message: 'Invalid ledger operation' },

  // 402 Payment Required
  ERR_INSUFFICIENT_FUNDS: { status: 402, message: 'Insufficient funds' }
};

class APIError extends Error {
  constructor(errorId, customMessage = null) {
    const errorConfig = ERROR_CODES[errorId];
    if (!errorConfig) {
      throw new Error(`Unknown error ID: ${errorId}`);
    }
    
    super(customMessage || errorConfig.message);
    this.errorId = errorId;
    this.status = errorConfig.status;
    this.name = 'APIError';
  }
}

function createErrorResponse(errorId, customMessage = null) {
  const error = new APIError(errorId, customMessage);
  return {
    meta: {
      api_request_id: generateRequestId(),
      api_request_timestamp: getCurrentTimestamp()
    },
    response_type: 'error',
    data: {
      error_id: error.errorId,
      message: error.message
    }
  };
}

function errorHandler(err, req, res, next) {
  console.error(err);

  if (err instanceof APIError) {
    return res.status(err.status).json({
      meta: {
        api_request_id: generateRequestId(),
        api_request_timestamp: getCurrentTimestamp()
      },
      response_type: 'error',
      data: {
        error_id: err.errorId,
        message: err.message
      }
    });
  }

  // Default error handler
  return res.status(500).json({
    meta: {
      api_request_id: generateRequestId(),
      api_request_timestamp: getCurrentTimestamp()
    },
    response_type: 'error',
    data: {
      error_id: 'ERR_INTERNAL_SERVER_ERROR',
      message: 'Internal server error'
    }
  });
}

module.exports = { APIError, createErrorResponse, errorHandler };