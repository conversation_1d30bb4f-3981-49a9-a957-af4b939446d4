const { v4: uuidv4 } = require('uuid');

// Constants
const TERM_STATES = {
  PLANNING: 'PLANNING',
  ENROLLMENT_OPEN: 'ENROLLMENT_OPEN',
  ENROLLMENT_CLOSED: 'ENROLLMENT_CLOSED',
  CONCLUDED: 'CONCLUDED'
};

const COURSE_STATES = {
  DRAFT: 'DRAFT',
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

const ENROLLMENT_STATES = {
  ENROLLED: 'ENROLLED',
  WAITLISTED: 'WAITLISTED',
  DROPPED: 'DROPPED',
  COMPLETED: 'COMPLETED'
};

const DELIVERY_MODES = {
  IN_PERSON: 'IN_PERSON',
  ONLINE: 'ONLINE',
  HYBRID: 'HYBRID'
};

const USER_ROLES = {
  STUDENT: 'STUDENT',
  PROFESSOR: 'PROFESSOR',
  REGISTRAR: 'R<PERSON>ISTRAR'
};

// Business constants
const MAX_CREDITS_PER_TERM = 18;
const MAX_COURSES_PER_PROF = 5;
const MAX_DROP_COUNT_PER_TERM = 3;
const COST_PER_CREDIT = 10000; // $100.00 in cents
const DROP_PENALTY_FEE = 5000; // $50.00 in cents

// In-memory storage
const storage = {
  terms: new Map(),
  courses: new Map(),
  enrollments: new Map(),
  courseSeatLedgers: new Map(),
  studentTuitionLedgers: new Map(),
  auditLog: []
};

// Helper functions
function generateId() {
  return uuidv4();
}

function generateRequestId() {
  return 'req_' + Math.random().toString(36).substr(2, 16).toUpperCase();
}

function getCurrentTimestamp() {
  return new Date().toISOString();
}

function validateUUID(id) {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidRegex.test(id);
}

function validateCourseCode(code) {
  const codeRegex = /^[A-Z]{2,4}\d{3}$/;
  return codeRegex.test(code);
}

// Term model
class Term {
  constructor(name, createdBy) {
    this.id = generateId();
    this.name = name;
    this.state = TERM_STATES.PLANNING;
    this.created_by = createdBy;
    this.created_at = getCurrentTimestamp();
    this.revision = 0;
  }

  static create(name, createdBy) {
    const term = new Term(name, createdBy);
    storage.terms.set(term.id, term);
    return term;
  }

  static findById(id) {
    return storage.terms.get(id);
  }

  static findByName(name) {
    for (const term of storage.terms.values()) {
      if (term.name === name) {
        return term;
      }
    }
    return null;
  }

  update(updates) {
    Object.assign(this, updates);
    this.revision++;
    storage.terms.set(this.id, this);
    return this;
  }
}

// Course model
class Course {
  constructor(termId, code, title, description, credits, capacity, professorId, deliveryMode, location, onlineLink) {
    this.id = generateId();
    this.term_id = termId;
    this.code = code;
    this.title = title;
    this.description = description;
    this.credits = credits;
    this.capacity = capacity;
    this.professor_id = professorId;
    this.delivery_mode = deliveryMode;
    this.location = location;
    this.online_link = onlineLink;
    this.state = COURSE_STATES.DRAFT;
    this.created_at = getCurrentTimestamp();
    this.revision = 0;
  }

  static create(termId, data) {
    const course = new Course(
      termId,
      data.code,
      data.title,
      data.description,
      data.credits,
      data.capacity,
      data.professor_id,
      data.delivery_mode,
      data.location,
      data.online_link
    );
    storage.courses.set(course.id, course);
    return course;
  }

  static findById(id) {
    return storage.courses.get(id);
  }

  static findByTermId(termId) {
    const courses = [];
    for (const course of storage.courses.values()) {
      if (course.term_id === termId) {
        courses.push(course);
      }
    }
    return courses;
  }

  static findByTermAndCode(termId, code) {
    for (const course of storage.courses.values()) {
      if (course.term_id === termId && course.code === code) {
        return course;
      }
    }
    return null;
  }

  static countByProfessorAndTerm(professorId, termId) {
    let count = 0;
    for (const course of storage.courses.values()) {
      if (course.professor_id === professorId && course.term_id === termId) {
        count++;
      }
    }
    return count;
  }

  update(updates) {
    Object.assign(this, updates);
    this.revision++;
    storage.courses.set(this.id, this);
    return this;
  }

  getEnrolledCount() {
    let count = 0;
    for (const enrollment of storage.enrollments.values()) {
      if (enrollment.course_id === this.id && enrollment.state === ENROLLMENT_STATES.ENROLLED) {
        count++;
      }
    }
    return count;
  }

  getWaitlistCount() {
    let count = 0;
    for (const enrollment of storage.enrollments.values()) {
      if (enrollment.course_id === this.id && enrollment.state === ENROLLMENT_STATES.WAITLISTED) {
        count++;
      }
    }
    return count;
  }

  getAvailableSeats() {
    const ledger = storage.courseSeatLedgers.get(this.id);
    return ledger ? ledger.seats_available : this.capacity;
  }
}

// Enrollment model
class Enrollment {
  constructor(courseId, studentId, state = ENROLLMENT_STATES.ENROLLED) {
    this.id = generateId();
    this.course_id = courseId;
    this.student_id = studentId;
    this.state = state;
    this.created_at = getCurrentTimestamp();
    this.revision = 0;
  }

  static create(courseId, studentId, state) {
    const enrollment = new Enrollment(courseId, studentId, state);
    storage.enrollments.set(enrollment.id, enrollment);
    return enrollment;
  }

  static findById(id) {
    return storage.enrollments.get(id);
  }

  static findByCourseId(courseId) {
    const enrollments = [];
    for (const enrollment of storage.enrollments.values()) {
      if (enrollment.course_id === courseId) {
        enrollments.push(enrollment);
      }
    }
    return enrollments;
  }

  static findByStudentAndCourse(studentId, courseId) {
    for (const enrollment of storage.enrollments.values()) {
      if (enrollment.student_id === studentId && enrollment.course_id === courseId) {
        return enrollment;
      }
    }
    return null;
  }

  static findByStudentAndTerm(studentId, termId) {
    const enrollments = [];
    for (const enrollment of storage.enrollments.values()) {
      const course = Course.findById(enrollment.course_id);
      if (course && course.term_id === termId && enrollment.student_id === studentId) {
        enrollments.push(enrollment);
      }
    }
    return enrollments;
  }

  static getNextWaitlistedStudent(courseId) {
    let earliestEnrollment = null;
    for (const enrollment of storage.enrollments.values()) {
      if (enrollment.course_id === courseId && enrollment.state === ENROLLMENT_STATES.WAITLISTED) {
        if (!earliestEnrollment || new Date(enrollment.created_at) < new Date(earliestEnrollment.created_at)) {
          earliestEnrollment = enrollment;
        }
      }
    }
    return earliestEnrollment;
  }

  static countDropsByStudentAndTerm(studentId, termId) {
    let count = 0;
    for (const enrollment of storage.enrollments.values()) {
      const course = Course.findById(enrollment.course_id);
      if (course && course.term_id === termId && 
          enrollment.student_id === studentId && 
          enrollment.state === ENROLLMENT_STATES.DROPPED) {
        count++;
      }
    }
    return count;
  }

  update(updates) {
    Object.assign(this, updates);
    this.revision++;
    storage.enrollments.set(this.id, this);
    return this;
  }
}

// Course Seat Ledger
class CourseSeatLedger {
  constructor(courseId, capacity) {
    this.course_id = courseId;
    this.seats_available = capacity;
    this.capacity = capacity;
  }

  static create(courseId, capacity) {
    const ledger = new CourseSeatLedger(courseId, capacity);
    storage.courseSeatLedgers.set(courseId, ledger);
    return ledger;
  }

  static findByCourseId(courseId) {
    return storage.courseSeatLedgers.get(courseId);
  }

  debit(amount = 1) {
    if (this.seats_available - amount < 0) {
      throw new Error('Cannot debit below zero');
    }
    this.seats_available -= amount;
    storage.courseSeatLedgers.set(this.course_id, this);
    return this;
  }

  credit(amount = 1) {
    if (this.seats_available + amount > this.capacity) {
      throw new Error('Cannot credit above capacity');
    }
    this.seats_available += amount;
    storage.courseSeatLedgers.set(this.course_id, this);
    return this;
  }
}

// Student Tuition Ledger
class StudentTuitionLedger {
  constructor(studentId, termId) {
    this.student_id = studentId;
    this.term_id = termId;
    this.balance_cents = 0;
  }

  static create(studentId, termId) {
    const ledger = new StudentTuitionLedger(studentId, termId);
    const key = `${studentId}_${termId}`;
    storage.studentTuitionLedgers.set(key, ledger);
    return ledger;
  }

  static findByStudentAndTerm(studentId, termId) {
    const key = `${studentId}_${termId}`;
    return storage.studentTuitionLedgers.get(key);
  }

  static getOrCreate(studentId, termId) {
    let ledger = this.findByStudentAndTerm(studentId, termId);
    if (!ledger) {
      ledger = this.create(studentId, termId);
    }
    return ledger;
  }

  credit(amount) {
    this.balance_cents += amount;
    const key = `${this.student_id}_${this.term_id}`;
    storage.studentTuitionLedgers.set(key, this);
    return this;
  }

  debit(amount) {
    if (this.balance_cents - amount < 0) {
      throw new Error('Cannot debit below zero');
    }
    this.balance_cents -= amount;
    const key = `${this.student_id}_${this.term_id}`;
    storage.studentTuitionLedgers.set(key, this);
    return this;
  }
}

module.exports = {
  TERM_STATES,
  COURSE_STATES,
  ENROLLMENT_STATES,
  DELIVERY_MODES,
  USER_ROLES,
  MAX_CREDITS_PER_TERM,
  MAX_COURSES_PER_PROF,
  MAX_DROP_COUNT_PER_TERM,
  COST_PER_CREDIT,
  DROP_PENALTY_FEE,
  storage,
  generateId,
  generateRequestId,
  getCurrentTimestamp,
  validateUUID,
  validateCourseCode,
  Term,
  Course,
  Enrollment,
  CourseSeatLedger,
  StudentTuitionLedger
};