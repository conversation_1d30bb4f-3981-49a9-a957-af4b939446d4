const { USER_ROLES, validateUUID, generateRequestId, getCurrentTimestamp } = require('./models');

function validateUserContext(req, res, next) {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  if (!userId || !userRole) {
    return res.status(400).json({
      meta: {
        api_request_id: generateRequestId(),
        api_request_timestamp: getCurrentTimestamp()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
        message: 'Missing or invalid user context headers (X-User-ID and X-User-Role required)'
      }
    });
  }

  if (!validateUUID(userId)) {
    return res.status(400).json({
      meta: {
        api_request_id: generateRequestId(),
        api_request_timestamp: getCurrentTimestamp()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_INVALID_ID_FORMAT',
        message: 'Invalid UUID format for X-User-ID'
      }
    });
  }

  if (!Object.values(USER_ROLES).includes(userRole)) {
    return res.status(400).json({
      meta: {
        api_request_id: generateRequestId(),
        api_request_timestamp: getCurrentTimestamp()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_INVALID_ENUM_VALUE',
        message: 'Invalid user role. Must be STUDENT, PROFESSOR, or REGISTRAR'
      }
    });
  }

  req.user = {
    id: userId,
    role: userRole
  };

  next();
}

function sanitizeInput(req, res, next) {
  if (req.body && typeof req.body === 'object') {
    // Remove any fields that start with underscore or contain invalid characters
    const sanitized = {};
    for (const [key, value] of Object.entries(req.body)) {
      if (typeof key === 'string' && key.match(/^[a-zA-Z_][a-zA-Z0-9_]*$/)) {
        sanitized[key] = value;
      }
    }
    req.body = sanitized;
  }
  next();
}

function requireRole(...allowedRoles) {
  return (req, res, next) => {
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        meta: {
          api_request_id: generateRequestId(),
          api_request_timestamp: getCurrentTimestamp()
        },
        response_type: 'error',
        data: {
          error_id: 'ERR_UNAUTHORIZED_ROLE',
          message: `Access denied. Required role: ${allowedRoles.join(' or ')}`
        }
      });
    }
    next();
  };
}

function validateUUIDs(...paramNames) {
  return (req, res, next) => {
    for (const paramName of paramNames) {
      const value = req.params[paramName];
      if (value && !validateUUID(value)) {
        return res.status(400).json({
          meta: {
            api_request_id: generateRequestId(),
            api_request_timestamp: getCurrentTimestamp()
          },
          response_type: 'error',
          data: {
            error_id: 'ERR_INVALID_ID_FORMAT',
            message: `Invalid UUID format for parameter: ${paramName}`
          }
        });
      }
    }
    next();
  };
}

function validatePagination(req, res, next) {
  const limit = parseInt(req.query.limit) || 50;
  const offset = parseInt(req.query.offset) || 0;

  if (limit < 1 || limit > 100) {
    return res.status(400).json({
      meta: {
        api_request_id: generateRequestId(),
        api_request_timestamp: getCurrentTimestamp()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_INVALID_FIELD_LENGTH',
        message: 'Limit must be between 1 and 100'
      }
    });
  }

  if (offset < 0) {
    return res.status(400).json({
      meta: {
        api_request_id: generateRequestId(),
        api_request_timestamp: getCurrentTimestamp()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_INVALID_FIELD_LENGTH',
        message: 'Offset must be non-negative'
      }
    });
  }

  req.pagination = { limit, offset };
  next();
}

function rejectUnknownFields(allowedFields) {
  return (req, res, next) => {
    if (req.body && typeof req.body === 'object') {
      const unknownFields = Object.keys(req.body).filter(key => !allowedFields.includes(key));
      if (unknownFields.length > 0) {
        return res.status(400).json({
          meta: {
            api_request_id: generateRequestId(),
            api_request_timestamp: getCurrentTimestamp()
          },
          response_type: 'error',
          data: {
            error_id: 'ERR_UNKNOWN_FIELD',
            message: `Unknown fields: ${unknownFields.join(', ')}`
          }
        });
      }
    }
    next();
  };
}

const middleware = {
  validateUserContext,
  sanitizeInput,
  requireRole,
  validateUUIDs,
  validatePagination,
  rejectUnknownFields
};

module.exports = { middleware };