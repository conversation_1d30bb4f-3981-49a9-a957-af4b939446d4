{"totalTests": 174, "passedTests": 101, "failedTests": 73, "passPercentage": 58, "totalSuites": 8, "passedSuites": 0, "failedSuites": 8, "duration": 0, "success": false, "failedTestNames": ["should reject drop attempt when term is ENROLLMENT_CLOSED", "should allow student to drop after 3 admin drops without penalty", "should provide full course refund even if balance goes negative", "should allow professor to create a new course after cancelling one of their 5 courses", "should reject drop attempt by professor when term is ENROLLMENT_CLOSED", "should reject drop attempt by registrar when term is CONCLUDED", "should apply penalty fee on the 3rd self-initiated drop", "should provide full course refund when course is cancelled, even if balance goes negative", "should allow professor to create a new course after cancelling one of their 5 courses", "should reject payment with unknown fields", "should transition DRAFT courses to COMPLETED when concluding term", "should NOT promote a waitlisted student if it would exceed their credit limit", "should skip waitlisted students who would exceed credit limit and promote the next eligible", "should expose that refunds are capped at current balance instead of full course cost", "should fail when professor tries to drop student after registration is closed", "should check state before revision when closing registration", "should initialize seat ledger with seats_available == capacity on publish", "should apply $50 penalty on 3rd self-initiated drop", "should reject professor drop when term is ENROLLMENT_CLOSED", "should drop all enrollments and refund enrolled students on cancel", "should reject payment with amount 0", "should block professor from dropping students from other professors courses", "should allow negative balance after full refund with partial payment", "should successfully enroll student when seat is available", "should place student on waitlist when course is full", "should reject enrollment for non-existent student", "should reject enrollment when term is in PLANNING state", "should reject registrar enrollment with malformed student_id", "should allow registrar to enroll student when registration is closed", "should reject enrollment when term is concluded with ERR_TERM_NOT_ACTIVE", "should apply $50 penalty fee on 3rd drop", "should not apply penalty on drops 1 and 2", "should block student from dropping after registration closes", "should not count professor-initiated drops toward student drop limit", "should reject drop when revision field is not provided", "should allow professor to create course for themselves in DRAFT state", "should allow registrar to create course on behalf of professor", "should reject course creation with credits outside allowed range", "should reject course creation with invalid capacity", "should reject registrar providing non-professor user as instructor", "should allow professor to publish their own draft course", "should drop all enrollments and refund tuition when course is cancelled", "should reduce balance when student makes a payment towards their tuition", "should allow registrar to record payment for any student", "should reject payment attempts by professors", "should forbid students from paying another student's tuition", "should reject payment with zero or negative amount", "should reject payment exceeding outstanding balance", "should return error when registrar tries to pay for invalid student", "should reject payment with non-integer amount", "should return error when payment references nonexistent term", "should reject payment when target user is not a student", "should initialize ledger for student with no prior payment history", "should complete full student lifecycle from enrollment to payment", "should handle complete course lifecycle from creation to cancellation with enrollments", "should return ERR_REGISTRATION_CLOSED for concluded term", "should correctly handle ledger and penalty when promoted student immediately drops", "should apply refund before penalty, resulting in positive balance", "should not increment drop count for auto-drops from course cancellation", "should return ERR_MISSING_REQUIRED_FIELD when revision is omitted from term state transition", "should initialize ledger at zero and reject overpayment for student with no enrollments", "should reject registrar overpayment attempt with ERR_OVERPAY_NOT_ALLOWED", "should reject payment with negative amount", "should correctly update ledger when waitlisted student is promoted to enrolled", "should handle course cancellation and ledger updates appropriately", "should allow dropping waitlisted enrollment without affecting ledger or seat counts", "should reject registrar enrollment attempt into concluded term", "should reject term creation with name exceeding 100 characters", "should automatically complete draft courses when concluding term", "should allow course creation without delivery_mode field", "should reject opening term without required revision", "should reject closing term without required revision", "should reject concluding term without required revision"], "passedTestNames": ["should reject enrollment with unknown fields", "should reject drop with unknown fields", "should reject open-registration with unknown fields", "should reject close-registration with unknown fields", "should reject conclude with unknown fields", "should increment revision by 1 after successful open-registration", "should increment revision by 1 after successful close-registration", "should increment revision by 1 after successful conclude", "should reject opening an already open term with ERR_TERM_NOT_ACTIVE", "should reject concurrent open-registration with stale revision", "should transition all OPEN courses to IN_PROGRESS when closing registration", "should reject 6th course creation with ERR_MAX_COURSES_REACHED", "should block student self-enrollment over 18 credits but allow registrar override", "should promote oldest waitlisted student when seat becomes available", "should enforce role-based field visibility rules", "should block waitlist promotion when it would exceed credit limit", "should reject unknown fields in enrollment creation", "should allow professor to drop their own course students", "should allow registrar to enroll student beyond credit limit", "should block student from exceeding 18-credit term limit", "should reject professor attempt to use enrollment endpoint", "should reject student attempting to enroll another student", "should require student_id when registrar enrolls", "should reject enrollment for invalid course ID", "should reject enrollment in draft course", "should reject enrollment when term registration is closed", "should reject duplicate enrollment attempt", "should reject enrollment with malformed termId", "should reject enrollment with malformed courseId", "should successfully drop enrolled course and refund tuition", "should successfully remove student from waitlist", "should allow instructor to drop student without penalty", "should reject unauthorized user attempting to drop enrollment", "should reject dropping already completed enrollment", "should reject dropping already dropped enrollment", "should reject student drop after term concluded", "should block student from dropping 4th course", "should reject drop with stale revision", "should reject drop with malformed termId", "should reject drop with malformed courseId", "should reject drop with malformed enrollmentId", "should allow registrar to drop after registration closes", "should reject student attempts to create courses", "should reject course creation with invalid code format", "should reject duplicate course codes in same term", "should reject professor specifying different instructor when creating course", "should reject registrar course creation without professor_id", "should reject course creation when professor reaches maximum courses", "should validate conditional fields based on delivery_mode", "should allow registrar to publish any professor's draft course", "should reject student attempts to publish courses", "should reject professor publishing course they do not own", "should reject publishing course that is not in DRAFT state", "should reject publishing course when term registration is not open", "should reject publishing course with outdated revision", "should allow professor to cancel their own course", "should allow registrar to cancel any course", "should reject student attempts to cancel courses", "should reject professor cancelling course they do not teach", "should reject cancelling course that is already completed", "should reject cancelling course with incorrect revision", "should return ERR_CONDITIONAL_FIELD_REQUIRED when HYBRID course has neither location nor online_link", "should return ERR_INVALID_ENUM_VALUE for invalid delivery_mode values", "should return ERR_UNKNOWN_FIELD when publish request contains unknown fields", "should return ERR_UNKNOWN_FIELD when cancel request contains unknown fields", "should automatically enroll waitlisted student when seat becomes available", "should handle complex interactions with waitlist, drops, and penalties", "should allow enrollment when student hits exactly 18 credits without error", "should allow professor to create their 5th course without triggering MAX_COURSES_REACHED", "should return ERR_UNAUTHORIZED_ROLE before deeper validation", "should fail Registrar B with stale revision after Registrar A updates term", "should block self-enrollment after dropping from registrar override to exactly 18 credits", "should return ERR_UNAUTHORIZED_ROLE when student tries to list course enrollments", "should return ERR_COURSE_NOT_FOUND for DRAFT course accessed by student", "should return ERR_INVALID_ID_FORMAT for invalid UUID", "should return ERR_COURSE_NOT_FOUND when accessing course through wrong term", "should promote exactly two waitlisted students when two seats are freed", "should maintain enrollment state after failed drop attempt due to drop limit", "should reject close-registration with stale revision", "should allow publishing course without revision field", "should allow cancelling course without revision field", "should reject course creation with description exceeding 1000 characters", "should allow Registrar to create a new academic term successfully", "should reject term creation by non-Registrar users", "should reject term creation without required name field", "should reject creating a term with a duplicate name", "should allow Registrar to open enrollment for a term", "should reject opening term by non-Registrar", "should reject opening term that is not in PLANNING state", "should reject opening term with stale revision number", "should allow Registrar to close enrollment for a term", "should reject closing term by non-Registrar", "should reject closing term that is not open for enrollment", "should reject closing term with out-of-date revision number", "should automatically transition all OPEN courses to IN_PROGRESS when closing term", "should allow Registrar to conclude a term", "should reject concluding term by non-Registrar", "should reject concluding term that is still open for enrollment", "should reject concluding term with bad revision", "should complete all in-progress courses and finalize enrollments when concluding term", "should drop waitlisted enrollments when term is concluded"], "fileBreakdown": {"06-misc-tests.test.ts": {"totalTests": 23, "passedTests": 8, "failedTests": 15, "failedTestNames": ["should reject drop attempt when term is ENROLLMENT_CLOSED", "should allow student to drop after 3 admin drops without penalty", "should provide full course refund even if balance goes negative", "should allow professor to create a new course after cancelling one of their 5 courses", "should reject drop attempt by professor when term is ENROLLMENT_CLOSED", "should reject drop attempt by registrar when term is CONCLUDED", "should apply penalty fee on the 3rd self-initiated drop", "should provide full course refund when course is cancelled, even if balance goes negative", "should allow professor to create a new course after cancelling one of their 5 courses", "should reject payment with unknown fields", "should transition DRAFT courses to COMPLETED when concluding term", "should NOT promote a waitlisted student if it would exceed their credit limit", "should skip waitlisted students who would exceed credit limit and promote the next eligible", "should expose that refunds are capped at current balance instead of full course cost", "should fail when professor tries to drop student after registration is closed"], "passedTestNames": ["should reject enrollment with unknown fields", "should reject drop with unknown fields", "should reject open-registration with unknown fields", "should reject close-registration with unknown fields", "should reject conclude with unknown fields", "should increment revision by 1 after successful open-registration", "should increment revision by 1 after successful close-registration", "should increment revision by 1 after successful conclude"]}, "07-misc-2-tests.test.ts": {"totalTests": 18, "passedTests": 10, "failedTests": 8, "failedTestNames": ["should check state before revision when closing registration", "should initialize seat ledger with seats_available == capacity on publish", "should apply $50 penalty on 3rd self-initiated drop", "should reject professor drop when term is ENROLLMENT_CLOSED", "should drop all enrollments and refund enrolled students on cancel", "should reject payment with amount 0", "should block professor from dropping students from other professors courses", "should allow negative balance after full refund with partial payment"], "passedTestNames": ["should reject opening an already open term with ERR_TERM_NOT_ACTIVE", "should reject concurrent open-registration with stale revision", "should transition all OPEN courses to IN_PROGRESS when closing registration", "should reject 6th course creation with ERR_MAX_COURSES_REACHED", "should block student self-enrollment over 18 credits but allow registrar override", "should promote oldest waitlisted student when seat becomes available", "should enforce role-based field visibility rules", "should block waitlist promotion when it would exceed credit limit", "should reject unknown fields in enrollment creation", "should allow professor to drop their own course students"]}, "03-enrollment-management.test.ts": {"totalTests": 36, "passedTests": 24, "failedTests": 12, "failedTestNames": ["should successfully enroll student when seat is available", "should place student on waitlist when course is full", "should reject enrollment for non-existent student", "should reject enrollment when term is in PLANNING state", "should reject registrar enrollment with malformed student_id", "should allow registrar to enroll student when registration is closed", "should reject enrollment when term is concluded with ERR_TERM_NOT_ACTIVE", "should apply $50 penalty fee on 3rd drop", "should not apply penalty on drops 1 and 2", "should block student from dropping after registration closes", "should not count professor-initiated drops toward student drop limit", "should reject drop when revision field is not provided"], "passedTestNames": ["should allow registrar to enroll student beyond credit limit", "should block student from exceeding 18-credit term limit", "should reject professor attempt to use enrollment endpoint", "should reject student attempting to enroll another student", "should require student_id when registrar enrolls", "should reject enrollment for invalid course ID", "should reject enrollment in draft course", "should reject enrollment when term registration is closed", "should reject duplicate enrollment attempt", "should reject enrollment with malformed termId", "should reject enrollment with malformed courseId", "should successfully drop enrolled course and refund tuition", "should successfully remove student from waitlist", "should allow instructor to drop student without penalty", "should reject unauthorized user attempting to drop enrollment", "should reject dropping already completed enrollment", "should reject dropping already dropped enrollment", "should reject student drop after term concluded", "should block student from dropping 4th course", "should reject drop with stale revision", "should reject drop with malformed termId", "should reject drop with malformed courseId", "should reject drop with malformed enrollmentId", "should allow registrar to drop after registration closes"]}, "02-course-management.test.ts": {"totalTests": 30, "passedTests": 23, "failedTests": 7, "failedTestNames": ["should allow professor to create course for themselves in DRAFT state", "should allow registrar to create course on behalf of professor", "should reject course creation with credits outside allowed range", "should reject course creation with invalid capacity", "should reject registrar providing non-professor user as instructor", "should allow professor to publish their own draft course", "should drop all enrollments and refund tuition when course is cancelled"], "passedTestNames": ["should reject student attempts to create courses", "should reject course creation with invalid code format", "should reject duplicate course codes in same term", "should reject professor specifying different instructor when creating course", "should reject registrar course creation without professor_id", "should reject course creation when professor reaches maximum courses", "should validate conditional fields based on delivery_mode", "should allow registrar to publish any professor's draft course", "should reject student attempts to publish courses", "should reject professor publishing course they do not own", "should reject publishing course that is not in DRAFT state", "should reject publishing course when term registration is not open", "should reject publishing course with outdated revision", "should allow professor to cancel their own course", "should allow registrar to cancel any course", "should reject student attempts to cancel courses", "should reject professor cancelling course they do not teach", "should reject cancelling course that is already completed", "should reject cancelling course with incorrect revision", "should return ERR_CONDITIONAL_FIELD_REQUIRED when HYBRID course has neither location nor online_link", "should return ERR_INVALID_ENUM_VALUE for invalid delivery_mode values", "should return ERR_UNKNOWN_FIELD when publish request contains unknown fields", "should return ERR_UNKNOWN_FIELD when cancel request contains unknown fields"]}, "04-payment-and-scenarios.test.ts": {"totalTests": 15, "passedTests": 2, "failedTests": 13, "failedTestNames": ["should reduce balance when student makes a payment towards their tuition", "should allow registrar to record payment for any student", "should reject payment attempts by professors", "should forbid students from paying another student's tuition", "should reject payment with zero or negative amount", "should reject payment exceeding outstanding balance", "should return error when registrar tries to pay for invalid student", "should reject payment with non-integer amount", "should return error when payment references nonexistent term", "should reject payment when target user is not a student", "should initialize ledger for student with no prior payment history", "should complete full student lifecycle from enrollment to payment", "should handle complete course lifecycle from creation to cancellation with enrollments"], "passedTestNames": ["should automatically enroll waitlisted student when seat becomes available", "should handle complex interactions with waitlist, drops, and penalties"]}, "05-edge-cases.test.ts": {"totalTests": 15, "passedTests": 10, "failedTests": 5, "failedTestNames": ["should return ERR_REGISTRATION_CLOSED for concluded term", "should correctly handle ledger and penalty when promoted student immediately drops", "should apply refund before penalty, resulting in positive balance", "should not increment drop count for auto-drops from course cancellation", "should return ERR_MISSING_REQUIRED_FIELD when revision is omitted from term state transition"], "passedTestNames": ["should allow enrollment when student hits exactly 18 credits without error", "should allow professor to create their 5th course without triggering MAX_COURSES_REACHED", "should return ERR_UNAUTHORIZED_ROLE before deeper validation", "should fail Registrar B with stale revision after Registrar A updates term", "should block self-enrollment after dropping from registrar override to exactly 18 credits", "should return ERR_UNAUTHORIZED_ROLE when student tries to list course enrollments", "should return ERR_COURSE_NOT_FOUND for DRAFT course accessed by student", "should return ERR_INVALID_ID_FORMAT for invalid UUID", "should return ERR_COURSE_NOT_FOUND when accessing course through wrong term", "should promote exactly two waitlisted students when two seats are freed"]}, "08-misc-3-tests.test.ts": {"totalTests": 15, "passedTests": 5, "failedTests": 10, "failedTestNames": ["should initialize ledger at zero and reject overpayment for student with no enrollments", "should reject registrar overpayment attempt with ERR_OVERPAY_NOT_ALLOWED", "should reject payment with negative amount", "should correctly update ledger when waitlisted student is promoted to enrolled", "should handle course cancellation and ledger updates appropriately", "should allow dropping waitlisted enrollment without affecting ledger or seat counts", "should reject registrar enrollment attempt into concluded term", "should reject term creation with name exceeding 100 characters", "should automatically complete draft courses when concluding term", "should allow course creation without delivery_mode field"], "passedTestNames": ["should maintain enrollment state after failed drop attempt due to drop limit", "should reject close-registration with stale revision", "should allow publishing course without revision field", "should allow cancelling course without revision field", "should reject course creation with description exceeding 1000 characters"]}, "01-term-management.test.ts": {"totalTests": 22, "passedTests": 19, "failedTests": 3, "failedTestNames": ["should reject opening term without required revision", "should reject closing term without required revision", "should reject concluding term without required revision"], "passedTestNames": ["should allow Registrar to create a new academic term successfully", "should reject term creation by non-Registrar users", "should reject term creation without required name field", "should reject creating a term with a duplicate name", "should allow Registrar to open enrollment for a term", "should reject opening term by non-Registrar", "should reject opening term that is not in PLANNING state", "should reject opening term with stale revision number", "should allow Registrar to close enrollment for a term", "should reject closing term by non-Registrar", "should reject closing term that is not open for enrollment", "should reject closing term with out-of-date revision number", "should automatically transition all OPEN courses to IN_PROGRESS when closing term", "should allow Registrar to conclude a term", "should reject concluding term by non-Registrar", "should reject concluding term that is still open for enrollment", "should reject concluding term with bad revision", "should complete all in-progress courses and finalize enrollments when concluding term", "should drop waitlisted enrollments when term is concluded"]}}}