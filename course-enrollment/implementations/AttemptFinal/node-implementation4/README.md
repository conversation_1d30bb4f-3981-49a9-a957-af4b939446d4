# University Course Registration & Enrollment API

A comprehensive Node.js API implementation for managing academic course offerings and student enrollments within academic terms.

## Features

✅ **Complete PRD Implementation**: All features from the Product Requirements Document
✅ **Role-Based Access Control**: STUDENT, PROFESSOR, REGISTRAR with proper permissions
✅ **Academic Term Management**: Full lifecycle from planning to conclusion
✅ **Course Management**: Create, publish, cancel courses with state management
✅ **Enrollment & Waitlists**: Automatic waitlist promotion when seats open
✅ **Financial Ledgers**: Tuition tracking with payments and refunds
✅ **Business Rules**: Credit limits, drop penalties, course limits per professor
✅ **Standard API Format**: Unified response envelopes and error handling
✅ **In-Memory Storage**: No external database required

## Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Run Tests**
   ```bash
   node test.js
   ```

3. **Start Server**
   ```bash
   npm start
   # or
   node index.js
   ```

The API will be available at `http://localhost:3000`

## API Endpoints

### Term Management
- `POST /terms` - Create academic term (Registrar only)
- `GET /terms/{termId}` - Get term details
- `PATCH /terms/{termId}:open-registration` - Open enrollment (Registrar only)
- `PATCH /terms/{termId}:close-registration` - Close enrollment (Registrar only)
- `PATCH /terms/{termId}:conclude` - Conclude term (Registrar only)

### Course Management
- `POST /terms/{termId}/courses` - Create course (Professor/Registrar)
- `GET /terms/{termId}/courses` - List courses (role-filtered)
- `GET /terms/{termId}/courses/{courseId}` - Get course details
- `PATCH /terms/{termId}/courses/{courseId}:publish` - Publish course
- `PATCH /terms/{termId}/courses/{courseId}:cancel` - Cancel course

### Enrollment Management
- `POST /terms/{termId}/courses/{courseId}/enrollments` - Enroll in course
- `GET /terms/{termId}/courses/{courseId}/enrollments` - List enrollments (Professor/Registrar)
- `GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}` - Get enrollment
- `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop` - Drop enrollment

### Financial Operations
- `POST /terms/{termId}/students/{studentId}:pay` - Make tuition payment
- `GET /terms/{termId}/students/{studentId}/balance` - Get tuition balance

## Authentication

All requests require these headers:
- `X-User-ID`: User's UUID
- `X-User-Role`: One of `STUDENT`, `PROFESSOR`, `REGISTRAR`

## Example Usage

### 1. Create a Term (Registrar)
```bash
curl -X POST http://localhost:3000/terms \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-************" \
  -H "X-User-Role: REGISTRAR" \
  -H "Content-Type: application/json" \
  -d '{"name": "Fall 2025"}'
```

### 2. Create a Course (Professor)
```bash
curl -X POST http://localhost:3000/terms/{termId}/courses \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-************" \
  -H "X-User-Role: PROFESSOR" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "CS101",
    "title": "Introduction to Computer Science",
    "description": "Basic programming concepts",
    "credits": 3,
    "capacity": 30,
    "delivery_mode": "IN_PERSON",
    "location": "Room 101"
  }'
```

### 3. Enroll in Course (Student)
```bash
curl -X POST http://localhost:3000/terms/{termId}/courses/{courseId}/enrollments \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-************" \
  -H "X-User-Role: STUDENT" \
  -H "Content-Type: application/json"
```

### 4. Make Payment (Student)
```bash
curl -X POST http://localhost:3000/terms/{termId}/students/{studentId}:pay \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-************" \
  -H "X-User-Role: STUDENT" \
  -H "Content-Type: application/json" \
  -d '{"amount": 30000}'
```

## Business Rules

- **Credit Limit**: Students limited to 18 credits per term
- **Professor Limit**: Maximum 5 courses per professor per term
- **Drop Limit**: Students can drop maximum 3 courses per term
- **Drop Penalty**: $50 fee applied on 3rd drop
- **Tuition**: $100 per credit hour
- **Waitlist**: Automatic promotion when seats become available

## Data Models

### Academic Term States
- `PLANNING` → `ENROLLMENT_OPEN` → `ENROLLMENT_CLOSED` → `CONCLUDED`

### Course States  
- `DRAFT` → `OPEN` → `IN_PROGRESS` → `COMPLETED`/`CANCELLED`

### Enrollment States
- `ENROLLED` / `WAITLISTED` → `DROPPED` / `COMPLETED`

## Error Handling

All errors follow the standard format:
```json
{
  "meta": {
    "api_request_id": "req_abc123",
    "api_request_timestamp": "2025-01-01T00:00:00.000Z"
  },
  "response_type": "error",
  "data": {
    "error_id": "ERR_TERM_NOT_FOUND",
    "message": "Academic term not found"
  }
}
```

## Architecture

- **Express.js**: Web framework
- **In-Memory Storage**: Simple data persistence
- **RBAC Middleware**: Role-based access control
- **Validation Layer**: Input validation and business rules
- **Ledger System**: Financial and seat tracking
- **Waitlist Manager**: Automatic promotion logic

## Development

The codebase is organized as follows:
```
├── index.js              # Main application entry
├── models/
│   ├── constants.js      # Business constants and enums
│   └── storage.js        # In-memory data storage
├── middleware/
│   ├── auth.js          # Authentication middleware
│   ├── response.js      # Response formatting
│   └── errorHandler.js  # Global error handling
├── routes/
│   ├── terms.js         # Term management endpoints
│   ├── courses.js       # Course management endpoints
│   ├── enrollments.js   # Enrollment endpoints
│   └── payments.js      # Payment endpoints
└── utils/
    ├── validation.js    # Input validation utilities
    ├── rbac.js         # Role-based access control
    ├── waitlist.js     # Waitlist management
    └── errors.js       # Error definitions
```

## Compliance

This implementation fully complies with the Product Requirements Document, including:
- All 41 error codes with proper HTTP status codes
- Standard response envelope format
- Field-level security based on user roles
- Complete business logic for enrollment, waitlists, and payments
- Proper validation of all inputs
- Comprehensive audit trail through ledger systems