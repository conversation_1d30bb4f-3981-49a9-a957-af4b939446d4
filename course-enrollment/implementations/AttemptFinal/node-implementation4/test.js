// Simple test script to validate the API implementation
const express = require('express');
const { v4: uuidv4 } = require('uuid');

// Mock test data
const mockRegistrarId = uuidv4();
const mockProfessorId = uuidv4();
const mockStudentId = uuidv4();

console.log('Testing University Course Registration API Implementation');
console.log('====================================================');

// Test 1: Load all modules
console.log('\n1. Testing module loading...');
try {
  const storage = require('./models/storage');
  const constants = require('./models/constants');
  const validation = require('./utils/validation');
  const rbac = require('./utils/rbac');
  const waitlist = require('./utils/waitlist');
  const errors = require('./utils/errors');
  
  console.log('✓ All core modules loaded successfully');
} catch (error) {
  console.log('✗ Module loading failed:', error.message);
  process.exit(1);
}

// Test 2: Validate constants
console.log('\n2. Testing constants...');
const CONSTANTS = require('./models/constants');
console.log('✓ MAX_CREDITS_PER_TERM:', CONSTANTS.MAX_CREDITS_PER_TERM);
console.log('✓ COST_PER_CREDIT:', CONSTANTS.COST_PER_CREDIT);
console.log('✓ User roles:', Object.values(CONSTANTS.USER_ROLES));
console.log('✓ Term states:', Object.values(CONSTANTS.TERM_STATES));

// Test 3: Test storage operations
console.log('\n3. Testing storage operations...');
const storage = require('./models/storage');

// Create a test term
const testTerm = storage.createTerm({
  name: 'Test Term 2025',
  state: CONSTANTS.TERM_STATES.PLANNING,
  created_by: mockRegistrarId
});
console.log('✓ Created test term:', testTerm.id);

// Create a test course
const testCourse = storage.createCourse({
  term_id: testTerm.id,
  code: 'CS101',
  title: 'Introduction to Computer Science',
  description: 'Basic computer science concepts',
  credits: 3,
  capacity: 30,
  delivery_mode: 'IN_PERSON',
  location: 'Room 101',
  professor_id: mockProfessorId,
  state: CONSTANTS.COURSE_STATES.DRAFT
});
console.log('✓ Created test course:', testCourse.id);

// Test seat ledger
storage.initializeSeatLedger(testCourse.id, testCourse.capacity);
console.log('✓ Initialized seat ledger with', storage.getAvailableSeats(testCourse.id), 'seats');

// Test enrollment
const testEnrollment = storage.createEnrollment({
  term_id: testTerm.id,
  course_id: testCourse.id,
  student_id: mockStudentId,
  state: CONSTANTS.ENROLLMENT_STATES.ENROLLED
});
console.log('✓ Created test enrollment:', testEnrollment.id);

// Test ledger operations
storage.debitSeatLedger(testCourse.id, 1);
storage.creditTuitionLedger(testTerm.id, mockStudentId, 3 * CONSTANTS.COST_PER_CREDIT);
console.log('✓ Updated ledgers - Available seats:', storage.getAvailableSeats(testCourse.id));
console.log('✓ Student balance:', storage.getTuitionBalance(testTerm.id, mockStudentId));

// Test 4: Validate validation utilities
console.log('\n4. Testing validation utilities...');
const ValidationUtils = require('./utils/validation');

try {
  ValidationUtils.validateUUID(uuidv4());
  console.log('✓ UUID validation works');
  
  ValidationUtils.validateCourseCode('CS101');
  console.log('✓ Course code validation works');
  
  ValidationUtils.validateCredits(3);
  console.log('✓ Credits validation works');
  
  ValidationUtils.validateCapacity(30);
  console.log('✓ Capacity validation works');
  
} catch (error) {
  console.log('✗ Validation test failed:', error.message);
}

// Test 5: Test RBAC filtering
console.log('\n5. Testing RBAC filtering...');
const RBACUtils = require('./utils/rbac');

const filteredCourseForStudent = RBACUtils.filterCourseData(testCourse, 'STUDENT', mockStudentId);
const filteredCourseForProf = RBACUtils.filterCourseData(testCourse, 'PROFESSOR', mockProfessorId);
const filteredCourseForRegistrar = RBACUtils.filterCourseData(testCourse, 'REGISTRAR', mockRegistrarId);

console.log('✓ Student filtered course has available_seats:', filteredCourseForStudent?.available_seats !== undefined);
console.log('✓ Professor filtered course has enrolled_count:', filteredCourseForProf?.enrolled_count !== undefined);
console.log('✓ Registrar filtered course has all fields:', filteredCourseForRegistrar?.enrolled_count !== undefined);

// Test 6: Test error creation
console.log('\n6. Testing error handling...');
const { createError } = require('./utils/errors');

try {
  throw createError('ERR_TERM_NOT_FOUND');
} catch (error) {
  console.log('✓ Error creation works:', error.errorId, error.statusCode);
}

// Test 7: Test waitlist functionality
console.log('\n7. Testing waitlist functionality...');
const WaitlistManager = require('./utils/waitlist');

// Create a waitlisted enrollment
const waitlistedEnrollment = storage.createEnrollment({
  term_id: testTerm.id,
  course_id: testCourse.id,
  student_id: uuidv4(),
  state: CONSTANTS.ENROLLMENT_STATES.WAITLISTED
});

const position = WaitlistManager.getWaitlistPosition(testCourse.id, waitlistedEnrollment.student_id);
console.log('✓ Waitlist position for student:', position);

const stats = WaitlistManager.getWaitlistStats(testCourse.id);
console.log('✓ Waitlist stats:', stats);

// Clean up
storage.clear();
console.log('✓ Storage cleared');

console.log('\n====================================================');
console.log('All tests completed successfully! 🎉');
console.log('The University Course Registration API is ready to use.');
console.log('\nTo start the server, run: node index.js');
console.log('Then test endpoints with a tool like curl or Postman.');
console.log('\nExample headers required for all requests:');
console.log('X-User-ID: ' + uuidv4());
console.log('X-User-Role: REGISTRAR (or PROFESSOR or STUDENT)');
console.log('Content-Type: application/json');