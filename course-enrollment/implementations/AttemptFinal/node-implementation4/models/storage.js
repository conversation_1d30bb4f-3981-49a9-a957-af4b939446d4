const { v4: uuidv4 } = require('uuid');

class InMemoryStorage {
  constructor() {
    // Core entities
    this.terms = new Map();
    this.courses = new Map();
    this.enrollments = new Map();
    
    // Ledgers
    this.courseSeatLedgers = new Map(); // courseId -> seats_available
    this.studentTuitionLedgers = new Map(); // termId_studentId -> balance_cents
    
    // Tracking maps for business rules
    this.coursesByTerm = new Map(); // termId -> Set of courseIds
    this.enrollmentsByCourse = new Map(); // courseId -> Set of enrollmentIds
    this.enrollmentsByStudent = new Map(); // termId_studentId -> Set of enrollmentIds
    this.coursesByProfessor = new Map(); // termId_professorId -> Set of courseIds
    this.studentDropCounts = new Map(); // termId_studentId -> drop count
  }

  // Generic CRUD operations
  create(collection, entity) {
    if (!entity.id) {
      entity.id = uuidv4();
    }
    entity.created_at = new Date().toISOString();
    entity.revision = 0;
    
    this[collection].set(entity.id, { ...entity });
    return { ...entity };
  }

  get(collection, id) {
    const entity = this[collection].get(id);
    return entity ? { ...entity } : null;
  }

  update(collection, id, updates) {
    const entity = this[collection].get(id);
    if (!entity) return null;
    
    const updated = {
      ...entity,
      ...updates,
      revision: entity.revision + 1
    };
    
    this[collection].set(id, updated);
    return { ...updated };
  }

  delete(collection, id) {
    return this[collection].delete(id);
  }

  getAll(collection) {
    return Array.from(this[collection].values()).map(entity => ({ ...entity }));
  }

  // Term-specific operations
  createTerm(term) {
    const created = this.create('terms', term);
    this.coursesByTerm.set(created.id, new Set());
    return created;
  }

  // Course-specific operations
  createCourse(course) {
    const created = this.create('courses', course);
    
    // Update tracking maps
    const termCourses = this.coursesByTerm.get(course.term_id) || new Set();
    termCourses.add(created.id);
    this.coursesByTerm.set(course.term_id, termCourses);
    
    const profCourses = this.coursesByProfessor.get(`${course.term_id}_${course.professor_id}`) || new Set();
    profCourses.add(created.id);
    this.coursesByProfessor.set(`${course.term_id}_${course.professor_id}`, profCourses);
    
    this.enrollmentsByCourse.set(created.id, new Set());
    
    return created;
  }

  getCoursesByTerm(termId) {
    const courseIds = this.coursesByTerm.get(termId) || new Set();
    return Array.from(courseIds).map(id => this.get('courses', id)).filter(Boolean);
  }

  getCoursesByProfessor(termId, professorId) {
    const courseIds = this.coursesByProfessor.get(`${termId}_${professorId}`) || new Set();
    return Array.from(courseIds).map(id => this.get('courses', id)).filter(Boolean);
  }

  // Enrollment-specific operations
  createEnrollment(enrollment) {
    const created = this.create('enrollments', enrollment);
    
    // Update tracking maps
    const courseEnrollments = this.enrollmentsByCourse.get(enrollment.course_id) || new Set();
    courseEnrollments.add(created.id);
    this.enrollmentsByCourse.set(enrollment.course_id, courseEnrollments);
    
    const studentEnrollments = this.enrollmentsByStudent.get(`${enrollment.term_id}_${enrollment.student_id}`) || new Set();
    studentEnrollments.add(created.id);
    this.enrollmentsByStudent.set(`${enrollment.term_id}_${enrollment.student_id}`, studentEnrollments);
    
    return created;
  }

  getEnrollmentsByCourse(courseId) {
    const enrollmentIds = this.enrollmentsByCourse.get(courseId) || new Set();
    return Array.from(enrollmentIds).map(id => this.get('enrollments', id)).filter(Boolean);
  }

  getEnrollmentsByStudent(termId, studentId) {
    const enrollmentIds = this.enrollmentsByStudent.get(`${termId}_${studentId}`) || new Set();
    return Array.from(enrollmentIds).map(id => this.get('enrollments', id)).filter(Boolean);
  }

  // Seat ledger operations
  initializeSeatLedger(courseId, capacity) {
    this.courseSeatLedgers.set(courseId, capacity);
  }

  getAvailableSeats(courseId) {
    return this.courseSeatLedgers.get(courseId) || 0;
  }

  debitSeatLedger(courseId, amount = 1) {
    const current = this.courseSeatLedgers.get(courseId) || 0;
    const newValue = current - amount;
    if (newValue < 0) {
      throw new Error('Cannot debit seat ledger below 0');
    }
    this.courseSeatLedgers.set(courseId, newValue);
    return newValue;
  }

  creditSeatLedger(courseId, amount = 1) {
    const current = this.courseSeatLedgers.get(courseId) || 0;
    const course = this.get('courses', courseId);
    if (!course) {
      throw new Error('Course not found');
    }
    const newValue = current + amount;
    if (newValue > course.capacity) {
      throw new Error('Cannot credit seat ledger above capacity');
    }
    this.courseSeatLedgers.set(courseId, newValue);
    return newValue;
  }

  // Tuition ledger operations
  getTuitionBalance(termId, studentId) {
    return this.studentTuitionLedgers.get(`${termId}_${studentId}`) || 0;
  }

  creditTuitionLedger(termId, studentId, amount) {
    const key = `${termId}_${studentId}`;
    const current = this.studentTuitionLedgers.get(key) || 0;
    const newValue = current + amount;
    this.studentTuitionLedgers.set(key, newValue);
    return newValue;
  }

  debitTuitionLedger(termId, studentId, amount) {
    const key = `${termId}_${studentId}`;
    const current = this.studentTuitionLedgers.get(key) || 0;
    const newValue = current - amount;
    if (newValue < 0) {
      throw new Error('Cannot debit tuition ledger below 0');
    }
    this.studentTuitionLedgers.set(key, newValue);
    return newValue;
  }

  // Drop count tracking
  getDropCount(termId, studentId) {
    return this.studentDropCounts.get(`${termId}_${studentId}`) || 0;
  }

  incrementDropCount(termId, studentId) {
    const key = `${termId}_${studentId}`;
    const current = this.studentDropCounts.get(key) || 0;
    const newValue = current + 1;
    this.studentDropCounts.set(key, newValue);
    return newValue;
  }

  // Utility methods for business logic
  findEnrollmentByStudentAndCourse(termId, studentId, courseId) {
    const studentEnrollments = this.getEnrollmentsByStudent(termId, studentId);
    return studentEnrollments.find(enrollment => 
      enrollment.course_id === courseId && 
      enrollment.state !== 'DROPPED'
    );
  }

  getEnrolledStudentCount(courseId) {
    const enrollments = this.getEnrollmentsByCourse(courseId);
    return enrollments.filter(e => e.state === 'ENROLLED').length;
  }

  getWaitlistedStudentCount(courseId) {
    const enrollments = this.getEnrollmentsByCourse(courseId);
    return enrollments.filter(e => e.state === 'WAITLISTED').length;
  }

  getNextWaitlistedStudent(courseId) {
    const enrollments = this.getEnrollmentsByCourse(courseId);
    const waitlisted = enrollments
      .filter(e => e.state === 'WAITLISTED')
      .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    
    return waitlisted.length > 0 ? waitlisted[0] : null;
  }

  // Clear all data (for testing)
  clear() {
    this.terms.clear();
    this.courses.clear();
    this.enrollments.clear();
    this.courseSeatLedgers.clear();
    this.studentTuitionLedgers.clear();
    this.coursesByTerm.clear();
    this.enrollmentsByCourse.clear();
    this.enrollmentsByStudent.clear();
    this.coursesByProfessor.clear();
    this.studentDropCounts.clear();
  }
}

// Export singleton instance
module.exports = new InMemoryStorage();