// Business constants from PRD
const CONSTANTS = {
  MAX_CREDITS_PER_TERM: 18,
  MAX_COURSES_PER_PROF: 5,
  MAX_DROP_COUNT_PER_TERM: 3,
  DROP_PENALTY_FEE: 5000, // cents
  COST_PER_CREDIT: 10000, // cents ($100.00)
  
  // Enums
  USER_ROLES: {
    STUDENT: 'STUDENT',
    PROFESSOR: 'PROFESSOR',
    REGISTRAR: 'REGISTRAR'
  },
  
  TERM_STATES: {
    PLANNING: 'PLANNING',
    ENROLLMENT_OPEN: 'ENROLLMENT_OPEN',
    ENROLLMENT_CLOSED: 'ENROLLMENT_CLOSED',
    CONCLUDED: 'CONCLUDED'
  },
  
  COURSE_STATES: {
    DRAFT: 'DRAFT',
    OPEN: 'OPEN',
    IN_PROGRESS: 'IN_PROGRESS',
    COMPLETED: 'COMPLETED',
    CANCELLED: 'CANCELLED'
  },
  
  ENROLLMENT_STATES: {
    ENROLLED: 'ENROLLED',
    WAITLISTED: 'WAITLISTED',
    DROPPED: 'DROPPED',
    COMPLETED: 'COMPLETED'
  },
  
  DELIVERY_MODES: {
    IN_PERSON: 'IN_PERSON',
    ONLINE: 'ONLINE',
    HYBRID: 'HYBRID'
  },
  
  // Validation patterns
  UUID_PATTERN: /^[0-9a-fA-F-]{36}$/,
  COURSE_CODE_PATTERN: /^[A-Z]{2,4}[0-9]{3}$/,
  
  // Field lengths
  MAX_TITLE_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 1000,
  MIN_CAPACITY: 1,
  MAX_CAPACITY: 500,
  MIN_CREDITS: 1,
  MAX_CREDITS: 5,
  
  // Pagination defaults
  DEFAULT_LIMIT: 50,
  MAX_LIMIT: 100
};

module.exports = CONSTANTS;