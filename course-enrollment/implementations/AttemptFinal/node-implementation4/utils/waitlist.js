const storage = require('../models/storage');
const CONSTANTS = require('../models/constants');

class WaitlistManager {
  // Promote the next waitlisted student when a seat becomes available
  static async promoteNextStudent(courseId) {
    try {
      // Get the next waitlisted student (earliest created_at)
      const nextStudent = storage.getNextWaitlistedStudent(courseId);
      if (!nextStudent) {
        return null; // No one waiting
      }

      // Check if seats are available
      const availableSeats = storage.getAvailableSeats(courseId);
      if (availableSeats <= 0) {
        return null; // No seats available
      }

      // Get course details for tuition calculation
      const course = storage.get('courses', courseId);
      if (!course) {
        throw new Error('Course not found during promotion');
      }

      // Promote the student
      const updatedEnrollment = storage.update('enrollments', nextStudent.id, {
        state: CONSTANTS.ENROLLMENT_STATES.ENROLLED
      });

      // Update ledgers
      storage.debitSeatLedger(courseId, 1); // Take a seat
      const tuitionCost = course.credits * CONSTANTS.COST_PER_CREDIT;
      storage.creditTuitionLedger(course.term_id, nextStudent.student_id, tuitionCost);

      console.log(`Promoted student ${nextStudent.student_id} from waitlist to enrolled in course ${courseId}`);
      
      // Check if more seats are available and more students waiting
      const stillAvailable = storage.getAvailableSeats(courseId);
      const nextWaiting = storage.getNextWaitlistedStudent(courseId);
      
      if (stillAvailable > 0 && nextWaiting) {
        // Recursively promote more students if seats still available
        setTimeout(() => this.promoteNextStudent(courseId), 0);
      }

      return updatedEnrollment;
    } catch (error) {
      console.error('Error during waitlist promotion:', error);
      return null;
    }
  }

  // Handle seat vacated event (when someone drops)
  static onSeatVacated(courseId) {
    // Use setTimeout to make this asynchronous (not blocking the drop response)
    setTimeout(() => {
      this.promoteNextStudent(courseId);
    }, 0);
  }

  // Check and promote students when course capacity is increased
  static onCapacityIncreased(courseId, oldCapacity, newCapacity) {
    const seatIncrease = newCapacity - oldCapacity;
    if (seatIncrease > 0) {
      // Credit the seat ledger with the additional seats
      try {
        storage.creditSeatLedger(courseId, seatIncrease);
        
        // Promote students for each new seat
        for (let i = 0; i < seatIncrease; i++) {
          setTimeout(() => {
            this.promoteNextStudent(courseId);
          }, i * 10); // Small delay between promotions
        }
      } catch (error) {
        console.error('Error handling capacity increase:', error);
      }
    }
  }

  // Get waitlist position for a student
  static getWaitlistPosition(courseId, studentId) {
    const enrollments = storage.getEnrollmentsByCourse(courseId);
    const waitlisted = enrollments
      .filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED)
      .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    
    const position = waitlisted.findIndex(e => e.student_id === studentId);
    return position >= 0 ? position + 1 : 0; // 1-based position, 0 if not found
  }

  // Get waitlist statistics for a course
  static getWaitlistStats(courseId) {
    const enrollments = storage.getEnrollmentsByCourse(courseId);
    const waitlisted = enrollments.filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED);
    const enrolled = enrollments.filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED);
    
    return {
      waitlist_count: waitlisted.length,
      enrolled_count: enrolled.length,
      available_seats: storage.getAvailableSeats(courseId),
      next_promotion_time: waitlisted.length > 0 ? 'when seat becomes available' : 'no waitlist'
    };
  }
}

module.exports = WaitlistManager;