// Error definitions from PRD Section 5
const ERROR_DEFINITIONS = {
  'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER': { status: 400, message: 'Missing or invalid user context header' },
  'ERR_UNAUTHORIZED_ROLE': { status: 403, message: 'User role not authorized for this operation' },
  'ERR_NOT_INSTRUCTOR': { status: 403, message: 'User is not the instructor of this course' },
  'ERR_PERMISSION_DENIED': { status: 403, message: 'Permission denied' },
  'ERR_TERM_NOT_FOUND': { status: 404, message: 'Academic term not found' },
  'ERR_COURSE_NOT_FOUND': { status: 404, message: 'Course not found' },
  'ERR_ENROLLMENT_NOT_FOUND': { status: 404, message: 'Enrollment not found' },
  'ERR_STUDENT_NOT_FOUND': { status: 404, message: 'Student not found' },
  'ERR_TERM_CLOSED': { status: 404, message: 'Term is closed and no longer accessible' },
  'ERR_INVALID_ID_FORMAT': { status: 400, message: 'Invalid ID format - must be a valid UUID' },
  'ERR_INVALID_COURSE_CODE': { status: 400, message: 'Invalid course code format' },
  'ERR_COURSE_CODE_NOT_UNIQUE': { status: 409, message: 'Course code must be unique within the term' },
  'ERR_INVALID_FIELD_LENGTH': { status: 400, message: 'Field length exceeds maximum allowed' },
  'ERR_INVALID_CREDITS': { status: 400, message: 'Credits must be between 1 and 5' },
  'ERR_INVALID_CAPACITY': { status: 400, message: 'Capacity must be between 1 and 500' },
  'ERR_INVALID_ENUM_VALUE': { status: 400, message: 'Invalid enum value provided' },
  'ERR_MISSING_REQUIRED_FIELD': { status: 400, message: 'Required field is missing' },
  'ERR_CONDITIONAL_FIELD_REQUIRED': { status: 400, message: 'Conditional field is required for this delivery mode' },
  'ERR_FIELD_CONFLICT': { status: 400, message: 'Field conflicts with delivery mode or other constraints' },
  'ERR_UNKNOWN_FIELD': { status: 400, message: 'Unknown field provided in request' },
  'ERR_COURSE_WRONG_STATE': { status: 409, message: 'Course is in wrong state for this operation' },
  'ERR_ENROLLMENT_WRONG_STATE': { status: 409, message: 'Enrollment is in wrong state for this operation' },
  'ERR_TERM_NOT_ACTIVE': { status: 409, message: 'Term is not in active state for this operation' },
  'ERR_REGISTRATION_CLOSED': { status: 409, message: 'Registration is closed' },
  'ERR_COURSE_FULL': { status: 409, message: 'Course is at full capacity' },
  'ERR_CAPACITY_EXCEEDED': { status: 409, message: 'Operation would exceed course capacity' },
  'ERR_ALREADY_ENROLLED': { status: 409, message: 'Student is already enrolled or waitlisted in this course' },
  'ERR_NOT_ENROLLED': { status: 409, message: 'Student is not enrolled in this course' },
  'ERR_MAX_COURSES_REACHED': { status: 409, message: 'Professor has reached maximum course limit' },
  'ERR_CREDIT_LIMIT_EXCEEDED': { status: 409, message: 'Enrollment would exceed credit limit' },
  'ERR_TOO_MANY_DROPS': { status: 409, message: 'Student has reached maximum drop limit' },
  'ERR_ILLEGAL_COURSE_STATE_TRANSITION': { status: 409, message: 'Illegal course state transition' },
  'ERR_ILLEGAL_ENROLLMENT_STATE': { status: 409, message: 'Illegal enrollment state transition' },
  'ERR_FORBIDDEN': { status: 403, message: 'Access forbidden' },
  'ERR_TERM_NAME_NOT_UNIQUE': { status: 409, message: 'Term name must be unique' },
  'ERR_INVALID_INSTRUCTOR': { status: 422, message: 'Invalid instructor ID' },
  'ERR_REV_CONFLICT': { status: 409, message: 'Resource revision conflict - resource was modified by another user' },
  'ERR_INSUFFICIENT_FUNDS': { status: 402, message: 'Insufficient funds' },
  'ERR_OVERPAY_NOT_ALLOWED': { status: 422, message: 'Payment amount exceeds outstanding balance' },
  'ERR_INVALID_PAYMENT_AMOUNT': { status: 422, message: 'Invalid payment amount' },
  'ERR_LEDGER_INVALID_OP': { status: 422, message: 'Invalid ledger operation' }
};

class ApiError extends Error {
  constructor(errorId, message, statusCode, customMessage = null) {
    super(customMessage || message);
    this.errorId = errorId;
    this.statusCode = statusCode;
    this.message = customMessage || message;
  }
}

function createError(errorId, customMessage = null, customStatus = null) {
  const errorDef = ERROR_DEFINITIONS[errorId];
  if (!errorDef) {
    console.error(`Unknown error ID: ${errorId}`);
    return new ApiError('ERR_INTERNAL_ERROR', 'Internal server error', 500);
  }
  
  return new ApiError(
    errorId, 
    errorDef.message, 
    customStatus || errorDef.status, 
    customMessage
  );
}

module.exports = {
  ApiError,
  createError,
  ERROR_DEFINITIONS
};