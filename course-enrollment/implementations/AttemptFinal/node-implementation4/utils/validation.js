const CONSTANTS = require('../models/constants');
const { createError } = require('./errors');
const validator = require('validator');

class ValidationUtils {
  static validateUUID(id, fieldName = 'id') {
    if (!id || typeof id !== 'string') {
      throw createError('ERR_INVALID_ID_FORMAT', `${fieldName} is required and must be a string`);
    }
    
    if (!CONSTANTS.UUID_PATTERN.test(id)) {
      throw createError('ERR_INVALID_ID_FORMAT', `${fieldName} must be a valid UUID format`);
    }
    
    return true;
  }

  static validateCourseCode(code) {
    if (!code || typeof code !== 'string') {
      throw createError('ERR_INVALID_COURSE_CODE', 'Course code is required');
    }
    
    if (!CONSTANTS.COURSE_CODE_PATTERN.test(code)) {
      throw createError('ERR_INVALID_COURSE_CODE', 
        'Course code must be 2-4 uppercase letters followed by 3 digits (e.g., CS101, MATH101)');
    }
    
    return true;
  }

  static validateFieldLength(value, maxLength, fieldName, required = true) {
    if (!value) {
      if (required) {
        throw createError('ERR_MISSING_REQUIRED_FIELD', `${fieldName} is required`);
      }
      return true;
    }
    
    if (typeof value !== 'string') {
      throw createError('ERR_INVALID_FIELD_LENGTH', `${fieldName} must be a string`);
    }
    
    if (value.length > maxLength) {
      throw createError('ERR_INVALID_FIELD_LENGTH', 
        `${fieldName} must not exceed ${maxLength} characters`);
    }
    
    if (required && value.trim().length === 0) {
      throw createError('ERR_MISSING_REQUIRED_FIELD', `${fieldName} cannot be empty`);
    }
    
    return true;
  }

  static validateCredits(credits) {
    if (credits === undefined || credits === null) {
      throw createError('ERR_MISSING_REQUIRED_FIELD', 'Credits is required');
    }
    
    if (!Number.isInteger(credits) || credits < CONSTANTS.MIN_CREDITS || credits > CONSTANTS.MAX_CREDITS) {
      throw createError('ERR_INVALID_CREDITS', 
        `Credits must be an integer between ${CONSTANTS.MIN_CREDITS} and ${CONSTANTS.MAX_CREDITS}`);
    }
    
    return true;
  }

  static validateCapacity(capacity) {
    if (capacity === undefined || capacity === null) {
      throw createError('ERR_MISSING_REQUIRED_FIELD', 'Capacity is required');
    }
    
    if (!Number.isInteger(capacity) || capacity < CONSTANTS.MIN_CAPACITY || capacity > CONSTANTS.MAX_CAPACITY) {
      throw createError('ERR_INVALID_CAPACITY', 
        `Capacity must be an integer between ${CONSTANTS.MIN_CAPACITY} and ${CONSTANTS.MAX_CAPACITY}`);
    }
    
    return true;
  }

  static validateEnum(value, allowedValues, fieldName) {
    if (!value) {
      throw createError('ERR_MISSING_REQUIRED_FIELD', `${fieldName} is required`);
    }
    
    if (!allowedValues.includes(value)) {
      throw createError('ERR_INVALID_ENUM_VALUE', 
        `${fieldName} must be one of: ${allowedValues.join(', ')}`);
    }
    
    return true;
  }

  static validateDeliveryModeFields(deliveryMode, location, onlineLink) {
    // Validate delivery mode enum
    this.validateEnum(deliveryMode, Object.values(CONSTANTS.DELIVERY_MODES), 'delivery_mode');
    
    switch (deliveryMode) {
      case CONSTANTS.DELIVERY_MODES.IN_PERSON:
        if (!location || location.trim().length === 0) {
          throw createError('ERR_CONDITIONAL_FIELD_REQUIRED', 
            'Location is required for IN_PERSON delivery mode');
        }
        if (onlineLink) {
          throw createError('ERR_FIELD_CONFLICT', 
            'online_link cannot be provided for IN_PERSON delivery mode');
        }
        break;
        
      case CONSTANTS.DELIVERY_MODES.ONLINE:
        if (!onlineLink || !validator.isURL(onlineLink)) {
          throw createError('ERR_CONDITIONAL_FIELD_REQUIRED', 
            'Valid online_link URL is required for ONLINE delivery mode');
        }
        if (location) {
          throw createError('ERR_FIELD_CONFLICT', 
            'location cannot be provided for ONLINE delivery mode');
        }
        break;
        
      case CONSTANTS.DELIVERY_MODES.HYBRID:
        if (!location && !onlineLink) {
          throw createError('ERR_CONDITIONAL_FIELD_REQUIRED', 
            'At least one of location or online_link is required for HYBRID delivery mode');
        }
        if (onlineLink && !validator.isURL(onlineLink)) {
          throw createError('ERR_CONDITIONAL_FIELD_REQUIRED', 
            'online_link must be a valid URL if provided');
        }
        break;
    }
    
    return true;
  }

  static validatePaymentAmount(amount) {
    if (amount === undefined || amount === null) {
      throw createError('ERR_MISSING_REQUIRED_FIELD', 'Payment amount is required');
    }
    
    if (!Number.isInteger(amount) || amount <= 0) {
      throw createError('ERR_INVALID_PAYMENT_AMOUNT', 
        'Payment amount must be a positive integer (in cents)');
    }
    
    return true;
  }

  static validatePaginationParams(limit, offset) {
    const parsedLimit = limit ? parseInt(limit, 10) : CONSTANTS.DEFAULT_LIMIT;
    const parsedOffset = offset ? parseInt(offset, 10) : 0;
    
    if (isNaN(parsedLimit) || parsedLimit < 1 || parsedLimit > CONSTANTS.MAX_LIMIT) {
      throw createError('ERR_INVALID_FIELD_LENGTH', 
        `Limit must be between 1 and ${CONSTANTS.MAX_LIMIT}`);
    }
    
    if (isNaN(parsedOffset) || parsedOffset < 0) {
      throw createError('ERR_INVALID_FIELD_LENGTH', 
        'Offset must be a non-negative integer');
    }
    
    return { limit: parsedLimit, offset: parsedOffset };
  }

  static validateRequestBody(body, allowedFields) {
    const bodyKeys = Object.keys(body);
    const unknownFields = bodyKeys.filter(key => !allowedFields.includes(key));
    
    if (unknownFields.length > 0) {
      throw createError('ERR_UNKNOWN_FIELD', 
        `Unknown fields: ${unknownFields.join(', ')}`);
    }
    
    return true;
  }

  static validateTermState(currentState, targetState) {
    const stateTransitions = {
      [CONSTANTS.TERM_STATES.PLANNING]: [CONSTANTS.TERM_STATES.ENROLLMENT_OPEN],
      [CONSTANTS.TERM_STATES.ENROLLMENT_OPEN]: [CONSTANTS.TERM_STATES.ENROLLMENT_CLOSED],
      [CONSTANTS.TERM_STATES.ENROLLMENT_CLOSED]: [CONSTANTS.TERM_STATES.CONCLUDED],
      [CONSTANTS.TERM_STATES.CONCLUDED]: []
    };
    
    const allowedTransitions = stateTransitions[currentState] || [];
    if (!allowedTransitions.includes(targetState)) {
      throw createError('ERR_TERM_NOT_ACTIVE', 
        `Cannot transition from ${currentState} to ${targetState}`);
    }
    
    return true;
  }

  static validateCourseState(currentState, targetState) {
    const stateTransitions = {
      [CONSTANTS.COURSE_STATES.DRAFT]: [CONSTANTS.COURSE_STATES.OPEN, CONSTANTS.COURSE_STATES.CANCELLED],
      [CONSTANTS.COURSE_STATES.OPEN]: [CONSTANTS.COURSE_STATES.IN_PROGRESS, CONSTANTS.COURSE_STATES.CANCELLED],
      [CONSTANTS.COURSE_STATES.IN_PROGRESS]: [CONSTANTS.COURSE_STATES.COMPLETED, CONSTANTS.COURSE_STATES.CANCELLED],
      [CONSTANTS.COURSE_STATES.COMPLETED]: [],
      [CONSTANTS.COURSE_STATES.CANCELLED]: []
    };
    
    const allowedTransitions = stateTransitions[currentState] || [];
    if (!allowedTransitions.includes(targetState)) {
      throw createError('ERR_ILLEGAL_COURSE_STATE_TRANSITION', 
        `Cannot transition from ${currentState} to ${targetState}`);
    }
    
    return true;
  }

  static validateEnrollmentState(currentState, targetState) {
    const stateTransitions = {
      [CONSTANTS.ENROLLMENT_STATES.ENROLLED]: [CONSTANTS.ENROLLMENT_STATES.DROPPED, CONSTANTS.ENROLLMENT_STATES.COMPLETED],
      [CONSTANTS.ENROLLMENT_STATES.WAITLISTED]: [CONSTANTS.ENROLLMENT_STATES.ENROLLED, CONSTANTS.ENROLLMENT_STATES.DROPPED],
      [CONSTANTS.ENROLLMENT_STATES.DROPPED]: [],
      [CONSTANTS.ENROLLMENT_STATES.COMPLETED]: []
    };
    
    const allowedTransitions = stateTransitions[currentState] || [];
    if (!allowedTransitions.includes(targetState)) {
      throw createError('ERR_ILLEGAL_ENROLLMENT_STATE', 
        `Cannot transition from ${currentState} to ${targetState}`);
    }
    
    return true;
  }
}

module.exports = ValidationUtils;