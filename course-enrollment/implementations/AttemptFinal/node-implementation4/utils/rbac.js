const CONSTANTS = require('../models/constants');
const storage = require('../models/storage');

class RBACUtils {
  // Filter course data based on user role and context
  static filterCourseData(course, userRole, userId, enrollmentContext = null) {
    if (!course) return null;

    const filtered = { ...course };

    // Add derived fields
    const enrollments = storage.getEnrollmentsByCourse(course.id);
    const enrolledCount = enrollments.filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED).length;
    const waitlistCount = enrollments.filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED).length;
    const availableSeats = storage.getAvailableSeats(course.id);

    if (userRole === CONSTANTS.USER_ROLES.STUDENT) {
      // Students see limited data and only for published/active courses
      if (course.state === CONSTANTS.COURSE_STATES.DRAFT || 
          course.state === CONSTANTS.COURSE_STATES.CANCELLED) {
        // Hide cancelled/draft courses unless student is enrolled
        if (!enrollmentContext) {
          return null;
        }
      }

      // Add contextual enrollment flags if provided
      if (enrollmentContext) {
        filtered.is_enrolled = enrollmentContext.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED;
        filtered.is_waitlisted = enrollmentContext.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED;
      }

      // Students see available seats but not counts
      filtered.available_seats = availableSeats;
      
      // Students don't see raw counts or rosters
      delete filtered.enrolled_count;
      delete filtered.waitlist_count;
      
    } else if (userRole === CONSTANTS.USER_ROLES.PROFESSOR) {
      if (course.professor_id === userId) {
        // Professor teaching this course sees everything including roster
        filtered.enrolled_count = enrolledCount;
        filtered.waitlist_count = waitlistCount;
        filtered.available_seats = availableSeats;
        filtered.enrollments = enrollments.map(e => this.filterEnrollmentData(e, userRole, userId));
      } else {
        // Professor not teaching this course sees limited data
        if (course.state === CONSTANTS.COURSE_STATES.DRAFT) {
          return null; // Hide other professors' draft courses
        }
        filtered.available_seats = availableSeats;
        delete filtered.enrolled_count;
        delete filtered.waitlist_count;
      }
      
    } else if (userRole === CONSTANTS.USER_ROLES.REGISTRAR) {
      // Registrar sees everything
      filtered.enrolled_count = enrolledCount;
      filtered.waitlist_count = waitlistCount;
      filtered.available_seats = availableSeats;
      filtered.enrollments = enrollments.map(e => this.filterEnrollmentData(e, userRole, userId));
    }

    return filtered;
  }

  // Filter enrollment data based on user role and context
  static filterEnrollmentData(enrollment, userRole, userId) {
    if (!enrollment) return null;

    const filtered = { ...enrollment };

    if (userRole === CONSTANTS.USER_ROLES.STUDENT) {
      // Students can only see their own enrollments
      if (enrollment.student_id !== userId) {
        return null;
      }
    } else if (userRole === CONSTANTS.USER_ROLES.PROFESSOR) {
      // Professors can see enrollments for their courses
      const course = storage.get('courses', enrollment.course_id);
      if (!course || course.professor_id !== userId) {
        return null;
      }
    }
    // Registrar sees all enrollments

    return filtered;
  }

  // Filter list of courses based on user role
  static filterCourseList(courses, userRole, userId, termId = null) {
    return courses
      .map(course => {
        // Get student's enrollment context if they're a student
        let enrollmentContext = null;
        if (userRole === CONSTANTS.USER_ROLES.STUDENT && termId) {
          enrollmentContext = storage.findEnrollmentByStudentAndCourse(termId, userId, course.id);
        }
        
        return this.filterCourseData(course, userRole, userId, enrollmentContext);
      })
      .filter(course => course !== null);
  }

  // Filter list of enrollments based on user role
  static filterEnrollmentList(enrollments, userRole, userId) {
    return enrollments
      .map(enrollment => this.filterEnrollmentData(enrollment, userRole, userId))
      .filter(enrollment => enrollment !== null);
  }

  // Check if user can access a specific course
  static canAccessCourse(course, userRole, userId, termId = null) {
    if (userRole === CONSTANTS.USER_ROLES.REGISTRAR) {
      return true;
    }

    if (userRole === CONSTANTS.USER_ROLES.PROFESSOR) {
      // Professor can access their own courses or published courses
      if (course.professor_id === userId) {
        return true;
      }
      // Can access other published courses
      return course.state !== CONSTANTS.COURSE_STATES.DRAFT;
    }

    if (userRole === CONSTANTS.USER_ROLES.STUDENT) {
      // Students can access published courses or courses they're enrolled in
      if (course.state !== CONSTANTS.COURSE_STATES.DRAFT && 
          course.state !== CONSTANTS.COURSE_STATES.CANCELLED) {
        return true;
      }
      
      // Can access if enrolled (even if cancelled)
      if (termId) {
        const enrollment = storage.findEnrollmentByStudentAndCourse(termId, userId, course.id);
        return enrollment !== null;
      }
    }

    return false;
  }

  // Check if user can access enrollment list for a course
  static canAccessEnrollmentList(course, userRole, userId) {
    if (userRole === CONSTANTS.USER_ROLES.STUDENT) {
      return false; // Students cannot list enrollments
    }

    if (userRole === CONSTANTS.USER_ROLES.PROFESSOR) {
      return course.professor_id === userId; // Only for their own courses
    }

    return userRole === CONSTANTS.USER_ROLES.REGISTRAR; // Registrar can access all
  }

  // Check if user can perform action on course
  static canModifyCourse(course, userRole, userId) {
    if (userRole === CONSTANTS.USER_ROLES.REGISTRAR) {
      return true;
    }

    if (userRole === CONSTANTS.USER_ROLES.PROFESSOR) {
      return course.professor_id === userId;
    }

    return false;
  }

  // Check if user can create courses
  static canCreateCourse(userRole) {
    return userRole === CONSTANTS.USER_ROLES.PROFESSOR || 
           userRole === CONSTANTS.USER_ROLES.REGISTRAR;
  }

  // Check if user can enroll students
  static canEnrollStudent(userRole, targetStudentId, authUserId) {
    if (userRole === CONSTANTS.USER_ROLES.REGISTRAR) {
      return true;
    }

    if (userRole === CONSTANTS.USER_ROLES.STUDENT) {
      return targetStudentId === authUserId; // Only themselves
    }

    return false; // Professors cannot enroll students
  }

  // Check if user can drop an enrollment
  static canDropEnrollment(enrollment, course, userRole, userId) {
    if (userRole === CONSTANTS.USER_ROLES.REGISTRAR) {
      return true;
    }

    if (userRole === CONSTANTS.USER_ROLES.PROFESSOR) {
      return course.professor_id === userId;
    }

    if (userRole === CONSTANTS.USER_ROLES.STUDENT) {
      return enrollment.student_id === userId;
    }

    return false;
  }

  // Check if user can make payments
  static canMakePayment(userRole, targetStudentId, authUserId) {
    if (userRole === CONSTANTS.USER_ROLES.REGISTRAR) {
      return true;
    }

    if (userRole === CONSTANTS.USER_ROLES.STUDENT) {
      return targetStudentId === authUserId; // Only for themselves
    }

    return false; // Professors cannot make payments
  }
}

module.exports = RBACUtils;