const express = require('express');
const router = express.Router({ mergeParams: true });
const storage = require('../models/storage');
const CONSTANTS = require('../models/constants');
const ValidationUtils = require('../utils/validation');
const RBACUtils = require('../utils/rbac');
const WaitlistManager = require('../utils/waitlist');
const { createError } = require('../utils/errors');

// POST /terms/:termId/courses/:courseId/enrollments - Enroll in a course
router.post('/', (req, res, next) => {
  try {
    const { termId, courseId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');
    ValidationUtils.validateUUID(courseId, 'courseId');

    // Validate request body
    const allowedFields = ['student_id'];
    ValidationUtils.validateRequestBody(req.body, allowedFields);

    // Determine target student based on role
    let targetStudentId;
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      // Students can only enroll themselves
      targetStudentId = req.user.id;
      if (req.body.student_id && req.body.student_id !== req.user.id) {
        throw createError('ERR_FIELD_CONFLICT', 'Students can only enroll themselves');
      }
    } else if (req.user.role === CONSTANTS.USER_ROLES.REGISTRAR) {
      // Registrar must specify student_id
      if (!req.body.student_id) {
        throw createError('ERR_MISSING_REQUIRED_FIELD', 'student_id is required for Registrar enrollment');
      }
      ValidationUtils.validateUUID(req.body.student_id, 'student_id');
      targetStudentId = req.body.student_id;
      // Note: In real system, we'd validate student_id exists and has STUDENT role
    } else {
      throw createError('ERR_UNAUTHORIZED_ROLE', 'Only students and registrars can create enrollments');
    }

    // Validate term exists and is active
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    if (term.state !== CONSTANTS.TERM_STATES.ENROLLMENT_OPEN) {
      throw createError('ERR_REGISTRATION_CLOSED', 
        `Registration is ${term.state.toLowerCase()}`);
    }

    // Validate course exists and is open
    const course = storage.get('courses', courseId);
    if (!course || course.term_id !== termId) {
      throw createError('ERR_COURSE_NOT_FOUND');
    }

    if (course.state !== CONSTANTS.COURSE_STATES.OPEN) {
      throw createError('ERR_COURSE_WRONG_STATE', 
        `Course is ${course.state} and not open for enrollment`);
    }

    // Check for duplicate enrollment
    const existingEnrollment = storage.findEnrollmentByStudentAndCourse(termId, targetStudentId, courseId);
    if (existingEnrollment) {
      throw createError('ERR_ALREADY_ENROLLED', 
        `Student is already ${existingEnrollment.state.toLowerCase()} in this course`);
    }

    // Check credit limit for students (not for Registrar override)
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      const studentEnrollments = storage.getEnrollmentsByStudent(termId, targetStudentId);
      const currentCredits = studentEnrollments
        .filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED)
        .reduce((total, enrollment) => {
          const enrollmentCourse = storage.get('courses', enrollment.course_id);
          return total + (enrollmentCourse ? enrollmentCourse.credits : 0);
        }, 0);

      if (currentCredits + course.credits > CONSTANTS.MAX_CREDITS_PER_TERM) {
        throw createError('ERR_CREDIT_LIMIT_EXCEEDED', 
          `Enrollment would exceed ${CONSTANTS.MAX_CREDITS_PER_TERM} credit limit`);
      }
    }

    // Determine enrollment state based on seat availability
    const availableSeats = storage.getAvailableSeats(courseId);
    const enrollmentState = availableSeats > 0 ? 
      CONSTANTS.ENROLLMENT_STATES.ENROLLED : 
      CONSTANTS.ENROLLMENT_STATES.WAITLISTED;

    // Create the enrollment
    const newEnrollment = {
      term_id: termId,
      course_id: courseId,
      student_id: targetStudentId,
      state: enrollmentState
    };

    const createdEnrollment = storage.createEnrollment(newEnrollment);

    // Update ledgers if enrolled (not waitlisted)
    if (enrollmentState === CONSTANTS.ENROLLMENT_STATES.ENROLLED) {
      storage.debitSeatLedger(courseId, 1);
      const tuitionCost = course.credits * CONSTANTS.COST_PER_CREDIT;
      storage.creditTuitionLedger(termId, targetStudentId, tuitionCost);
    }

    // Filter response based on user role
    const filteredEnrollment = RBACUtils.filterEnrollmentData(createdEnrollment, req.user.role, req.user.id);

    res.status(201).json(filteredEnrollment);
  } catch (error) {
    next(error);
  }
});

// GET /terms/:termId/courses/:courseId/enrollments - List enrollments for a course
router.get('/', (req, res, next) => {
  try {
    const { termId, courseId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');
    ValidationUtils.validateUUID(courseId, 'courseId');

    // Validate pagination parameters
    const { limit, offset } = ValidationUtils.validatePaginationParams(req.query.limit, req.query.offset);

    // Validate term and course exist
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    const course = storage.get('courses', courseId);
    if (!course || course.term_id !== termId) {
      throw createError('ERR_COURSE_NOT_FOUND');
    }

    // Check if user can access enrollment list
    if (!RBACUtils.canAccessEnrollmentList(course, req.user.role, req.user.id)) {
      throw createError('ERR_UNAUTHORIZED_ROLE', 'Cannot access enrollment list');
    }

    // Get all enrollments for the course
    let enrollments = storage.getEnrollmentsByCourse(courseId);

    // Apply state filter if provided
    if (req.query.state) {
      ValidationUtils.validateEnum(req.query.state, Object.values(CONSTANTS.ENROLLMENT_STATES), 'state');
      enrollments = enrollments.filter(enrollment => enrollment.state === req.query.state);
    }

    // Filter enrollments based on user role
    const filteredEnrollments = RBACUtils.filterEnrollmentList(enrollments, req.user.role, req.user.id);

    // Sort by creation time (waitlist order)
    filteredEnrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Apply pagination
    const total = filteredEnrollments.length;
    const paginatedEnrollments = filteredEnrollments.slice(offset, offset + limit);

    res.jsonPaginated(paginatedEnrollments, total, limit, offset);
  } catch (error) {
    next(error);
  }
});

// GET /terms/:termId/courses/:courseId/enrollments/:enrollmentId - Get specific enrollment
router.get('/:enrollmentId', (req, res, next) => {
  try {
    const { termId, courseId, enrollmentId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');
    ValidationUtils.validateUUID(courseId, 'courseId');
    ValidationUtils.validateUUID(enrollmentId, 'enrollmentId');

    // Validate term and course exist
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    const course = storage.get('courses', courseId);
    if (!course || course.term_id !== termId) {
      throw createError('ERR_COURSE_NOT_FOUND');
    }

    // Get the enrollment
    const enrollment = storage.get('enrollments', enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId || enrollment.term_id !== termId) {
      throw createError('ERR_ENROLLMENT_NOT_FOUND');
    }

    // Filter enrollment based on user role
    const filteredEnrollment = RBACUtils.filterEnrollmentData(enrollment, req.user.role, req.user.id);

    if (!filteredEnrollment) {
      throw createError('ERR_ENROLLMENT_NOT_FOUND'); // Hide existence
    }

    // Add waitlist position if applicable
    if (filteredEnrollment.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED) {
      filteredEnrollment.waitlist_position = WaitlistManager.getWaitlistPosition(courseId, enrollment.student_id);
    }

    res.json(filteredEnrollment);
  } catch (error) {
    next(error);
  }
});

// PATCH /terms/:termId/courses/:courseId/enrollments/:enrollmentId:drop - Drop an enrollment
router.patch('/:enrollmentId\\:drop', (req, res, next) => {
  try {
    const { termId, courseId, enrollmentId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');
    ValidationUtils.validateUUID(courseId, 'courseId');
    ValidationUtils.validateUUID(enrollmentId, 'enrollmentId');

    // Validate term and course exist
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Don't allow drops in concluded terms
    if (term.state === CONSTANTS.TERM_STATES.CONCLUDED) {
      throw createError('ERR_TERM_NOT_ACTIVE', 'Cannot drop from concluded term');
    }

    const course = storage.get('courses', courseId);
    if (!course || course.term_id !== termId) {
      throw createError('ERR_COURSE_NOT_FOUND');
    }

    // Get the enrollment
    const enrollment = storage.get('enrollments', enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId || enrollment.term_id !== termId) {
      throw createError('ERR_ENROLLMENT_NOT_FOUND');
    }

    // Check if user can drop this enrollment
    if (!RBACUtils.canDropEnrollment(enrollment, course, req.user.role, req.user.id)) {
      if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
        throw createError('ERR_FORBIDDEN', 'Can only drop your own enrollments');
      } else if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR) {
        throw createError('ERR_NOT_INSTRUCTOR', 'Can only drop enrollments from your own courses');
      } else {
        throw createError('ERR_UNAUTHORIZED_ROLE');
      }
    }

    // Validate enrollment state allows dropping
    const dropableStates = [CONSTANTS.ENROLLMENT_STATES.ENROLLED, CONSTANTS.ENROLLMENT_STATES.WAITLISTED];
    if (!dropableStates.includes(enrollment.state)) {
      throw createError('ERR_ENROLLMENT_WRONG_STATE', 
        `Cannot drop enrollment in ${enrollment.state} state`);
    }

    // Check drop count limit for students
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT && enrollment.student_id === req.user.id) {
      const currentDropCount = storage.getDropCount(termId, req.user.id);
      if (currentDropCount >= CONSTANTS.MAX_DROP_COUNT_PER_TERM) {
        throw createError('ERR_TOO_MANY_DROPS', 
          `Student has reached maximum of ${CONSTANTS.MAX_DROP_COUNT_PER_TERM} drops per term`);
      }
    }

    // Handle revision conflict if provided
    if (req.body.revision !== undefined && req.body.revision !== enrollment.revision) {
      throw createError('ERR_REV_CONFLICT');
    }

    const previousState = enrollment.state;

    // Update enrollment to DROPPED
    const updatedEnrollment = storage.update('enrollments', enrollmentId, {
      state: CONSTANTS.ENROLLMENT_STATES.DROPPED
    });

    // Handle ledger updates based on previous state
    if (previousState === CONSTANTS.ENROLLMENT_STATES.ENROLLED) {
      // Free up seat and refund tuition
      storage.creditSeatLedger(courseId, 1);
      const tuitionCost = course.credits * CONSTANTS.COST_PER_CREDIT;
      storage.debitTuitionLedger(termId, enrollment.student_id, tuitionCost);

      // Trigger waitlist promotion (asynchronous)
      WaitlistManager.onSeatVacated(courseId);
    }

    // Handle drop count and penalty for student-initiated drops
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT && enrollment.student_id === req.user.id) {
      const newDropCount = storage.incrementDropCount(termId, req.user.id);
      
      // Apply penalty on third drop
      if (newDropCount === CONSTANTS.MAX_DROP_COUNT_PER_TERM) {
        storage.creditTuitionLedger(termId, req.user.id, CONSTANTS.DROP_PENALTY_FEE);
        console.log(`Applied drop penalty of ${CONSTANTS.DROP_PENALTY_FEE} cents to student ${req.user.id}`);
      }
    }

    // Filter response based on user role
    const filteredEnrollment = RBACUtils.filterEnrollmentData(updatedEnrollment, req.user.role, req.user.id);

    res.json(filteredEnrollment);
  } catch (error) {
    next(error);
  }
});

module.exports = router;