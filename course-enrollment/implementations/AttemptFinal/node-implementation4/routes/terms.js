const express = require('express');
const router = express.Router();
const storage = require('../models/storage');
const CONSTANTS = require('../models/constants');
const ValidationUtils = require('../utils/validation');
const { createError } = require('../utils/errors');

// POST /terms - Create a new academic term
router.post('/', (req, res, next) => {
  try {
    // Only Registrar can create terms
    if (req.user.role !== CONSTANTS.USER_ROLES.REGISTRAR) {
      throw createError('ERR_UNAUTHORIZED_ROLE');
    }

    // Validate request body
    const allowedFields = ['name'];
    ValidationUtils.validateRequestBody(req.body, allowedFields);

    const { name } = req.body;

    // Validate required fields
    ValidationUtils.validateFieldLength(name, 100, 'name', true);

    // Check if term name is unique
    const existingTerms = storage.getAll('terms');
    const existingTerm = existingTerms.find(term => term.name === name);
    if (existingTerm) {
      throw createError('ERR_TERM_NAME_NOT_UNIQUE');
    }

    // Create the term
    const newTerm = {
      name: name.trim(),
      state: CONSTANTS.TERM_STATES.PLANNING,
      created_by: req.user.id
    };

    const createdTerm = storage.createTerm(newTerm);

    res.status(201).json(createdTerm);
  } catch (error) {
    next(error);
  }
});

// GET /terms/:termId - Retrieve details of an academic term
router.get('/:termId', (req, res, next) => {
  try {
    const { termId } = req.params;

    // Validate term ID format
    ValidationUtils.validateUUID(termId, 'termId');

    // Get the term
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Check if term is concluded and hide from non-registrars
    if (term.state === CONSTANTS.TERM_STATES.CONCLUDED && req.user.role !== CONSTANTS.USER_ROLES.REGISTRAR) {
      throw createError('ERR_TERM_CLOSED');
    }

    res.json(term);
  } catch (error) {
    next(error);
  }
});

// PATCH /terms/:termId:open-registration - Open registration for the term
router.patch('/:termId\\:open-registration', (req, res, next) => {
  try {
    // Only Registrar can open registration
    if (req.user.role !== CONSTANTS.USER_ROLES.REGISTRAR) {
      throw createError('ERR_UNAUTHORIZED_ROLE');
    }

    const { termId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');

    // Get the term
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Validate current state allows opening
    if (term.state !== CONSTANTS.TERM_STATES.PLANNING) {
      throw createError('ERR_TERM_NOT_ACTIVE', 
        `Cannot open registration - term is in ${term.state} state`);
    }

    // Handle revision conflict if provided
    if (req.body.revision !== undefined && req.body.revision !== term.revision) {
      throw createError('ERR_REV_CONFLICT');
    }

    // Transition to ENROLLMENT_OPEN
    const updatedTerm = storage.update('terms', termId, {
      state: CONSTANTS.TERM_STATES.ENROLLMENT_OPEN
    });

    res.json(updatedTerm);
  } catch (error) {
    next(error);
  }
});

// PATCH /terms/:termId:close-registration - Close registration for the term
router.patch('/:termId\\:close-registration', (req, res, next) => {
  try {
    // Only Registrar can close registration
    if (req.user.role !== CONSTANTS.USER_ROLES.REGISTRAR) {
      throw createError('ERR_UNAUTHORIZED_ROLE');
    }

    const { termId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');

    // Get the term
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Validate current state allows closing
    if (term.state !== CONSTANTS.TERM_STATES.ENROLLMENT_OPEN) {
      throw createError('ERR_TERM_NOT_ACTIVE', 
        `Cannot close registration - term is in ${term.state} state`);
    }

    // Handle revision conflict if provided
    if (req.body.revision !== undefined && req.body.revision !== term.revision) {
      throw createError('ERR_REV_CONFLICT');
    }

    // Transition all OPEN courses to IN_PROGRESS
    const courses = storage.getCoursesByTerm(termId);
    courses.forEach(course => {
      if (course.state === CONSTANTS.COURSE_STATES.OPEN) {
        storage.update('courses', course.id, {
          state: CONSTANTS.COURSE_STATES.IN_PROGRESS
        });
      }
    });

    // Transition term to ENROLLMENT_CLOSED
    const updatedTerm = storage.update('terms', termId, {
      state: CONSTANTS.TERM_STATES.ENROLLMENT_CLOSED
    });

    res.json(updatedTerm);
  } catch (error) {
    next(error);
  }
});

// PATCH /terms/:termId:conclude - Conclude the term
router.patch('/:termId\\:conclude', (req, res, next) => {
  try {
    // Only Registrar can conclude terms
    if (req.user.role !== CONSTANTS.USER_ROLES.REGISTRAR) {
      throw createError('ERR_UNAUTHORIZED_ROLE');
    }

    const { termId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');

    // Get the term
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Validate current state allows conclusion
    if (term.state !== CONSTANTS.TERM_STATES.ENROLLMENT_CLOSED) {
      throw createError('ERR_TERM_NOT_ACTIVE', 
        `Cannot conclude term - must close registration first. Term is in ${term.state} state`);
    }

    // Handle revision conflict if provided
    if (req.body.revision !== undefined && req.body.revision !== term.revision) {
      throw createError('ERR_REV_CONFLICT');
    }

    // Transition all IN_PROGRESS courses to COMPLETED
    const courses = storage.getCoursesByTerm(termId);
    courses.forEach(course => {
      if (course.state === CONSTANTS.COURSE_STATES.IN_PROGRESS) {
        storage.update('courses', course.id, {
          state: CONSTANTS.COURSE_STATES.COMPLETED
        });
      }
    });

    // Finalize all active enrollments
    courses.forEach(course => {
      const enrollments = storage.getEnrollmentsByCourse(course.id);
      enrollments.forEach(enrollment => {
        if (enrollment.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED) {
          storage.update('enrollments', enrollment.id, {
            state: CONSTANTS.ENROLLMENT_STATES.COMPLETED
          });
        } else if (enrollment.state === CONSTANTS.ENROLLMENT_STATES.WAITLISTED) {
          storage.update('enrollments', enrollment.id, {
            state: CONSTANTS.ENROLLMENT_STATES.DROPPED
          });
        }
      });
    });

    // Transition term to CONCLUDED
    const updatedTerm = storage.update('terms', termId, {
      state: CONSTANTS.TERM_STATES.CONCLUDED
    });

    res.json(updatedTerm);
  } catch (error) {
    next(error);
  }
});

module.exports = router;