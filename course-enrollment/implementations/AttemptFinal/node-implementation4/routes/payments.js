const express = require('express');
const router = express.Router({ mergeParams: true });
const storage = require('../models/storage');
const CONSTANTS = require('../models/constants');
const ValidationUtils = require('../utils/validation');
const RBACUtils = require('../utils/rbac');
const { createError } = require('../utils/errors');

// POST /terms/:termId/students/:studentId:pay - Record a tuition payment
router.post('/:studentId\\:pay', (req, res, next) => {
  try {
    const { termId, studentId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');
    ValidationUtils.validateUUID(studentId, 'studentId');

    // Check if user can make payment for this student
    if (!RBACUtils.canMakePayment(req.user.role, studentId, req.user.id)) {
      if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
        throw createError('ERR_FORBIDDEN', 'Students can only make payments for themselves');
      } else {
        throw createError('ERR_UNAUTHORIZED_ROLE', 'Only students and registrars can make payments');
      }
    }

    // Validate request body
    const allowedFields = ['amount'];
    ValidationUtils.validateRequestBody(req.body, allowedFields);

    const { amount } = req.body;

    // Validate payment amount
    ValidationUtils.validatePaymentAmount(amount);

    // Validate term exists
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Note: In a real system, we'd validate that studentId exists and has STUDENT role
    // For this implementation, we'll assume it's valid

    // Get current tuition balance
    const currentBalance = storage.getTuitionBalance(termId, studentId);

    // Check for overpayment
    if (amount > currentBalance) {
      throw createError('ERR_OVERPAY_NOT_ALLOWED', 
        `Payment amount (${amount}) exceeds outstanding balance (${currentBalance})`);
    }

    // Process the payment
    const newBalance = storage.debitTuitionLedger(termId, studentId, amount);

    // Create payment response
    const paymentResponse = {
      term_id: termId,
      student_id: studentId,
      payment_amount: amount,
      previous_balance: currentBalance,
      new_balance: newBalance,
      payment_timestamp: new Date().toISOString()
    };

    // Log if balance is now fully paid
    if (newBalance === 0) {
      console.log(`Student ${studentId} has fully paid tuition for term ${termId}`);
    }

    res.json(paymentResponse);
  } catch (error) {
    next(error);
  }
});

// GET /terms/:termId/students/:studentId/balance - Get student's tuition balance (convenience endpoint)
router.get('/:studentId/balance', (req, res, next) => {
  try {
    const { termId, studentId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');
    ValidationUtils.validateUUID(studentId, 'studentId');

    // Check if user can view balance for this student
    if (!RBACUtils.canMakePayment(req.user.role, studentId, req.user.id)) {
      if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
        throw createError('ERR_FORBIDDEN', 'Students can only view their own balance');
      } else {
        throw createError('ERR_UNAUTHORIZED_ROLE', 'Only students and registrars can view balances');
      }
    }

    // Validate term exists
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Get tuition balance
    const balance = storage.getTuitionBalance(termId, studentId);

    // Get enrolled courses for context
    const enrollments = storage.getEnrollmentsByStudent(termId, studentId);
    const enrolledCourses = enrollments
      .filter(e => e.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED)
      .map(e => {
        const course = storage.get('courses', e.course_id);
        return {
          course_id: e.course_id,
          course_code: course ? course.code : 'UNKNOWN',
          credits: course ? course.credits : 0,
          tuition_cost: course ? course.credits * CONSTANTS.COST_PER_CREDIT : 0
        };
      });

    const totalCredits = enrolledCourses.reduce((sum, course) => sum + course.credits, 0);
    const totalExpectedTuition = enrolledCourses.reduce((sum, course) => sum + course.tuition_cost, 0);

    const balanceResponse = {
      term_id: termId,
      student_id: studentId,
      outstanding_balance: balance,
      total_credits: totalCredits,
      expected_total_tuition: totalExpectedTuition,
      amount_paid: totalExpectedTuition - balance,
      enrolled_courses: enrolledCourses,
      is_fully_paid: balance === 0
    };

    res.json(balanceResponse);
  } catch (error) {
    next(error);
  }
});

module.exports = router;