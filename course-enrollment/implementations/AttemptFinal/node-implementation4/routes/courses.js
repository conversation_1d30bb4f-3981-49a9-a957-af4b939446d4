const express = require('express');
const router = express.Router({ mergeParams: true });
const storage = require('../models/storage');
const CONSTANTS = require('../models/constants');
const ValidationUtils = require('../utils/validation');
const RBACUtils = require('../utils/rbac');
const { createError } = require('../utils/errors');

// POST /terms/:termId/courses - Create a new course
router.post('/', (req, res, next) => {
  try {
    const { termId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');

    // Check if user can create courses
    if (!RBACUtils.canCreateCourse(req.user.role)) {
      throw createError('ERR_UNAUTHORIZED_ROLE');
    }

    // Validate term exists and is accessible
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Validate request body
    const allowedFields = ['code', 'title', 'description', 'credits', 'capacity', 
                          'delivery_mode', 'location', 'online_link', 'professor_id'];
    ValidationUtils.validateRequestBody(req.body, allowedFields);

    const { code, title, description, credits, capacity, delivery_mode, 
            location, online_link, professor_id } = req.body;

    // Validate required fields
    ValidationUtils.validateCourseCode(code);
    ValidationUtils.validateFieldLength(title, CONSTANTS.MAX_TITLE_LENGTH, 'title', true);
    ValidationUtils.validateFieldLength(description, CONSTANTS.MAX_DESCRIPTION_LENGTH, 'description', false);
    ValidationUtils.validateCredits(credits);
    ValidationUtils.validateCapacity(capacity);
    ValidationUtils.validateDeliveryModeFields(delivery_mode, location, online_link);

    // Check course code uniqueness within term
    const existingCourses = storage.getCoursesByTerm(termId);
    const duplicateCode = existingCourses.find(course => course.code === code);
    if (duplicateCode) {
      throw createError('ERR_COURSE_CODE_NOT_UNIQUE');
    }

    // Determine professor ID based on caller role
    let assignedProfessorId;
    if (req.user.role === CONSTANTS.USER_ROLES.REGISTRAR) {
      if (!professor_id) {
        throw createError('ERR_MISSING_REQUIRED_FIELD', 'professor_id is required when Registrar creates course');
      }
      ValidationUtils.validateUUID(professor_id, 'professor_id');
      assignedProfessorId = professor_id;
      
      // Note: In a real system, we'd validate professor_id exists and has PROFESSOR role
      // For this implementation, we'll assume it's valid
    } else if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR) {
      if (professor_id && professor_id !== req.user.id) {
        throw createError('ERR_FIELD_CONFLICT', 'Professor cannot create course for another instructor');
      }
      assignedProfessorId = req.user.id;
    }

    // Check professor course limit
    const profCourses = storage.getCoursesByProfessor(termId, assignedProfessorId);
    const activeCourses = profCourses.filter(course => 
      course.state !== CONSTANTS.COURSE_STATES.CANCELLED
    );
    if (activeCourses.length >= CONSTANTS.MAX_COURSES_PER_PROF) {
      throw createError('ERR_MAX_COURSES_REACHED');
    }

    // Create the course
    const newCourse = {
      term_id: termId,
      code: code.toUpperCase(),
      title: title.trim(),
      description: description ? description.trim() : null,
      credits,
      capacity,
      delivery_mode,
      location: location ? location.trim() : null,
      online_link: online_link || null,
      professor_id: assignedProfessorId,
      state: CONSTANTS.COURSE_STATES.DRAFT
    };

    const createdCourse = storage.createCourse(newCourse);

    // Filter response based on user role
    const filteredCourse = RBACUtils.filterCourseData(createdCourse, req.user.role, req.user.id);

    res.status(201).json(filteredCourse);
  } catch (error) {
    next(error);
  }
});

// GET /terms/:termId/courses - List courses in the term
router.get('/', (req, res, next) => {
  try {
    const { termId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');

    // Validate pagination parameters
    const { limit, offset } = ValidationUtils.validatePaginationParams(req.query.limit, req.query.offset);

    // Validate term exists and is accessible
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Check if term is accessible
    if (term.state === CONSTANTS.TERM_STATES.CONCLUDED && req.user.role !== CONSTANTS.USER_ROLES.REGISTRAR) {
      throw createError('ERR_TERM_CLOSED');
    }

    // Get all courses for the term
    let courses = storage.getCoursesByTerm(termId);

    // Apply query filters (for admin convenience)
    if (req.query.state && req.user.role === CONSTANTS.USER_ROLES.REGISTRAR) {
      ValidationUtils.validateEnum(req.query.state, Object.values(CONSTANTS.COURSE_STATES), 'state');
      courses = courses.filter(course => course.state === req.query.state);
    }

    if (req.query.professor_id && req.user.role === CONSTANTS.USER_ROLES.REGISTRAR) {
      ValidationUtils.validateUUID(req.query.professor_id, 'professor_id');
      courses = courses.filter(course => course.professor_id === req.query.professor_id);
    }

    // Filter courses based on user role
    const filteredCourses = RBACUtils.filterCourseList(courses, req.user.role, req.user.id, termId);

    // Apply pagination
    const total = filteredCourses.length;
    const paginatedCourses = filteredCourses.slice(offset, offset + limit);

    res.jsonPaginated(paginatedCourses, total, limit, offset);
  } catch (error) {
    next(error);
  }
});

// GET /terms/:termId/courses/:courseId - Get detailed course info
router.get('/:courseId', (req, res, next) => {
  try {
    const { termId, courseId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');
    ValidationUtils.validateUUID(courseId, 'courseId');

    // Validate term exists
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Get the course
    const course = storage.get('courses', courseId);
    if (!course || course.term_id !== termId) {
      throw createError('ERR_COURSE_NOT_FOUND');
    }

    // Check if user can access this course
    if (!RBACUtils.canAccessCourse(course, req.user.role, req.user.id, termId)) {
      throw createError('ERR_COURSE_NOT_FOUND'); // Hide existence
    }

    // Get student's enrollment context if applicable
    let enrollmentContext = null;
    if (req.user.role === CONSTANTS.USER_ROLES.STUDENT) {
      enrollmentContext = storage.findEnrollmentByStudentAndCourse(termId, req.user.id, courseId);
    }

    // Filter course data based on user role
    const filteredCourse = RBACUtils.filterCourseData(course, req.user.role, req.user.id, enrollmentContext);

    if (!filteredCourse) {
      throw createError('ERR_COURSE_NOT_FOUND');
    }

    res.json(filteredCourse);
  } catch (error) {
    next(error);
  }
});

// PATCH /terms/:termId/courses/:courseId:publish - Publish a draft course
router.patch('/:courseId\\:publish', (req, res, next) => {
  try {
    const { termId, courseId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');
    ValidationUtils.validateUUID(courseId, 'courseId');

    // Validate term exists
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Get the course
    const course = storage.get('courses', courseId);
    if (!course || course.term_id !== termId) {
      throw createError('ERR_COURSE_NOT_FOUND');
    }

    // Check if user can modify this course
    if (!RBACUtils.canModifyCourse(course, req.user.role, req.user.id)) {
      if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR) {
        throw createError('ERR_NOT_INSTRUCTOR');
      } else {
        throw createError('ERR_UNAUTHORIZED_ROLE');
      }
    }

    // Validate course is in DRAFT state
    if (course.state !== CONSTANTS.COURSE_STATES.DRAFT) {
      throw createError('ERR_COURSE_WRONG_STATE', 
        `Cannot publish course in ${course.state} state`);
    }

    // Validate term is open for enrollment
    if (term.state !== CONSTANTS.TERM_STATES.ENROLLMENT_OPEN) {
      throw createError('ERR_TERM_NOT_ACTIVE', 
        'Cannot publish course - term enrollment is not open');
    }

    // Handle revision conflict if provided
    if (req.body.revision !== undefined && req.body.revision !== course.revision) {
      throw createError('ERR_REV_CONFLICT');
    }

    // Initialize seat ledger
    storage.initializeSeatLedger(courseId, course.capacity);

    // Transition to OPEN state
    const updatedCourse = storage.update('courses', courseId, {
      state: CONSTANTS.COURSE_STATES.OPEN
    });

    // Filter response based on user role
    const filteredCourse = RBACUtils.filterCourseData(updatedCourse, req.user.role, req.user.id);

    res.json(filteredCourse);
  } catch (error) {
    next(error);
  }
});

// PATCH /terms/:termId/courses/:courseId:cancel - Cancel a course
router.patch('/:courseId\\:cancel', (req, res, next) => {
  try {
    const { termId, courseId } = req.params;
    ValidationUtils.validateUUID(termId, 'termId');
    ValidationUtils.validateUUID(courseId, 'courseId');

    // Validate term exists
    const term = storage.get('terms', termId);
    if (!term) {
      throw createError('ERR_TERM_NOT_FOUND');
    }

    // Get the course
    const course = storage.get('courses', courseId);
    if (!course || course.term_id !== termId) {
      throw createError('ERR_COURSE_NOT_FOUND');
    }

    // Check if user can modify this course
    if (!RBACUtils.canModifyCourse(course, req.user.role, req.user.id)) {
      if (req.user.role === CONSTANTS.USER_ROLES.PROFESSOR) {
        throw createError('ERR_NOT_INSTRUCTOR');
      } else {
        throw createError('ERR_UNAUTHORIZED_ROLE');
      }
    }

    // Validate course can be cancelled
    const cancelableStates = [
      CONSTANTS.COURSE_STATES.DRAFT,
      CONSTANTS.COURSE_STATES.OPEN,
      CONSTANTS.COURSE_STATES.IN_PROGRESS
    ];
    if (!cancelableStates.includes(course.state)) {
      throw createError('ERR_COURSE_WRONG_STATE', 
        `Cannot cancel course in ${course.state} state`);
    }

    // Handle revision conflict if provided
    if (req.body.revision !== undefined && req.body.revision !== course.revision) {
      throw createError('ERR_REV_CONFLICT');
    }

    // Cancel all enrollments and handle refunds
    const enrollments = storage.getEnrollmentsByCourse(courseId);
    enrollments.forEach(enrollment => {
      if (enrollment.state === CONSTANTS.ENROLLMENT_STATES.ENROLLED) {
        // Refund tuition for enrolled students
        const tuitionCost = course.credits * CONSTANTS.COST_PER_CREDIT;
        storage.debitTuitionLedger(termId, enrollment.student_id, tuitionCost);
      }
      
      // Mark enrollment as dropped
      if (enrollment.state !== CONSTANTS.ENROLLMENT_STATES.DROPPED) {
        storage.update('enrollments', enrollment.id, {
          state: CONSTANTS.ENROLLMENT_STATES.DROPPED
        });
      }
    });

    // Transition course to CANCELLED
    const updatedCourse = storage.update('courses', courseId, {
      state: CONSTANTS.COURSE_STATES.CANCELLED
    });

    // Filter response based on user role
    const filteredCourse = RBACUtils.filterCourseData(updatedCourse, req.user.role, req.user.id);

    res.json(filteredCourse);
  } catch (error) {
    next(error);
  }
});

module.exports = router;