const express = require('express');
const app = express();

// Middleware
app.use(express.json());

// Import middleware
const authMiddleware = require('./middleware/auth');
const responseMiddleware = require('./middleware/response');
const errorHandler = require('./middleware/errorHandler');

// Apply global middleware
app.use(authMiddleware);
app.use(responseMiddleware);

// Import routes
const termRoutes = require('./routes/terms');
const courseRoutes = require('./routes/courses');
const enrollmentRoutes = require('./routes/enrollments');
const paymentRoutes = require('./routes/payments');

// Routes
app.use('/terms', termRoutes);
app.use('/terms/:termId/courses', courseRoutes);
app.use('/terms/:termId/courses/:courseId/enrollments', enrollmentRoutes);
app.use('/terms/:termId/students', paymentRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Global error handler
app.use(errorHandler);

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`University Course Registration API running on port ${PORT}`);
});

module.exports = app;