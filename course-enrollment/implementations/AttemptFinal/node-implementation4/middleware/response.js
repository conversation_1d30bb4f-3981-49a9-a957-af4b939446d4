const { v4: uuidv4 } = require('uuid');

function responseMiddleware(req, res, next) {
  // Generate unique request ID
  req.requestId = `req_${uuidv4().replace(/-/g, '').substring(0, 16)}`;
  req.requestTimestamp = new Date().toISOString();
  
  // Override res.json to wrap responses in standard format
  const originalJson = res.json.bind(res);
  
  res.json = function(data) {
    const envelope = {
      meta: {
        api_request_id: req.requestId,
        api_request_timestamp: req.requestTimestamp
      },
      response_type: Array.isArray(data) ? 'array' : 'object',
      data: data || {}
    };
    
    return originalJson(envelope);
  };
  
  // Helper method for paginated responses
  res.jsonPaginated = function(data, total, limit, offset) {
    const envelope = {
      meta: {
        api_request_id: req.requestId,
        api_request_timestamp: req.requestTimestamp,
        total: total,
        limit: limit,
        offset: offset,
        has_more: offset + data.length < total
      },
      response_type: 'array',
      data: data || []
    };
    
    return originalJson(envelope);
  };
  
  next();
}

module.exports = responseMiddleware;