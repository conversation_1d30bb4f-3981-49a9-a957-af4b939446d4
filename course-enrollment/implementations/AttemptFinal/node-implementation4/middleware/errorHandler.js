const { ApiError } = require('../utils/errors');

function errorHandler(err, req, res, next) {
  // If response already sent, delegate to default Express error handler
  if (res.headersSent) {
    return next(err);
  }
  
  let statusCode = 500;
  let errorId = 'ERR_INTERNAL_ERROR';
  let message = 'Internal server error';
  
  if (err instanceof ApiError) {
    statusCode = err.statusCode;
    errorId = err.errorId;
    message = err.message;
  } else if (err.name === 'ValidationError') {
    statusCode = 400;
    errorId = 'ERR_INVALID_FIELD_LENGTH';
    message = err.message;
  } else if (err.name === 'CastError') {
    statusCode = 400;
    errorId = 'ERR_INVALID_ID_FORMAT';
    message = 'Invalid ID format';
  } else {
    // Log unexpected errors
    console.error('Unexpected error:', err);
  }
  
  const errorResponse = {
    meta: {
      api_request_id: req.requestId || 'unknown',
      api_request_timestamp: req.requestTimestamp || new Date().toISOString()
    },
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };
  
  res.status(statusCode).json(errorResponse);
}

module.exports = errorHandler;