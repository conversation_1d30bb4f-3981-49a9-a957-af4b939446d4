const CONSTANTS = require('../models/constants');
const { createError } = require('../utils/errors');

function authMiddleware(req, res, next) {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];
  
  // Check if headers are present
  if (!userId || !userRole) {
    return next(createError('ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER', 
      'Missing required X-User-ID or X-User-Role header', 400));
  }
  
  // Validate UUID format for user ID
  if (!CONSTANTS.UUID_PATTERN.test(userId)) {
    return next(createError('ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER', 
      'Invalid X-User-ID format - must be a valid UUID', 400));
  }
  
  // Validate user role enum
  if (!Object.values(CONSTANTS.USER_ROLES).includes(userRole)) {
    return next(createError('ERR_INVALID_ENUM_VALUE', 
      `Invalid X-User-Role. Must be one of: ${Object.values(CONSTANTS.USER_ROLES).join(', ')}`, 400));
  }
  
  // Attach user context to request
  req.user = {
    id: userId,
    role: userRole
  };
  
  next();
}

module.exports = authMiddleware;