{"name": "university-course-registration-api", "version": "1.0.0", "description": "University Course Registration & Enrollment API implementation", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "keywords": ["university", "course", "registration", "enrollment", "api"], "author": "University API Team", "license": "ISC"}