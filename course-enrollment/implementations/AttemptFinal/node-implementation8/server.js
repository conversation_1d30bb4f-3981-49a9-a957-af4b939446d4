const express = require('express');
const cors = require('cors');
const { authMiddleware } = require('./middleware/auth');
const { responseEnvelope } = require('./utils/response');
const termRoutes = require('./routes/terms');
const courseRoutes = require('./routes/courses');
const enrollmentRoutes = require('./routes/enrollments');
const paymentRoutes = require('./routes/payments');

const app = express();
const PORT = process.env.PORT || 3000;

app.use(cors());
app.use(express.json());

app.use(authMiddleware);
app.use(responseEnvelope);

app.use('/terms', termRoutes);
app.use('/terms', courseRoutes);
app.use('/terms', enrollmentRoutes);
app.use('/terms', paymentRoutes);

app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error occurred');
});

app.use((req, res) => {
  res.status(404).sendError('ERR_ENDPOINT_NOT_FOUND', 'The requested endpoint was not found');
});

app.listen(PORT, () => {
  console.log(`University Course Registration API running on port ${PORT}`);
});

module.exports = app;