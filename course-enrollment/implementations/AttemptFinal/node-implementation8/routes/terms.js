const express = require('express');
const { storage } = require('../models/data');
const { TERM_STATES, COURSE_STATES, ENROLLMENT_STATES, USER_ROLES } = require('../utils/constants');
const { ValidationError, validateTermData, validateUUID, validateRequestBody, validateRevision } = require('../utils/validation');

const router = express.Router();

// POST /terms - Create a new academic term
router.post('/', (req, res) => {
  try {
    // Only Registrar can create terms
    if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.status(403).sendError(
        'ERR_UNAUTHORIZED_ROLE',
        'Only Registrar can create academic terms'
      );
    }

    validateRequestBody(req.body, ['name']);
    validateTermData(req.body);

    // Check term name uniqueness
    if (!storage.isTermNameUnique(req.body.name)) {
      return res.status(409).sendError(
        'ERR_TERM_NAME_NOT_UNIQUE',
        'A term with this name already exists'
      );
    }

    const term = storage.createTerm({
      name: req.body.name,
      created_by: req.user.id
    });

    res.sendSuccess(term, 201);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Term creation error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// GET /terms/:termId - Retrieve details of an academic term
router.get('/:termId', (req, res) => {
  try {
    const { termId } = req.params;
    validateUUID(termId, 'termId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    res.sendSuccess(term);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Term retrieval error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// PATCH /terms/:termId:open-registration - Open student registration
router.patch('/:termId:open-registration', (req, res) => {
  try {
    // Only Registrar can open registration
    if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.status(403).sendError(
        'ERR_UNAUTHORIZED_ROLE',
        'Only Registrar can open registration'
      );
    }

    const { termId } = req.params;
    validateUUID(termId, 'termId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    // Check current state
    if (term.state !== TERM_STATES.PLANNING) {
      return res.status(409).sendError(
        'ERR_TERM_NOT_ACTIVE',
        'Term must be in PLANNING state to open registration'
      );
    }

    // Handle revision if provided
    if (req.body.revision !== undefined) {
      validateRevision(req.body.revision, term.revision);
    }

    const updatedTerm = storage.updateTerm(termId, {
      state: TERM_STATES.ENROLLMENT_OPEN
    });

    res.sendSuccess(updatedTerm);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Open registration error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// PATCH /terms/:termId:close-registration - Close the enrollment period
router.patch('/:termId:close-registration', (req, res) => {
  try {
    // Only Registrar can close registration
    if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.status(403).sendError(
        'ERR_UNAUTHORIZED_ROLE',
        'Only Registrar can close registration'
      );
    }

    const { termId } = req.params;
    validateUUID(termId, 'termId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    // Check current state
    if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
      return res.status(409).sendError(
        'ERR_TERM_NOT_ACTIVE',
        'Term must be in ENROLLMENT_OPEN state to close registration'
      );
    }

    // Handle revision if provided
    if (req.body.revision !== undefined) {
      validateRevision(req.body.revision, term.revision);
    }

    // Update term state
    const updatedTerm = storage.updateTerm(termId, {
      state: TERM_STATES.ENROLLMENT_CLOSED
    });

    // Transition all OPEN courses to IN_PROGRESS
    const termCourses = storage.getCoursesByTerm(termId);
    termCourses.forEach(course => {
      if (course.state === COURSE_STATES.OPEN) {
        storage.updateCourse(course.id, {
          state: COURSE_STATES.IN_PROGRESS
        });
      }
    });

    res.sendSuccess(updatedTerm);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Close registration error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// PATCH /terms/:termId:conclude - Conclude the term
router.patch('/:termId:conclude', (req, res) => {
  try {
    // Only Registrar can conclude terms
    if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.status(403).sendError(
        'ERR_UNAUTHORIZED_ROLE',
        'Only Registrar can conclude terms'
      );
    }

    const { termId } = req.params;
    validateUUID(termId, 'termId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    // Check current state
    if (term.state !== TERM_STATES.ENROLLMENT_CLOSED) {
      return res.status(409).sendError(
        'ERR_TERM_NOT_ACTIVE',
        'Term must be in ENROLLMENT_CLOSED state to conclude'
      );
    }

    // Handle revision if provided
    if (req.body.revision !== undefined) {
      validateRevision(req.body.revision, term.revision);
    }

    // Update term state
    const updatedTerm = storage.updateTerm(termId, {
      state: TERM_STATES.CONCLUDED
    });

    // Transition all IN_PROGRESS courses to COMPLETED
    const termCourses = storage.getCoursesByTerm(termId);
    termCourses.forEach(course => {
      if (course.state === COURSE_STATES.IN_PROGRESS) {
        storage.updateCourse(course.id, {
          state: COURSE_STATES.COMPLETED
        });
      }
    });

    // Finalize all enrollments
    termCourses.forEach(course => {
      const enrollments = storage.getEnrollmentsByCourse(course.id);
      enrollments.forEach(enrollment => {
        if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
          storage.updateEnrollment(enrollment.id, {
            state: ENROLLMENT_STATES.COMPLETED
          });
        } else if (enrollment.state === ENROLLMENT_STATES.WAITLISTED) {
          storage.updateEnrollment(enrollment.id, {
            state: ENROLLMENT_STATES.DROPPED
          });
        }
      });
    });

    res.sendSuccess(updatedTerm);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Conclude term error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

module.exports = router;