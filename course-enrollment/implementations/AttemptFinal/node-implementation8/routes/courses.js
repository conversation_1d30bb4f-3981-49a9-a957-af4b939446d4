const express = require('express');
const { storage } = require('../models/data');
const { TERM_STATES, COURSE_STATES, ENROLLMENT_STATES, USER_ROLES } = require('../utils/constants');
const { ValidationError, validateCourseData, validateUUID, validateRequestBody } = require('../utils/validation');
const { 
  checkTermActive, 
  checkTermNotConcluded, 
  checkCourseState, 
  checkProfessorCourseLimit,
  checkInstructorPermission
} = require('../utils/business-rules');
const { filterCoursesForRole, filterCourseForRole, canAccessCourse } = require('../utils/role-filter');
const { sanitizePagination } = require('../utils/helpers');

const router = express.Router();

// POST /terms/:termId/courses - Create a new course
router.post('/:termId/courses', (req, res) => {
  try {
    const { termId } = req.params;
    validateUUID(termId, 'termId');

    // Only Professor and Registrar can create courses
    if (![USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR].includes(req.user.role)) {
      return res.status(403).sendError(
        'ERR_UNAUTHORIZED_ROLE',
        'Only Professor and Registrar can create courses'
      );
    }

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    checkTermNotConcluded(term);

    const allowedFields = ['code', 'title', 'description', 'credits', 'capacity', 'delivery_mode', 'location', 'online_link', 'professor_id'];
    validateRequestBody(req.body, allowedFields);
    validateCourseData(req.body);

    // Handle professor assignment
    let professorId;
    if (req.user.role === USER_ROLES.PROFESSOR) {
      // Professor creating course for themselves
      if (req.body.professor_id && req.body.professor_id !== req.user.id) {
        return res.status(400).sendError(
          'ERR_FIELD_CONFLICT',
          'Professors can only create courses for themselves'
        );
      }
      professorId = req.user.id;
    } else {
      // Registrar creating course - must specify professor
      if (!req.body.professor_id) {
        return res.status(400).sendError(
          'ERR_MISSING_REQUIRED_FIELD',
          'professor_id is required when Registrar creates a course'
        );
      }
      // Note: In a real system, we'd validate that professor_id exists and has PROFESSOR role
      professorId = req.body.professor_id;
    }

    // Check course code uniqueness in term
    if (!storage.isCourseCodeUniqueInTerm(termId, req.body.code)) {
      return res.status(409).sendError(
        'ERR_COURSE_CODE_NOT_UNIQUE',
        'Course code already exists in this term'
      );
    }

    // Check professor course limit
    checkProfessorCourseLimit(professorId, termId);

    const course = storage.createCourse(termId, {
      ...req.body,
      professor_id: professorId
    });

    res.sendSuccess(course, 201);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Course creation error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// GET /terms/:termId/courses - List courses in the term
router.get('/:termId/courses', (req, res) => {
  try {
    const { termId } = req.params;
    validateUUID(termId, 'termId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    const { limit, offset } = sanitizePagination(req.query.limit, req.query.offset);
    let courses = storage.getCoursesByTerm(termId);

    // Apply query filters for Registrar
    if (req.user.role === USER_ROLES.REGISTRAR) {
      if (req.query.state) {
        courses = courses.filter(course => course.state === req.query.state);
      }
      if (req.query.professor_id) {
        courses = courses.filter(course => course.professor_id === req.query.professor_id);
      }
    }

    // Apply role-based filtering
    const filteredCourses = filterCoursesForRole(courses, req.user.role, req.user.id);

    // Sort by creation date
    filteredCourses.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Apply pagination
    const total = filteredCourses.length;
    const paginatedCourses = filteredCourses.slice(offset, offset + limit);

    // Add pagination meta
    const response = {
      courses: paginatedCourses,
      meta: {
        total,
        limit,
        offset,
        has_more: offset + limit < total
      }
    };

    res.sendSuccess(response);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Course listing error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// GET /terms/:termId/courses/:courseId - Get specific course details
router.get('/:termId/courses/:courseId', (req, res) => {
  try {
    const { termId, courseId } = req.params;
    validateUUID(termId, 'termId');
    validateUUID(courseId, 'courseId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).sendError(
        'ERR_COURSE_NOT_FOUND',
        'Course not found'
      );
    }

    // Check if user can access this course
    if (!canAccessCourse(course, req.user.role, req.user.id)) {
      return res.status(404).sendError(
        'ERR_COURSE_NOT_FOUND',
        'Course not found'
      );
    }

    const filteredCourse = filterCourseForRole(course, req.user.role, req.user.id);
    res.sendSuccess(filteredCourse);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Course retrieval error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// PATCH /terms/:termId/courses/:courseId:publish - Publish a draft course
router.patch('/:termId/courses/:courseId:publish', (req, res) => {
  try {
    const { termId, courseId } = req.params;
    validateUUID(termId, 'termId');
    validateUUID(courseId, 'courseId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).sendError(
        'ERR_COURSE_NOT_FOUND',
        'Course not found'
      );
    }

    // Check permissions
    if (req.user.role === USER_ROLES.PROFESSOR) {
      checkInstructorPermission(req.user.id, course, 'publish this course');
    } else if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.status(403).sendError(
        'ERR_UNAUTHORIZED_ROLE',
        'Only the course instructor or Registrar can publish courses'
      );
    }

    // Check term state
    checkTermActive(term);

    // Check course state
    checkCourseState(course, [COURSE_STATES.DRAFT], 'publish');

    // Update course and initialize seat ledger
    const updatedCourse = storage.updateCourse(courseId, {
      state: COURSE_STATES.OPEN
    });

    storage.initializeCourseSeatLedger(courseId, course.capacity);

    const filteredCourse = filterCourseForRole(updatedCourse, req.user.role, req.user.id);
    res.sendSuccess(filteredCourse);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Course publish error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// PATCH /terms/:termId/courses/:courseId:cancel - Cancel a course
router.patch('/:termId/courses/:courseId:cancel', (req, res) => {
  try {
    const { termId, courseId } = req.params;
    validateUUID(termId, 'termId');
    validateUUID(courseId, 'courseId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).sendError(
        'ERR_COURSE_NOT_FOUND',
        'Course not found'
      );
    }

    // Check permissions
    if (req.user.role === USER_ROLES.PROFESSOR) {
      checkInstructorPermission(req.user.id, course, 'cancel this course');
    } else if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.status(403).sendError(
        'ERR_UNAUTHORIZED_ROLE',
        'Only the course instructor or Registrar can cancel courses'
      );
    }

    // Check course state
    checkCourseState(course, [COURSE_STATES.DRAFT, COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS], 'cancel');

    // Cancel the course
    const updatedCourse = storage.updateCourse(courseId, {
      state: COURSE_STATES.CANCELLED
    });

    // Drop all enrollments and refund tuition
    const enrollments = storage.getEnrollmentsByCourse(courseId);
    enrollments.forEach(enrollment => {
      if (enrollment.state === ENROLLMENT_STATES.ENROLLED || enrollment.state === ENROLLMENT_STATES.WAITLISTED) {
        storage.updateEnrollment(enrollment.id, {
          state: ENROLLMENT_STATES.DROPPED
        });

        // Refund tuition for enrolled students
        if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
          const tuitionCost = course.credits * require('../utils/constants').COST_PER_CREDIT;
          storage.updateStudentTuitionLedger(enrollment.student_id, termId, -tuitionCost);
        }
      }
    });

    const filteredCourse = filterCourseForRole(updatedCourse, req.user.role, req.user.id);
    res.sendSuccess(filteredCourse);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Course cancel error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

module.exports = router;