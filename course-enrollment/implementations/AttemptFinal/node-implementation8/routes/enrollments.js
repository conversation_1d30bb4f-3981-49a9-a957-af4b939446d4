const express = require('express');
const { storage } = require('../models/data');
const { TERM_STATES, COURSE_STATES, ENROLLMENT_STATES, USER_ROLES, DROP_PENALTY_FEE } = require('../utils/constants');
const { ValidationError, validateUUID, validateRequestBody } = require('../utils/validation');
const { 
  checkTermActive, 
  checkTermNotConcluded, 
  checkCourseState,
  checkEnrollmentState,
  checkCreditLimit,
  checkDropLimit,
  checkDuplicateEnrollment,
  checkEnrollmentOwnership,
  calculateTuitionCost,
  shouldApplyDropPenalty
} = require('../utils/business-rules');
const { filterEnrollmentsForRole, filterEnrollmentForRole, canAccessEnrollment } = require('../utils/role-filter');
const { sanitizePagination } = require('../utils/helpers');
const { promoteFromWaitlist } = require('../utils/waitlist');

const router = express.Router();

// POST /terms/:termId/courses/:courseId/enrollments - Enroll in a course
router.post('/:termId/courses/:courseId/enrollments', (req, res) => {
  try {
    const { termId, courseId } = req.params;
    validateUUID(termId, 'termId');
    validateUUID(courseId, 'courseId');

    // Only Student and Registrar can create enrollments
    if (![USER_ROLES.STUDENT, USER_ROLES.REGISTRAR].includes(req.user.role)) {
      return res.status(403).sendError(
        'ERR_UNAUTHORIZED_ROLE',
        'Only Student and Registrar can create enrollments'
      );
    }

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    checkTermActive(term);

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).sendError(
        'ERR_COURSE_NOT_FOUND',
        'Course not found'
      );
    }

    checkCourseState(course, [COURSE_STATES.OPEN], 'enroll in');

    // Determine student ID
    let studentId;
    if (req.user.role === USER_ROLES.STUDENT) {
      studentId = req.user.id;
      // Students cannot specify different student_id
      if (req.body.student_id && req.body.student_id !== req.user.id) {
        return res.status(403).sendError(
          'ERR_FORBIDDEN',
          'Students can only enroll themselves'
        );
      }
    } else {
      // Registrar must specify student_id
      validateRequestBody(req.body, ['student_id']);
      if (!req.body.student_id) {
        return res.status(400).sendError(
          'ERR_MISSING_REQUIRED_FIELD',
          'student_id is required for Registrar enrollments'
        );
      }
      validateUUID(req.body.student_id, 'student_id');
      studentId = req.body.student_id;
    }

    // Check for duplicate enrollment
    checkDuplicateEnrollment(studentId, courseId);

    // Check credit limit (skip for Registrar override)
    const isRegistrarOverride = req.user.role === USER_ROLES.REGISTRAR;
    checkCreditLimit(studentId, termId, course.credits, isRegistrarOverride);

    // Create enrollment
    const enrollment = storage.createEnrollment(courseId, studentId);

    // Determine if student gets a seat or goes on waitlist
    const seatLedger = storage.getCourseSeatLedger(courseId);
    if (!seatLedger) {
      return res.status(409).sendError(
        'ERR_COURSE_WRONG_STATE',
        'Course is not open for enrollment'
      );
    }

    if (seatLedger.seats_available > 0) {
      // Student gets enrolled
      storage.updateEnrollment(enrollment.id, {
        state: ENROLLMENT_STATES.ENROLLED
      });

      // Update ledgers
      storage.updateCourseSeatLedger(courseId, -1);
      const tuitionCost = calculateTuitionCost(course.credits);
      storage.updateStudentTuitionLedger(studentId, termId, tuitionCost);
    } else {
      // Student goes on waitlist
      storage.updateEnrollment(enrollment.id, {
        state: ENROLLMENT_STATES.WAITLISTED
      });
    }

    const updatedEnrollment = storage.getEnrollment(enrollment.id);
    const filteredEnrollment = filterEnrollmentForRole(updatedEnrollment, req.user.role, req.user.id);
    res.sendSuccess(filteredEnrollment, 201);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Enrollment creation error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// GET /terms/:termId/courses/:courseId/enrollments - List enrollments for a course
router.get('/:termId/courses/:courseId/enrollments', (req, res) => {
  try {
    const { termId, courseId } = req.params;
    validateUUID(termId, 'termId');
    validateUUID(courseId, 'courseId');

    // Students cannot list enrollments (roster)
    if (req.user.role === USER_ROLES.STUDENT) {
      return res.status(403).sendError(
        'ERR_UNAUTHORIZED_ROLE',
        'Students cannot view course rosters'
      );
    }

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).sendError(
        'ERR_COURSE_NOT_FOUND',
        'Course not found'
      );
    }

    // Check if professor can access this course's roster
    if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.status(403).sendError(
        'ERR_NOT_INSTRUCTOR',
        'Professors can only view rosters for their own courses'
      );
    }

    const { limit, offset } = sanitizePagination(req.query.limit, req.query.offset);
    let enrollments = storage.getEnrollmentsByCourse(courseId);

    // Sort by creation date (waitlist order)
    enrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Apply pagination
    const total = enrollments.length;
    const paginatedEnrollments = enrollments.slice(offset, offset + limit);

    const filteredEnrollments = filterEnrollmentsForRole(paginatedEnrollments, req.user.role, req.user.id);

    const response = {
      enrollments: filteredEnrollments,
      meta: {
        total,
        limit,
        offset,
        has_more: offset + limit < total
      }
    };

    res.sendSuccess(response);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Enrollment listing error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// GET /terms/:termId/courses/:courseId/enrollments/:enrollmentId - Get specific enrollment
router.get('/:termId/courses/:courseId/enrollments/:enrollmentId', (req, res) => {
  try {
    const { termId, courseId, enrollmentId } = req.params;
    validateUUID(termId, 'termId');
    validateUUID(courseId, 'courseId');
    validateUUID(enrollmentId, 'enrollmentId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).sendError(
        'ERR_COURSE_NOT_FOUND',
        'Course not found'
      );
    }

    const enrollment = storage.getEnrollment(enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.status(404).sendError(
        'ERR_ENROLLMENT_NOT_FOUND',
        'Enrollment not found'
      );
    }

    // Check access permissions
    if (!canAccessEnrollment(enrollment, req.user.role, req.user.id)) {
      return res.status(404).sendError(
        'ERR_ENROLLMENT_NOT_FOUND',
        'Enrollment not found'
      );
    }

    const filteredEnrollment = filterEnrollmentForRole(enrollment, req.user.role, req.user.id);
    res.sendSuccess(filteredEnrollment);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Enrollment retrieval error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

// PATCH /terms/:termId/courses/:courseId/enrollments/:enrollmentId:drop - Drop an enrollment
router.patch('/:termId/courses/:courseId/enrollments/:enrollmentId:drop', (req, res) => {
  try {
    const { termId, courseId, enrollmentId } = req.params;
    validateUUID(termId, 'termId');
    validateUUID(courseId, 'courseId');
    validateUUID(enrollmentId, 'enrollmentId');

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).sendError(
        'ERR_TERM_NOT_FOUND',
        'Academic term not found'
      );
    }

    checkTermNotConcluded(term);

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).sendError(
        'ERR_COURSE_NOT_FOUND',
        'Course not found'
      );
    }

    const enrollment = storage.getEnrollment(enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.status(404).sendError(
        'ERR_ENROLLMENT_NOT_FOUND',
        'Enrollment not found'
      );
    }

    // Check permissions
    checkEnrollmentOwnership(req.user.id, enrollment, req.user.role);

    // Check enrollment state
    checkEnrollmentState(enrollment, [ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED], 'drop');

    // Check drop limit for students
    if (req.user.role === USER_ROLES.STUDENT) {
      checkDropLimit(enrollment.student_id, termId);
    }

    const wasEnrolled = enrollment.state === ENROLLMENT_STATES.ENROLLED;

    // Drop the enrollment
    const updatedEnrollment = storage.updateEnrollment(enrollmentId, {
      state: ENROLLMENT_STATES.DROPPED
    });

    // Handle ledger updates for enrolled students
    if (wasEnrolled) {
      // Free up the seat
      storage.updateCourseSeatLedger(courseId, 1);

      // Refund tuition
      const tuitionCost = calculateTuitionCost(course.credits);
      storage.updateStudentTuitionLedger(enrollment.student_id, termId, -tuitionCost);

      // Trigger waitlist promotion asynchronously
      setTimeout(() => promoteFromWaitlist(courseId), 0);
    }

    // Apply drop penalty if this is student's 3rd drop
    if (req.user.role === USER_ROLES.STUDENT && shouldApplyDropPenalty(enrollment.student_id, termId)) {
      storage.updateStudentTuitionLedger(enrollment.student_id, termId, DROP_PENALTY_FEE);
    }

    // Increment drop count for students
    if (req.user.role === USER_ROLES.STUDENT) {
      storage.incrementStudentDropCount(enrollment.student_id, termId);
    }

    const filteredEnrollment = filterEnrollmentForRole(updatedEnrollment, req.user.role, req.user.id);
    res.sendSuccess(filteredEnrollment);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(error.statusCode).sendError(error.errorId, error.message);
    }
    console.error('Enrollment drop error:', error);
    res.status(500).sendError('ERR_INTERNAL_SERVER_ERROR', 'Internal server error');
  }
});

module.exports = router;