const { storage } = require('../models/data');
const { COURSE_STATES, ENROLLMENT_STATES, USER_ROLES } = require('./constants');

function filterCourseForRole(course, userRole, userId) {
  if (!course) return null;

  // Base course data that all roles can see (when allowed to see the course)
  const baseCourse = {
    id: course.id,
    code: course.code,
    title: course.title,
    description: course.description,
    credits: course.credits,
    capacity: course.capacity,
    professor_id: course.professor_id,
    delivery_mode: course.delivery_mode,
    location: course.location,
    online_link: course.online_link,
    state: course.state,
    created_at: course.created_at
  };

  // Get enrollment counts
  const enrollments = storage.getEnrollmentsByCourse(course.id);
  const enrolledCount = enrollments.filter(e => e.state === ENROLLMENT_STATES.ENROLLED).length;
  const waitlistCount = enrollments.filter(e => e.state === ENROLLMENT_STATES.WAITLISTED).length;
  const availableSeats = course.capacity - enrolledCount;

  if (userRole === USER_ROLES.STUDENT) {
    // Students see basic info for published courses
    if (![COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)) {
      // Check if student is enrolled/waitlisted to show cancelled courses
      const studentEnrollment = storage.getStudentEnrollmentInCourse(userId, course.id);
      if (!studentEnrollment) {
        return null; // Hide draft/cancelled courses unless enrolled
      }
    }

    const studentCourse = {
      ...baseCourse,
      available_seats: availableSeats
    };

    // Add student's enrollment status if they're enrolled/waitlisted
    const studentEnrollment = storage.getStudentEnrollmentInCourse(userId, course.id);
    if (studentEnrollment) {
      studentCourse.is_enrolled = studentEnrollment.state === ENROLLMENT_STATES.ENROLLED;
      studentCourse.is_waitlisted = studentEnrollment.state === ENROLLMENT_STATES.WAITLISTED;
    }

    return studentCourse;
  } else if (userRole === USER_ROLES.PROFESSOR) {
    // Professors see their own courses fully, others limited
    if (course.professor_id === userId) {
      // Full details for their own course
      return {
        ...baseCourse,
        enrolled_count: enrolledCount,
        waitlist_count: waitlistCount,
        available_seats: availableSeats,
        enrollments: enrollments.map(e => filterEnrollmentForRole(e, userRole, userId))
      };
    } else {
      // Limited view of other professors' courses
      if (![COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)) {
        return null; // Hide draft/cancelled courses not owned
      }
      return {
        ...baseCourse,
        available_seats: availableSeats
      };
    }
  } else if (userRole === USER_ROLES.REGISTRAR) {
    // Registrar sees everything
    return {
      ...baseCourse,
      enrolled_count: enrolledCount,
      waitlist_count: waitlistCount,
      available_seats: availableSeats,
      enrollments: enrollments.map(e => filterEnrollmentForRole(e, userRole, userId))
    };
  }

  return null;
}

function filterEnrollmentForRole(enrollment, userRole, userId) {
  if (!enrollment) return null;

  const baseEnrollment = {
    id: enrollment.id,
    course_id: enrollment.course_id,
    student_id: enrollment.student_id,
    state: enrollment.state,
    created_at: enrollment.created_at
  };

  if (userRole === USER_ROLES.STUDENT) {
    // Students can only see their own enrollments
    if (enrollment.student_id !== userId) {
      return null;
    }
    return baseEnrollment;
  } else if (userRole === USER_ROLES.PROFESSOR) {
    // Professors can see enrollments for their courses
    const course = storage.getCourse(enrollment.course_id);
    if (!course || course.professor_id !== userId) {
      return null;
    }
    return baseEnrollment;
  } else if (userRole === USER_ROLES.REGISTRAR) {
    // Registrar sees all enrollments
    return baseEnrollment;
  }

  return null;
}

function filterCoursesForRole(courses, userRole, userId) {
  return courses
    .map(course => filterCourseForRole(course, userRole, userId))
    .filter(course => course !== null);
}

function filterEnrollmentsForRole(enrollments, userRole, userId) {
  return enrollments
    .map(enrollment => filterEnrollmentForRole(enrollment, userRole, userId))
    .filter(enrollment => enrollment !== null);
}

function canAccessCourse(course, userRole, userId) {
  if (userRole === USER_ROLES.REGISTRAR) {
    return true; // Registrar can access all courses
  }

  if (userRole === USER_ROLES.PROFESSOR && course.professor_id === userId) {
    return true; // Professor can access their own courses
  }

  if (userRole === USER_ROLES.STUDENT) {
    // Students can access published courses or courses they're enrolled in
    if ([COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)) {
      return true;
    }
    
    // Check if student is enrolled/waitlisted
    const enrollment = storage.getStudentEnrollmentInCourse(userId, course.id);
    return !!enrollment;
  }

  return false;
}

function canAccessEnrollment(enrollment, userRole, userId) {
  if (userRole === USER_ROLES.REGISTRAR) {
    return true; // Registrar can access all enrollments
  }

  if (userRole === USER_ROLES.STUDENT) {
    return enrollment.student_id === userId; // Students can only access their own
  }

  if (userRole === USER_ROLES.PROFESSOR) {
    const course = storage.getCourse(enrollment.course_id);
    return course && course.professor_id === userId; // Professors can access enrollments for their courses
  }

  return false;
}

module.exports = {
  filterCourseForRole,
  filterEnrollmentForRole,
  filterCoursesForRole,
  filterEnrollmentsForRole,
  canAccessCourse,
  canAccessEnrollment
};