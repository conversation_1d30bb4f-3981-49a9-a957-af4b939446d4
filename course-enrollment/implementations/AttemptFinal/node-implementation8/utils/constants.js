const MAX_CREDITS_PER_TERM = 18;
const MAX_COURSES_PER_PROF = 5;
const MAX_DROP_COUNT_PER_TERM = 3;
const COST_PER_CREDIT = 10000; // $100.00 in cents
const DROP_PENALTY_FEE = 5000; // $50.00 in cents

const TERM_STATES = {
  PLANNING: 'PLANNING',
  ENROLLMENT_OPEN: 'ENROLLMENT_OPEN',
  ENROLLMENT_CLOSED: 'ENROLLMENT_CLOSED',
  CONCLUDED: 'CONCLUDED'
};

const COURSE_STATES = {
  DRAFT: 'DRAFT',
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

const ENROLLMENT_STATES = {
  ENROLLED: 'ENROLLED',
  WAITLISTED: 'WAITLISTED',
  DROPPED: 'DROPPED',
  COMPLETED: 'COMPLETED'
};

const DELIVERY_MODES = {
  IN_PERSON: 'IN_PERSON',
  ONLINE: 'ONLINE',
  HYBRID: 'HYBRID'
};

const USER_ROLES = {
  STUDENT: 'STUDENT',
  PROFESSOR: 'PROFESSOR',
  REGISTRAR: 'REGISTRAR'
};

module.exports = {
  MAX_CREDITS_PER_TERM,
  MAX_COURSES_PER_PROF,
  MAX_DROP_COUNT_PER_TERM,
  COST_PER_CREDIT,
  DROP_PENALTY_FEE,
  TERM_STATES,
  COURSE_STATES,
  ENROLLMENT_STATES,
  DELIVERY_MODES,
  USER_ROLES
};