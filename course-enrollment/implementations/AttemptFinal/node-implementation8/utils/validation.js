const { isValidUUID, isValidCourseCode, isValidURL } = require('./helpers');
const { TERM_STATES, COURSE_STATES, ENROLLMENT_STATES, DELIVERY_MODES, USER_ROLES } = require('./constants');

class ValidationError extends Error {
  constructor(errorId, message, statusCode = 400) {
    super(message);
    this.errorId = errorId;
    this.statusCode = statusCode;
  }
}

function validateRequired(value, fieldName) {
  if (value === undefined || value === null || value === '') {
    throw new ValidationError(
      'ERR_MISSING_REQUIRED_FIELD',
      `${fieldName} is required`
    );
  }
}

function validateUUID(uuid, fieldName = 'ID') {
  if (!isValidUUID(uuid)) {
    throw new ValidationError(
      'ERR_INVALID_ID_FORMAT',
      `${fieldName} must be a valid UUID format`
    );
  }
}

function validateEnum(value, validValues, fieldName) {
  if (!validValues.includes(value)) {
    throw new ValidationError(
      'ERR_INVALID_ENUM_VALUE',
      `${fieldName} must be one of: ${validValues.join(', ')}`
    );
  }
}

function validateStringLength(value, minLength, maxLength, fieldName) {
  if (typeof value !== 'string') {
    throw new ValidationError(
      'ERR_INVALID_FIELD_LENGTH',
      `${fieldName} must be a string`
    );
  }
  
  if (value.length < minLength || value.length > maxLength) {
    throw new ValidationError(
      'ERR_INVALID_FIELD_LENGTH',
      `${fieldName} must be between ${minLength} and ${maxLength} characters`
    );
  }
}

function validateCourseCode(code) {
  if (!isValidCourseCode(code)) {
    throw new ValidationError(
      'ERR_INVALID_COURSE_CODE',
      'Course code must be 2-4 uppercase letters followed by 3 digits (e.g., CS101)'
    );
  }
}

function validateCredits(credits) {
  if (!Number.isInteger(credits) || credits < 1 || credits > 5) {
    throw new ValidationError(
      'ERR_INVALID_CREDITS',
      'Credits must be an integer between 1 and 5'
    );
  }
}

function validateCapacity(capacity) {
  if (!Number.isInteger(capacity) || capacity < 1 || capacity > 500) {
    throw new ValidationError(
      'ERR_INVALID_CAPACITY',
      'Capacity must be an integer between 1 and 500'
    );
  }
}

function validatePaymentAmount(amount) {
  if (!Number.isInteger(amount) || amount <= 0) {
    throw new ValidationError(
      'ERR_INVALID_PAYMENT_AMOUNT',
      'Payment amount must be a positive integer (cents)',
      422
    );
  }
}

function validateTermData(data) {
  validateRequired(data.name, 'name');
  validateStringLength(data.name, 1, 100, 'name');
}

function validateCourseData(data) {
  validateRequired(data.code, 'code');
  validateCourseCode(data.code);
  
  validateRequired(data.title, 'title');
  validateStringLength(data.title, 1, 100, 'title');
  
  if (data.description !== undefined) {
    validateStringLength(data.description, 0, 1000, 'description');
  }
  
  validateRequired(data.credits, 'credits');
  validateCredits(data.credits);
  
  validateRequired(data.capacity, 'capacity');
  validateCapacity(data.capacity);
  
  validateRequired(data.delivery_mode, 'delivery_mode');
  validateEnum(data.delivery_mode, Object.values(DELIVERY_MODES), 'delivery_mode');
  
  // Validate delivery mode requirements
  if (data.delivery_mode === DELIVERY_MODES.IN_PERSON) {
    validateRequired(data.location, 'location');
    validateStringLength(data.location, 1, 200, 'location');
    
    if (data.online_link) {
      throw new ValidationError(
        'ERR_FIELD_CONFLICT',
        'online_link cannot be provided for IN_PERSON courses'
      );
    }
  } else if (data.delivery_mode === DELIVERY_MODES.ONLINE) {
    validateRequired(data.online_link, 'online_link');
    if (!isValidURL(data.online_link)) {
      throw new ValidationError(
        'ERR_INVALID_FIELD_LENGTH',
        'online_link must be a valid URL'
      );
    }
    
    if (data.location) {
      throw new ValidationError(
        'ERR_FIELD_CONFLICT',
        'location cannot be provided for ONLINE courses'
      );
    }
  } else if (data.delivery_mode === DELIVERY_MODES.HYBRID) {
    if (!data.location && !data.online_link) {
      throw new ValidationError(
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        'HYBRID courses require at least one of location or online_link'
      );
    }
    
    if (data.location) {
      validateStringLength(data.location, 1, 200, 'location');
    }
    
    if (data.online_link && !isValidURL(data.online_link)) {
      throw new ValidationError(
        'ERR_INVALID_FIELD_LENGTH',
        'online_link must be a valid URL'
      );
    }
  }
  
  if (data.professor_id) {
    validateUUID(data.professor_id, 'professor_id');
  }
}

function validateRequestBody(body, allowedFields) {
  const extraFields = Object.keys(body).filter(key => !allowedFields.includes(key));
  if (extraFields.length > 0) {
    throw new ValidationError(
      'ERR_UNKNOWN_FIELD',
      `Unknown fields: ${extraFields.join(', ')}`
    );
  }
}

function validateRevision(providedRevision, currentRevision) {
  if (providedRevision !== currentRevision) {
    throw new ValidationError(
      'ERR_REV_CONFLICT',
      'Resource has been modified by another user',
      409
    );
  }
}

module.exports = {
  ValidationError,
  validateRequired,
  validateUUID,
  validateEnum,
  validateStringLength,
  validateCourseCode,
  validateCredits,
  validateCapacity,
  validatePaymentAmount,
  validateTermData,
  validateCourseData,
  validateRequestBody,
  validateRevision
};