const { storage } = require('../models/data');
const { ENROLLMENT_STATES } = require('./constants');
const { calculateTuitionCost } = require('./business-rules');

function promoteFromWaitlist(courseId) {
  try {
    // Get course to determine term
    const course = storage.getCourse(courseId);
    if (!course) {
      console.error(`Course ${courseId} not found for waitlist promotion`);
      return;
    }

    // Check if there are available seats
    const seatLedger = storage.getCourseSeatLedger(courseId);
    if (!seatLedger || seatLedger.seats_available <= 0) {
      return; // No seats available
    }

    // Find the earliest waitlisted student
    const waitlistedEnrollments = storage.getEnrollmentsByCourse(courseId)
      .filter(enrollment => enrollment.state === ENROLLMENT_STATES.WAITLISTED)
      .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    if (waitlistedEnrollments.length === 0) {
      return; // No one on waitlist
    }

    const enrollmentToPromote = waitlistedEnrollments[0];
    
    // Promote the student
    storage.updateEnrollment(enrollmentToPromote.id, {
      state: ENROLLMENT_STATES.ENROLLED
    });

    // Update seat ledger (consume the available seat)
    storage.updateCourseSeatLedger(courseId, -1);

    // Charge tuition
    const tuitionCost = calculateTuitionCost(course.credits);
    storage.updateStudentTuitionLedger(
      enrollmentToPromote.student_id, 
      course.term_id, 
      tuitionCost
    );

    console.log(`Promoted student ${enrollmentToPromote.student_id} from waitlist for course ${courseId}`);

    // Check if more promotions are possible (recursive promotion)
    setTimeout(() => promoteFromWaitlist(courseId), 0);
    
  } catch (error) {
    console.error('Waitlist promotion error:', error);
  }
}

module.exports = {
  promoteFromWaitlist
};