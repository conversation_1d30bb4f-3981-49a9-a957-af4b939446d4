const crypto = require('crypto');

function generateId(prefix = '') {
  const uuid = crypto.randomUUID();
  return prefix ? `${prefix}_${uuid}` : uuid;
}

function generateRequestId() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = 'req_';
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

const UUID_PATTERN = /^[0-9a-fA-F-]{36}$/;

function isValidUUID(uuid) {
  return UUID_PATTERN.test(uuid);
}

function isValidCourseCode(code) {
  const pattern = /^[A-Z]{2,4}\d{3}$/;
  return pattern.test(code);
}

function isValidURL(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function sanitizePagination(limit, offset) {
  const sanitizedLimit = Math.max(1, Math.min(parseInt(limit) || 50, 500));
  const sanitizedOffset = Math.max(0, parseInt(offset) || 0);
  return { limit: sanitizedLimit, offset: sanitizedOffset };
}

module.exports = {
  generateId,
  generateRequestId,
  isValidUUID,
  isValidCourseCode,
  isValidURL,
  sanitizePagination
};