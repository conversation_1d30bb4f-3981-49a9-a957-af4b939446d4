const { generateRequestId } = require('./helpers');

function responseEnvelope(req, res, next) {
  const requestId = generateRequestId();
  const timestamp = new Date().toISOString();

  res.sendSuccess = function(data, statusCode = 200) {
    const responseType = Array.isArray(data) ? 'array' : 'object';
    
    this.status(statusCode).json({
      meta: {
        api_request_id: requestId,
        api_request_timestamp: timestamp
      },
      response_type: responseType,
      data: data || {}
    });
  };

  res.sendError = function(errorId, message, statusCode = 400) {
    this.status(statusCode).json({
      meta: {
        api_request_id: requestId,
        api_request_timestamp: timestamp
      },
      response_type: 'error',
      data: {
        error_id: errorId,
        message: message
      }
    });
  };

  next();
}

module.exports = { responseEnvelope };