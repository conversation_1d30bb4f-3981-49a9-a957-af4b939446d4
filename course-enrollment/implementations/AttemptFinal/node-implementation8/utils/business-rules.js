const { storage } = require('../models/data');
const { 
  MAX_CREDITS_PER_TERM, 
  MAX_COURSES_PER_PROF, 
  MAX_DROP_COUNT_PER_TERM,
  COST_PER_CREDIT,
  DROP_PENALTY_FEE,
  TERM_STATES,
  COURSE_STATES,
  ENROLLMENT_STATES,
  USER_ROLES
} = require('./constants');
const { ValidationError } = require('./validation');

function checkTermActive(term) {
  if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
    throw new ValidationError(
      'ERR_TERM_NOT_ACTIVE',
      'Term is not open for enrollment',
      409
    );
  }
}

function checkTermNotConcluded(term) {
  if (term.state === TERM_STATES.CONCLUDED) {
    throw new ValidationError(
      'ERR_TERM_CLOSED',
      'Term has been concluded and is read-only',
      404
    );
  }
}

function checkCourseState(course, allowedStates, operation) {
  if (!allowedStates.includes(course.state)) {
    throw new ValidationError(
      'ERR_COURSE_WRONG_STATE',
      `Cannot ${operation} course in ${course.state} state`,
      409
    );
  }
}

function checkEnrollmentState(enrollment, allowedStates, operation) {
  if (!allowedStates.includes(enrollment.state)) {
    throw new ValidationError(
      'ERR_ENROLLMENT_WRONG_STATE',
      `Cannot ${operation} enrollment in ${enrollment.state} state`,
      409
    );
  }
}

function checkProfessorCourseLimit(professorId, termId, excludeCourseId = null) {
  const professorCourses = storage.getCoursesByProfessor(termId, professorId)
    .filter(course => course.id !== excludeCourseId && course.state !== COURSE_STATES.CANCELLED);
  
  if (professorCourses.length >= MAX_COURSES_PER_PROF) {
    throw new ValidationError(
      'ERR_MAX_COURSES_REACHED',
      `Professor cannot teach more than ${MAX_COURSES_PER_PROF} courses per term`,
      409
    );
  }
}

function checkCreditLimit(studentId, termId, additionalCredits, isRegistrarOverride = false) {
  if (isRegistrarOverride) {
    return; // Registrar can override credit limits
  }

  const enrollments = storage.getEnrollmentsByStudent(studentId, termId);
  const currentCredits = enrollments
    .filter(enrollment => enrollment.state === ENROLLMENT_STATES.ENROLLED)
    .reduce((total, enrollment) => {
      const course = storage.getCourse(enrollment.course_id);
      return total + (course ? course.credits : 0);
    }, 0);

  if (currentCredits + additionalCredits > MAX_CREDITS_PER_TERM) {
    throw new ValidationError(
      'ERR_CREDIT_LIMIT_EXCEEDED',
      `Student would exceed maximum credit limit of ${MAX_CREDITS_PER_TERM}`,
      409
    );
  }
}

function checkDropLimit(studentId, termId) {
  const dropCount = storage.getStudentDropCount(studentId, termId);
  if (dropCount >= MAX_DROP_COUNT_PER_TERM) {
    throw new ValidationError(
      'ERR_TOO_MANY_DROPS',
      `Student has reached maximum drop limit of ${MAX_DROP_COUNT_PER_TERM} for this term`,
      409
    );
  }
}

function checkDuplicateEnrollment(studentId, courseId) {
  const existingEnrollment = storage.getStudentEnrollmentInCourse(studentId, courseId);
  if (existingEnrollment) {
    throw new ValidationError(
      'ERR_ALREADY_ENROLLED',
      'Student is already enrolled or waitlisted in this course',
      409
    );
  }
}

function checkInstructorPermission(userId, course, operation) {
  if (course.professor_id !== userId) {
    throw new ValidationError(
      'ERR_NOT_INSTRUCTOR',
      `Only the course instructor can ${operation}`,
      403
    );
  }
}

function checkEnrollmentOwnership(userId, enrollment, userRole) {
  if (userRole === USER_ROLES.STUDENT && enrollment.student_id !== userId) {
    throw new ValidationError(
      'ERR_FORBIDDEN',
      'Students can only access their own enrollments',
      403
    );
  } else if (userRole === USER_ROLES.PROFESSOR) {
    const course = storage.getCourse(enrollment.course_id);
    if (!course || course.professor_id !== userId) {
      throw new ValidationError(
        'ERR_NOT_INSTRUCTOR',
        'Professors can only access enrollments for their courses',
        403
      );
    }
  }
}

function checkPaymentPermission(userId, studentId, userRole) {
  if (userRole === USER_ROLES.STUDENT && studentId !== userId) {
    throw new ValidationError(
      'ERR_FORBIDDEN',
      'Students can only make payments for their own account',
      403
    );
  }
}

function checkOverpayment(amount, currentBalance) {
  if (amount > currentBalance) {
    throw new ValidationError(
      'ERR_OVERPAY_NOT_ALLOWED',
      'Payment amount exceeds outstanding balance',
      422
    );
  }
}

function calculateTuitionCost(credits) {
  return credits * COST_PER_CREDIT;
}

function shouldApplyDropPenalty(studentId, termId) {
  const dropCount = storage.getStudentDropCount(studentId, termId);
  return dropCount === 2; // Apply penalty on 3rd drop (current count is 2, will become 3)
}

module.exports = {
  checkTermActive,
  checkTermNotConcluded,
  checkCourseState,
  checkEnrollmentState,
  checkProfessorCourseLimit,
  checkCreditLimit,
  checkDropLimit,
  checkDuplicateEnrollment,
  checkInstructorPermission,
  checkEnrollmentOwnership,
  checkPaymentPermission,
  checkOverpayment,
  calculateTuitionCost,
  shouldApplyDropPenalty
};