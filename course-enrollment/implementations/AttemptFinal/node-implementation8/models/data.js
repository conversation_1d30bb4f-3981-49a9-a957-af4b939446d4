const { generateId } = require('../utils/helpers');
const { TERM_STATES, COURSE_STATES, ENROLLMENT_STATES } = require('../utils/constants');

class InMemoryStorage {
  constructor() {
    this.terms = new Map();
    this.courses = new Map();
    this.enrollments = new Map();
    this.courseSeatLedgers = new Map();
    this.studentTuitionLedgers = new Map();
    this.dropCounts = new Map(); // studentId_termId -> count
  }

  createTerm(data) {
    const term = {
      id: generateId(),
      name: data.name,
      state: TERM_STATES.PLANNING,
      created_by: data.created_by,
      created_at: new Date().toISOString(),
      revision: 0
    };
    this.terms.set(term.id, term);
    return term;
  }

  getTerm(termId) {
    return this.terms.get(termId);
  }

  updateTerm(termId, updates) {
    const term = this.terms.get(termId);
    if (!term) return null;
    
    const updatedTerm = { ...term, ...updates, revision: term.revision + 1 };
    this.terms.set(termId, updatedTerm);
    return updatedTerm;
  }

  createCourse(termId, data) {
    const course = {
      id: generateId(),
      term_id: termId,
      code: data.code,
      title: data.title,
      description: data.description || '',
      credits: data.credits,
      capacity: data.capacity,
      professor_id: data.professor_id,
      delivery_mode: data.delivery_mode,
      location: data.location || null,
      online_link: data.online_link || null,
      state: COURSE_STATES.DRAFT,
      created_at: new Date().toISOString(),
      revision: 0
    };
    this.courses.set(course.id, course);
    return course;
  }

  getCourse(courseId) {
    return this.courses.get(courseId);
  }

  getCoursesByTerm(termId) {
    return Array.from(this.courses.values()).filter(course => course.term_id === termId);
  }

  getCoursesByProfessor(termId, professorId) {
    return Array.from(this.courses.values()).filter(
      course => course.term_id === termId && course.professor_id === professorId
    );
  }

  updateCourse(courseId, updates) {
    const course = this.courses.get(courseId);
    if (!course) return null;
    
    const updatedCourse = { ...course, ...updates, revision: course.revision + 1 };
    this.courses.set(courseId, updatedCourse);
    return updatedCourse;
  }

  createEnrollment(courseId, studentId) {
    const enrollment = {
      id: generateId(),
      course_id: courseId,
      student_id: studentId,
      state: ENROLLMENT_STATES.WAITLISTED, // Will be determined by seat availability
      created_at: new Date().toISOString(),
      revision: 0
    };
    this.enrollments.set(enrollment.id, enrollment);
    return enrollment;
  }

  getEnrollment(enrollmentId) {
    return this.enrollments.get(enrollmentId);
  }

  getEnrollmentsByCourse(courseId) {
    return Array.from(this.enrollments.values()).filter(
      enrollment => enrollment.course_id === courseId
    );
  }

  getEnrollmentsByStudent(studentId, termId) {
    const termCourses = this.getCoursesByTerm(termId);
    const termCourseIds = new Set(termCourses.map(course => course.id));
    
    return Array.from(this.enrollments.values()).filter(
      enrollment => enrollment.student_id === studentId && termCourseIds.has(enrollment.course_id)
    );
  }

  getStudentEnrollmentInCourse(studentId, courseId) {
    return Array.from(this.enrollments.values()).find(
      enrollment => enrollment.student_id === studentId && 
                   enrollment.course_id === courseId &&
                   enrollment.state !== ENROLLMENT_STATES.DROPPED
    );
  }

  updateEnrollment(enrollmentId, updates) {
    const enrollment = this.enrollments.get(enrollmentId);
    if (!enrollment) return null;
    
    const updatedEnrollment = { ...enrollment, ...updates, revision: enrollment.revision + 1 };
    this.enrollments.set(enrollmentId, updatedEnrollment);
    return updatedEnrollment;
  }

  initializeCourseSeatLedger(courseId, capacity) {
    this.courseSeatLedgers.set(courseId, {
      course_id: courseId,
      seats_available: capacity,
      capacity: capacity
    });
  }

  getCourseSeatLedger(courseId) {
    return this.courseSeatLedgers.get(courseId);
  }

  updateCourseSeatLedger(courseId, change) {
    const ledger = this.courseSeatLedgers.get(courseId);
    if (!ledger) return null;
    
    const newSeatsAvailable = ledger.seats_available + change;
    if (newSeatsAvailable < 0 || newSeatsAvailable > ledger.capacity) {
      throw new Error('Invalid seat ledger operation');
    }
    
    ledger.seats_available = newSeatsAvailable;
    return ledger;
  }

  getOrCreateStudentTuitionLedger(studentId, termId) {
    const key = `${studentId}_${termId}`;
    if (!this.studentTuitionLedgers.has(key)) {
      this.studentTuitionLedgers.set(key, {
        student_id: studentId,
        term_id: termId,
        balance_cents: 0
      });
    }
    return this.studentTuitionLedgers.get(key);
  }

  updateStudentTuitionLedger(studentId, termId, change) {
    const ledger = this.getOrCreateStudentTuitionLedger(studentId, termId);
    const newBalance = ledger.balance_cents + change;
    
    if (newBalance < 0) {
      throw new Error('Invalid ledger operation - balance would be negative');
    }
    
    ledger.balance_cents = newBalance;
    return ledger;
  }

  getStudentDropCount(studentId, termId) {
    const key = `${studentId}_${termId}`;
    return this.dropCounts.get(key) || 0;
  }

  incrementStudentDropCount(studentId, termId) {
    const key = `${studentId}_${termId}`;
    const current = this.dropCounts.get(key) || 0;
    this.dropCounts.set(key, current + 1);
    return current + 1;
  }

  isTermNameUnique(name, excludeId = null) {
    return !Array.from(this.terms.values()).some(
      term => term.name === name && term.id !== excludeId
    );
  }

  isCourseCodeUniqueInTerm(termId, code, excludeId = null) {
    return !Array.from(this.courses.values()).some(
      course => course.term_id === termId && course.code === code && course.id !== excludeId
    );
  }
}

const storage = new InMemoryStorage();
module.exports = { storage };