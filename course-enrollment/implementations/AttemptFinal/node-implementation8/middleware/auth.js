const { generateId } = require('../utils/helpers');

const VALID_ROLES = ['STUDENT', 'PROFESSOR', 'REGISTRAR'];

const UUID_PATTERN = /^[0-9a-fA-F-]{36}$/;

function authMiddleware(req, res, next) {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  if (!userId || !userRole) {
    return res.status(400).sendError(
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'Missing required authentication headers X-User-ID and X-User-Role'
    );
  }

  if (!UUID_PATTERN.test(userId)) {
    return res.status(400).sendError(
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'X-User-ID must be a valid UUID format'
    );
  }

  if (!VALID_ROLES.includes(userRole)) {
    return res.status(400).sendError(
      'ERR_INVALID_ENUM_VALUE',
      'X-User-Role must be one of: STUDENT, PROFESSOR, REGISTRAR'
    );
  }

  req.user = {
    id: userId,
    role: userRole
  };

  next();
}

module.exports = { authMiddleware };