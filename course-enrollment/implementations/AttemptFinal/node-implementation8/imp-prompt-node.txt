You are an expert Node.js API developer. Your task is to implement an API based *strictly* on the provided Product Requirements Document (PRD).
   

  **Overall Goal:**
  Create a fully functional Node.js API that implements all features, resources, endpoints, business rules, validation, and error 
  handling as defined in the PRD.

  **Key Instructions:**

  1.  **PRD Adherence (Primary):** The PRD is the single source of truth. Implement every detail.

  2.  **Deliverables:**
      * A complete Node.js implementation of the PRD. 

  Remember the PRD.md is the gospel. Refer to this closely as the source of truth, make sure to keep re-reading it so you dont stray.
Keep it simple, don't over-modularize or over-engineer, use in-memory data (dont use an actual database).