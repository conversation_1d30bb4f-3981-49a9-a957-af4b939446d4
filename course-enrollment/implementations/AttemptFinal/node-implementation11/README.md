# University Course Registration & Enrollment API

A complete Node.js implementation of the University Course Registration & Enrollment API based on the provided PRD.

## Features

- **Term Management**: Create, open, close, and conclude academic terms
- **Course Management**: Create, publish, and cancel courses with role-based permissions
- **Enrollment System**: Student enrollment with waitlist support and automatic promotion
- **Payment Processing**: Tuition payment with balance tracking
- **Ledger Systems**: Automated seat and tuition tracking
- **RBAC**: Role-based access control (<PERSON>, Professor, Registrar)
- **Business Rules**: Credit limits, drop penalties, capacity management

## Getting Started

1. Install dependencies:
```bash
npm install
```

2. Start the server:
```bash
npm start
```

The API will be available at `http://localhost:3000`

## Authentication

All requests require these headers:
- `X-User-ID`: User's UUID
- `X-User-Role`: One of `STUDENT`, `PROFESSOR`, or `REGISTRAR`

## API Endpoints

### Term Management
- `POST /terms` - Create a new term
- `GET /terms/{termId}` - Get term details
- `PATCH /terms/{termId}/open-registration` - Open enrollment
- `PATCH /terms/{termId}/close-registration` - Close enrollment
- `PATCH /terms/{termId}/conclude` - Conclude term

### Course Management
- `POST /terms/{termId}/courses` - Create a course
- `GET /terms/{termId}/courses` - List courses
- `GET /terms/{termId}/courses/{courseId}` - Get course details
- `PATCH /terms/{termId}/courses/{courseId}/publish` - Publish course
- `PATCH /terms/{termId}/courses/{courseId}/cancel` - Cancel course

### Enrollment Management
- `POST /terms/{termId}/courses/{courseId}/enrollments` - Enroll in course
- `GET /terms/{termId}/courses/{courseId}/enrollments` - List enrollments
- `GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}` - Get enrollment
- `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}/drop` - Drop enrollment

### Payment
- `POST /terms/{termId}/students/{studentId}/pay` - Make payment

## Data Storage

This implementation uses in-memory data storage. All data is lost when the server restarts.

## Business Rules

- Students can enroll in max 18 credits per term
- Professors can teach max 5 courses per term
- Students can drop max 3 courses per term (with $50 penalty on 3rd drop)
- Automatic waitlist promotion when seats become available
- Course capacity management with seat ledgers
- Tuition tracking with payment processing