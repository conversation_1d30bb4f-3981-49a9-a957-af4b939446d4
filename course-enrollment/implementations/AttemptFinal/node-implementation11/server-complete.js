const express = require('express');
const { v4: uuidv4 } = require('uuid');

const app = express();
app.use(express.json());

// Constants from PRD
const MAX_CREDITS_PER_TERM = 18;
const MAX_COURSES_PER_PROF = 5;
const MAX_DROP_COUNT_PER_TERM = 3;
const DROP_PENALTY_FEE = 5000; // cents
const COST_PER_CREDIT = 10000; // cents ($100.00)

// In-memory data storage
const data = {
  terms: new Map(),
  courses: new Map(),
  enrollments: new Map(),
  courseSeatLedgers: new Map(),
  studentTuitionLedgers: new Map(),
  users: new Map() // For validation purposes
};

// Utility functions
function generateId() {
  return uuidv4();
}

function generateRequestId() {
  return 'req_' + Math.random().toString(36).substr(2, 16).toUpperCase();
}

function validateUUID(id) {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i;
  return uuidRegex.test(id);
}

function validateCourseCode(code) {
  const codeRegex = /^[A-Z]{2,4}\d{3}$/;
  return codeRegex.test(code);
}

// Standard response wrapper
function createSuccessResponse(req, data, responseType = 'object') {
  return {
    meta: {
      api_request_id: req.requestId,
      api_request_timestamp: new Date().toISOString()
    },
    response_type: responseType,
    data: data
  };
}

function createErrorResponse(req, errorId, message) {
  return {
    meta: {
      api_request_id: req.requestId,
      api_request_timestamp: new Date().toISOString()
    },
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };
}

// Middleware to generate request ID
app.use((req, res, next) => {
  req.requestId = generateRequestId();
  next();
});

// Authentication middleware
app.use((req, res, next) => {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  if (!userId || !userRole) {
    return res.status(400).json(createErrorResponse(req, 'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER', 
      'Missing or invalid user context headers'));
  }

  if (!validateUUID(userId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid user ID format'));
  }

  const validRoles = ['STUDENT', 'PROFESSOR', 'REGISTRAR'];
  if (!validRoles.includes(userRole)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ENUM_VALUE', 
      'Invalid user role'));
  }

  req.auth = { userId, userRole };
  next();
});

// Validation middleware for unknown fields
function validateNoUnknownFields(allowedFields) {
  return (req, res, next) => {
    if (req.body && typeof req.body === 'object') {
      const providedFields = Object.keys(req.body);
      const unknownFields = providedFields.filter(field => !allowedFields.includes(field));
      
      if (unknownFields.length > 0) {
        return res.status(400).json(createErrorResponse(req, 'ERR_UNKNOWN_FIELD', 
          `Unknown field(s): ${unknownFields.join(', ')}`));
      }
    }
    next();
  };
}

// Helper function to get course counts for display
function getCourseStats(courseId) {
  let enrolledCount = 0;
  let waitlistCount = 0;
  
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.course_id === courseId) {
      if (enrollment.state === 'ENROLLED') {
        enrolledCount++;
      } else if (enrollment.state === 'WAITLISTED') {
        waitlistCount++;
      }
    }
  }
  
  const course = data.courses.get(courseId);
  const availableSeats = course ? Math.max(0, course.capacity - enrolledCount) : 0;
  
  return { enrolledCount, waitlistCount, availableSeats };
}

// Helper function to promote waitlisted students
function promoteFromWaitlist(courseId) {
  // Find the earliest waitlisted enrollment for this course
  const waitlistedEnrollments = Array.from(data.enrollments.values())
    .filter(e => e.course_id === courseId && e.state === 'WAITLISTED')
    .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  if (waitlistedEnrollments.length === 0) {
    return null;
  }

  const enrollment = waitlistedEnrollments[0];
  const course = data.courses.get(courseId);
  
  if (!course) {
    return null;
  }

  // Promote the student
  enrollment.state = 'ENROLLED';
  enrollment.revision++;

  // Update seat ledger
  const seatLedger = data.courseSeatLedgers.get(courseId);
  if (seatLedger) {
    seatLedger.seats_available--;
  }

  // Update tuition ledger
  const tuitionCost = course.credits * COST_PER_CREDIT;
  const ledgerKey = `${course.term_id}-${enrollment.student_id}`;
  let tuitionLedger = data.studentTuitionLedgers.get(ledgerKey);
  
  if (!tuitionLedger) {
    tuitionLedger = {
      term_id: course.term_id,
      student_id: enrollment.student_id,
      balance_cents: 0
    };
    data.studentTuitionLedgers.set(ledgerKey, tuitionLedger);
  }
  
  tuitionLedger.balance_cents += tuitionCost;

  return enrollment;
}

// Term Management Endpoints

// POST /terms - Create a new academic term
app.post('/terms', validateNoUnknownFields(['name']), (req, res) => {
  // Only Registrar can create terms
  if (req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Only Registrar can create terms'));
  }

  const { name } = req.body;

  if (!name || typeof name !== 'string' || name.trim() === '') {
    return res.status(400).json(createErrorResponse(req, 'ERR_MISSING_REQUIRED_FIELD', 
      'Term name is required'));
  }

  // Check for duplicate term name
  for (const term of data.terms.values()) {
    if (term.name === name.trim()) {
      return res.status(409).json(createErrorResponse(req, 'ERR_TERM_NAME_NOT_UNIQUE', 
        'A term with this name already exists'));
    }
  }

  const termId = generateId();
  const term = {
    id: termId,
    name: name.trim(),
    state: 'PLANNING',
    created_by: req.auth.userId,
    created_at: new Date().toISOString(),
    revision: 0
  };

  data.terms.set(termId, term);

  res.status(201).json(createSuccessResponse(req, term));
});

// GET /terms/{termId} - Retrieve details of an academic term
app.get('/terms/:termId', (req, res) => {
  const { termId } = req.params;

  if (!validateUUID(termId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid term ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  res.json(createSuccessResponse(req, term));
});

// PATCH /terms/{termId}:open-registration - Open student registration
app.patch('/terms/:termId/open-registration', (req, res) => {
  // Only Registrar can open registration
  if (req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Only Registrar can open registration'));
  }

  const { termId } = req.params;

  if (!validateUUID(termId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid term ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  if (term.state !== 'PLANNING') {
    return res.status(409).json(createErrorResponse(req, 'ERR_TERM_NOT_ACTIVE', 
      'Term is not in PLANNING state'));
  }

  term.state = 'ENROLLMENT_OPEN';
  term.revision++;

  res.json(createSuccessResponse(req, term));
});

// PATCH /terms/{termId}:close-registration - Close enrollment period
app.patch('/terms/:termId/close-registration', (req, res) => {
  // Only Registrar can close registration
  if (req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Only Registrar can close registration'));
  }

  const { termId } = req.params;

  if (!validateUUID(termId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid term ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  if (term.state !== 'ENROLLMENT_OPEN') {
    return res.status(409).json(createErrorResponse(req, 'ERR_TERM_NOT_ACTIVE', 
      'Term is not in ENROLLMENT_OPEN state'));
  }

  term.state = 'ENROLLMENT_CLOSED';
  term.revision++;

  // Cascade update: all OPEN courses transition to IN_PROGRESS
  for (const course of data.courses.values()) {
    if (course.term_id === termId && course.state === 'OPEN') {
      course.state = 'IN_PROGRESS';
      course.revision++;
    }
  }

  res.json(createSuccessResponse(req, term));
});

// PATCH /terms/{termId}:conclude - Conclude the term
app.patch('/terms/:termId/conclude', (req, res) => {
  // Only Registrar can conclude term
  if (req.auth.userRole !== 'REGISTRAR') {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Only Registrar can conclude term'));
  }

  const { termId } = req.params;

  if (!validateUUID(termId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid term ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  if (term.state !== 'ENROLLMENT_CLOSED') {
    return res.status(409).json(createErrorResponse(req, 'ERR_TERM_NOT_ACTIVE', 
      'Term must be in ENROLLMENT_CLOSED state to conclude'));
  }

  term.state = 'CONCLUDED';
  term.revision++;

  // Cascade update: all IN_PROGRESS courses to COMPLETED, finalize enrollments
  for (const course of data.courses.values()) {
    if (course.term_id === termId && course.state === 'IN_PROGRESS') {
      course.state = 'COMPLETED';
      course.revision++;
    }
  }

  // Finalize enrollments
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.term_id === termId) {
      if (enrollment.state === 'ENROLLED') {
        enrollment.state = 'COMPLETED';
      } else if (enrollment.state === 'WAITLISTED') {
        enrollment.state = 'DROPPED';
      }
      enrollment.revision++;
    }
  }

  res.json(createSuccessResponse(req, term));
});

// Course Management Endpoints

// POST /terms/{termId}/courses - Create a new course
app.post('/terms/:termId/courses', validateNoUnknownFields([
  'code', 'title', 'description', 'credits', 'capacity', 'professor_id', 'delivery_mode', 'location', 'online_link'
]), (req, res) => {
  const { termId } = req.params;
  const { code, title, description, credits, capacity, professor_id, delivery_mode, location, online_link } = req.body;

  // Validate term ID
  if (!validateUUID(termId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid term ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  // Role authorization
  if (!['PROFESSOR', 'REGISTRAR'].includes(req.auth.userRole)) {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Only Professor or Registrar can create courses'));
  }

  // Validate required fields
  if (!code || !title || credits === undefined || capacity === undefined || !delivery_mode) {
    return res.status(400).json(createErrorResponse(req, 'ERR_MISSING_REQUIRED_FIELD', 
      'Missing required fields: code, title, credits, capacity, delivery_mode'));
  }

  // Validate course code format
  if (!validateCourseCode(code)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_COURSE_CODE', 
      'Course code must be 2-4 uppercase letters followed by 3 digits'));
  }

  // Check code uniqueness within term
  for (const course of data.courses.values()) {
    if (course.term_id === termId && course.code === code) {
      return res.status(409).json(createErrorResponse(req, 'ERR_COURSE_CODE_NOT_UNIQUE', 
        'Course code already exists in this term'));
    }
  }

  // Validate title length
  if (title.length === 0 || title.length > 100) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_FIELD_LENGTH', 
      'Title must be between 1 and 100 characters'));
  }

  // Validate description length
  if (description && description.length > 1000) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_FIELD_LENGTH', 
      'Description must not exceed 1000 characters'));
  }

  // Validate credits
  if (!Number.isInteger(credits) || credits < 1 || credits > 5) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_CREDITS', 
      'Credits must be an integer between 1 and 5'));
  }

  // Validate capacity
  if (!Number.isInteger(capacity) || capacity < 1 || capacity > 500) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_CAPACITY', 
      'Capacity must be an integer between 1 and 500'));
  }

  // Validate delivery mode
  const validDeliveryModes = ['IN_PERSON', 'ONLINE', 'HYBRID'];
  if (!validDeliveryModes.includes(delivery_mode)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ENUM_VALUE', 
      'Invalid delivery mode'));
  }

  // Validate delivery mode requirements
  if (delivery_mode === 'IN_PERSON') {
    if (!location || location.trim() === '') {
      return res.status(400).json(createErrorResponse(req, 'ERR_CONDITIONAL_FIELD_REQUIRED', 
        'Location is required for IN_PERSON courses'));
    }
    if (online_link) {
      return res.status(400).json(createErrorResponse(req, 'ERR_FIELD_CONFLICT', 
        'Online link cannot be provided for IN_PERSON courses'));
    }
  } else if (delivery_mode === 'ONLINE') {
    if (!online_link || online_link.trim() === '') {
      return res.status(400).json(createErrorResponse(req, 'ERR_CONDITIONAL_FIELD_REQUIRED', 
        'Online link is required for ONLINE courses'));
    }
    if (location) {
      return res.status(400).json(createErrorResponse(req, 'ERR_FIELD_CONFLICT', 
        'Location cannot be provided for ONLINE courses'));
    }
  } else if (delivery_mode === 'HYBRID') {
    if ((!location || location.trim() === '') && (!online_link || online_link.trim() === '')) {
      return res.status(400).json(createErrorResponse(req, 'ERR_CONDITIONAL_FIELD_REQUIRED', 
        'At least one of location or online_link is required for HYBRID courses'));
    }
  }

  // Determine professor ID
  let actualProfessorId;
  if (req.auth.userRole === 'PROFESSOR') {
    if (professor_id && professor_id !== req.auth.userId) {
      return res.status(400).json(createErrorResponse(req, 'ERR_FIELD_CONFLICT', 
        'Professor cannot create course for another professor'));
    }
    actualProfessorId = req.auth.userId;
  } else {
    // Registrar case
    if (!professor_id) {
      return res.status(400).json(createErrorResponse(req, 'ERR_MISSING_REQUIRED_FIELD', 
        'Professor ID is required when Registrar creates course'));
    }
    if (!validateUUID(professor_id)) {
      return res.status(422).json(createErrorResponse(req, 'ERR_INVALID_INSTRUCTOR', 
        'Invalid professor ID format'));
    }
    actualProfessorId = professor_id;
  }

  // Check professor course limit
  let professorCourseCount = 0;
  for (const course of data.courses.values()) {
    if (course.term_id === termId && course.professor_id === actualProfessorId && 
        !['CANCELLED', 'COMPLETED'].includes(course.state)) {
      professorCourseCount++;
    }
  }

  if (professorCourseCount >= MAX_COURSES_PER_PROF) {
    return res.status(409).json(createErrorResponse(req, 'ERR_MAX_COURSES_REACHED', 
      'Professor has reached maximum course limit'));
  }

  // Create course
  const courseId = generateId();
  const course = {
    id: courseId,
    term_id: termId,
    code,
    title,
    description: description || '',
    credits,
    capacity,
    professor_id: actualProfessorId,
    delivery_mode,
    location: location || null,
    online_link: online_link || null,
    state: 'DRAFT',
    created_at: new Date().toISOString(),
    revision: 0
  };

  data.courses.set(courseId, course);

  res.status(201).json(createSuccessResponse(req, course));
});

// GET /terms/{termId}/courses - List courses in the term
app.get('/terms/:termId/courses', (req, res) => {
  const { termId } = req.params;
  const { limit = 50, offset = 0, state, professor_id } = req.query;

  if (!validateUUID(termId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid term ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  // Get courses for this term
  let courses = Array.from(data.courses.values()).filter(course => course.term_id === termId);

  // Apply filters
  if (state) {
    courses = courses.filter(course => course.state === state);
  }
  if (professor_id) {
    courses = courses.filter(course => course.professor_id === professor_id);
  }

  // Role-based filtering
  if (req.auth.userRole === 'STUDENT') {
    // Students only see published or in-progress/completed courses
    courses = courses.filter(course => ['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state));
  } else if (req.auth.userRole === 'PROFESSOR') {
    // Professors see all their own courses + other published courses
    courses = courses.filter(course => 
      course.professor_id === req.auth.userId || 
      ['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)
    );
  }
  // Registrar sees all courses (no additional filtering)

  // Sort by creation time for stable pagination
  courses.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  // Apply pagination
  const parsedLimit = Math.min(Math.max(1, parseInt(limit)), 100);
  const parsedOffset = Math.max(0, parseInt(offset));
  const paginatedCourses = courses.slice(parsedOffset, parsedOffset + parsedLimit);

  // Enhance courses with stats based on role
  const enhancedCourses = paginatedCourses.map(course => {
    const baseCourse = { ...course };
    const stats = getCourseStats(course.id);

    if (req.auth.userRole === 'STUDENT') {
      baseCourse.available_seats = stats.availableSeats;
      // Check if student is enrolled/waitlisted
      const studentEnrollment = Array.from(data.enrollments.values()).find(e => 
        e.course_id === course.id && e.student_id === req.auth.userId
      );
      if (studentEnrollment) {
        baseCourse.is_enrolled = studentEnrollment.state === 'ENROLLED';
        baseCourse.is_waitlisted = studentEnrollment.state === 'WAITLISTED';
      } else {
        baseCourse.is_enrolled = false;
        baseCourse.is_waitlisted = false;
      }
    } else if (req.auth.userRole === 'PROFESSOR' && course.professor_id === req.auth.userId) {
      // Professor sees stats for their own courses
      baseCourse.enrolled_count = stats.enrolledCount;
      baseCourse.waitlist_count = stats.waitlistCount;
      baseCourse.available_seats = stats.availableSeats;
    } else if (req.auth.userRole === 'REGISTRAR') {
      // Registrar sees all stats
      baseCourse.enrolled_count = stats.enrolledCount;
      baseCourse.waitlist_count = stats.waitlistCount;
      baseCourse.available_seats = stats.availableSeats;
    } else {
      // Other professors viewing published courses get limited info
      baseCourse.available_seats = stats.availableSeats;
    }

    return baseCourse;
  });

  res.json(createSuccessResponse(req, enhancedCourses, 'array'));
});

// GET /terms/{termId}/courses/{courseId} - Get detailed info on a specific course
app.get('/terms/:termId/courses/:courseId', (req, res) => {
  const { termId, courseId } = req.params;

  if (!validateUUID(termId) || !validateUUID(courseId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  const course = data.courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(req, 'ERR_COURSE_NOT_FOUND', 
      'Course not found'));
  }

  // Role-based visibility
  if (req.auth.userRole === 'STUDENT') {
    // Students can only see published courses or courses they're involved with
    if (!['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)) {
      // Check if student is enrolled/waitlisted
      const studentEnrollment = Array.from(data.enrollments.values()).find(e => 
        e.course_id === courseId && e.student_id === req.auth.userId
      );
      if (!studentEnrollment) {
        return res.status(404).json(createErrorResponse(req, 'ERR_COURSE_NOT_FOUND', 
          'Course not found'));
      }
    }
  } else if (req.auth.userRole === 'PROFESSOR') {
    // Professors can see their own courses or published courses
    if (course.professor_id !== req.auth.userId && 
        !['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state)) {
      return res.status(404).json(createErrorResponse(req, 'ERR_COURSE_NOT_FOUND', 
        'Course not found'));
    }
  }
  // Registrar can see all courses

  const enhancedCourse = { ...course };
  const stats = getCourseStats(courseId);

  if (req.auth.userRole === 'STUDENT') {
    enhancedCourse.available_seats = stats.availableSeats;
    // Check student's enrollment status
    const studentEnrollment = Array.from(data.enrollments.values()).find(e => 
      e.course_id === courseId && e.student_id === req.auth.userId
    );
    if (studentEnrollment) {
      enhancedCourse.is_enrolled = studentEnrollment.state === 'ENROLLED';
      enhancedCourse.is_waitlisted = studentEnrollment.state === 'WAITLISTED';
    } else {
      enhancedCourse.is_enrolled = false;
      enhancedCourse.is_waitlisted = false;
    }
  } else if (req.auth.userRole === 'PROFESSOR' && course.professor_id === req.auth.userId) {
    // Professor sees detailed stats for their own course
    enhancedCourse.enrolled_count = stats.enrolledCount;
    enhancedCourse.waitlist_count = stats.waitlistCount;
    enhancedCourse.available_seats = stats.availableSeats;
    
    // Include enrollment roster
    const enrollments = Array.from(data.enrollments.values())
      .filter(e => e.course_id === courseId)
      .map(e => ({
        id: e.id,
        student_id: e.student_id,
        state: e.state,
        created_at: e.created_at
      }));
    enhancedCourse.enrollments = enrollments;
  } else if (req.auth.userRole === 'REGISTRAR') {
    // Registrar sees everything
    enhancedCourse.enrolled_count = stats.enrolledCount;
    enhancedCourse.waitlist_count = stats.waitlistCount;
    enhancedCourse.available_seats = stats.availableSeats;
    
    // Include enrollment roster
    const enrollments = Array.from(data.enrollments.values())
      .filter(e => e.course_id === courseId)
      .map(e => ({
        id: e.id,
        student_id: e.student_id,
        state: e.state,
        created_at: e.created_at
      }));
    enhancedCourse.enrollments = enrollments;
  } else {
    // Other professors viewing published courses
    enhancedCourse.available_seats = stats.availableSeats;
  }

  res.json(createSuccessResponse(req, enhancedCourse));
});

// PATCH /terms/{termId}/courses/{courseId}:publish - Publish a draft course
app.patch('/terms/:termId/courses/:courseId/publish', (req, res) => {
  const { termId, courseId } = req.params;

  if (!validateUUID(termId) || !validateUUID(courseId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  // Term must be open for enrollment
  if (term.state !== 'ENROLLMENT_OPEN') {
    return res.status(409).json(createErrorResponse(req, 'ERR_TERM_NOT_ACTIVE', 
      'Term is not open for enrollment'));
  }

  const course = data.courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(req, 'ERR_COURSE_NOT_FOUND', 
      'Course not found'));
  }

  // Authorization check
  if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse(req, 'ERR_NOT_INSTRUCTOR', 
      'Only the course instructor can publish this course'));
  } else if (req.auth.userRole === 'STUDENT') {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Students cannot publish courses'));
  }

  // Course must be in DRAFT state
  if (course.state !== 'DRAFT') {
    return res.status(409).json(createErrorResponse(req, 'ERR_COURSE_WRONG_STATE', 
      'Course is not in DRAFT state'));
  }

  // Publish the course
  course.state = 'OPEN';
  course.revision++;
  course.published_at = new Date().toISOString();

  // Initialize seat ledger
  data.courseSeatLedgers.set(courseId, {
    course_id: courseId,
    seats_available: course.capacity
  });

  res.json(createSuccessResponse(req, course));
});

// PATCH /terms/{termId}/courses/{courseId}:cancel - Cancel a course
app.patch('/terms/:termId/courses/:courseId/cancel', (req, res) => {
  const { termId, courseId } = req.params;

  if (!validateUUID(termId) || !validateUUID(courseId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  const course = data.courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(req, 'ERR_COURSE_NOT_FOUND', 
      'Course not found'));
  }

  // Authorization check
  if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse(req, 'ERR_NOT_INSTRUCTOR', 
      'Only the course instructor can cancel this course'));
  } else if (req.auth.userRole === 'STUDENT') {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Students cannot cancel courses'));
  }

  // Course must be cancellable
  if (!['DRAFT', 'OPEN', 'IN_PROGRESS'].includes(course.state)) {
    return res.status(409).json(createErrorResponse(req, 'ERR_COURSE_WRONG_STATE', 
      'Course cannot be cancelled in current state'));
  }

  // Cancel the course
  course.state = 'CANCELLED';
  course.revision++;

  // Drop all enrollments and handle refunds
  for (const enrollment of data.enrollments.values()) {
    if (enrollment.course_id === courseId && 
        ['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {
      
      const previousState = enrollment.state;
      enrollment.state = 'DROPPED';
      enrollment.revision++;

      // If student was enrolled, refund tuition
      if (previousState === 'ENROLLED') {
        const tuitionCost = course.credits * COST_PER_CREDIT;
        const ledgerKey = `${termId}-${enrollment.student_id}`;
        const ledger = data.studentTuitionLedgers.get(ledgerKey);
        if (ledger) {
          ledger.balance_cents = Math.max(0, ledger.balance_cents - tuitionCost);
        }
      }
    }
  }

  // Reset course seat ledger
  data.courseSeatLedgers.delete(courseId);

  res.json(createSuccessResponse(req, course));
});

// Enrollment Management Endpoints

// POST /terms/{termId}/courses/{courseId}/enrollments - Enroll in a course
app.post('/terms/:termId/courses/:courseId/enrollments', 
  validateNoUnknownFields(['student_id']), (req, res) => {
  
  const { termId, courseId } = req.params;
  const { student_id } = req.body;

  if (!validateUUID(termId) || !validateUUID(courseId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  // Term must be open for enrollment
  if (term.state !== 'ENROLLMENT_OPEN') {
    return res.status(409).json(createErrorResponse(req, 'ERR_REGISTRATION_CLOSED', 
      'Registration is not open'));
  }

  const course = data.courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(req, 'ERR_COURSE_NOT_FOUND', 
      'Course not found'));
  }

  // Course must be open for enrollment
  if (course.state !== 'OPEN') {
    return res.status(409).json(createErrorResponse(req, 'ERR_COURSE_WRONG_STATE', 
      'Course is not open for enrollment'));
  }

  // Determine target student
  let targetStudentId;
  if (req.auth.userRole === 'STUDENT') {
    if (student_id && student_id !== req.auth.userId) {
      return res.status(403).json(createErrorResponse(req, 'ERR_FORBIDDEN', 
        'Students can only enroll themselves'));
    }
    targetStudentId = req.auth.userId;
  } else if (req.auth.userRole === 'REGISTRAR') {
    if (!student_id) {
      return res.status(400).json(createErrorResponse(req, 'ERR_MISSING_REQUIRED_FIELD', 
        'Student ID is required'));
    }
    if (!validateUUID(student_id)) {
      return res.status(404).json(createErrorResponse(req, 'ERR_STUDENT_NOT_FOUND', 
        'Invalid student ID'));
    }
    targetStudentId = student_id;
  } else {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Only students and registrars can create enrollments'));
  }

  // Check for duplicate enrollment
  const existingEnrollment = Array.from(data.enrollments.values()).find(e => 
    e.course_id === courseId && e.student_id === targetStudentId && 
    ['ENROLLED', 'WAITLISTED'].includes(e.state)
  );

  if (existingEnrollment) {
    return res.status(409).json(createErrorResponse(req, 'ERR_ALREADY_ENROLLED', 
      'Student is already enrolled or waitlisted in this course'));
  }

  // Check credit limit for students (not for registrar overrides)
  if (req.auth.userRole === 'STUDENT') {
    let currentCredits = 0;
    for (const enrollment of data.enrollments.values()) {
      if (enrollment.student_id === targetStudentId && 
          enrollment.state === 'ENROLLED') {
        const enrolledCourse = data.courses.get(enrollment.course_id);
        if (enrolledCourse && enrolledCourse.term_id === termId) {
          currentCredits += enrolledCourse.credits;
        }
      }
    }

    if (currentCredits + course.credits > MAX_CREDITS_PER_TERM) {
      return res.status(409).json(createErrorResponse(req, 'ERR_CREDIT_LIMIT_EXCEEDED', 
        'Enrollment would exceed credit limit'));
    }
  }

  // Check seat availability
  const seatLedger = data.courseSeatLedgers.get(courseId);
  const seatsAvailable = seatLedger ? seatLedger.seats_available : course.capacity;
  
  // Create enrollment
  const enrollmentId = generateId();
  const enrollment = {
    id: enrollmentId,
    course_id: courseId,
    student_id: targetStudentId,
    term_id: termId,
    state: seatsAvailable > 0 ? 'ENROLLED' : 'WAITLISTED',
    created_at: new Date().toISOString(),
    revision: 0
  };

  data.enrollments.set(enrollmentId, enrollment);

  // Update ledgers if enrolled
  if (enrollment.state === 'ENROLLED') {
    // Update seat ledger
    if (seatLedger) {
      seatLedger.seats_available--;
    } else {
      data.courseSeatLedgers.set(courseId, {
        course_id: courseId,
        seats_available: course.capacity - 1
      });
    }

    // Update tuition ledger
    const tuitionCost = course.credits * COST_PER_CREDIT;
    const ledgerKey = `${termId}-${targetStudentId}`;
    let tuitionLedger = data.studentTuitionLedgers.get(ledgerKey);
    
    if (!tuitionLedger) {
      tuitionLedger = {
        term_id: termId,
        student_id: targetStudentId,
        balance_cents: 0
      };
      data.studentTuitionLedgers.set(ledgerKey, tuitionLedger);
    }
    
    tuitionLedger.balance_cents += tuitionCost;
  }

  res.status(201).json(createSuccessResponse(req, enrollment));
});

// GET /terms/{termId}/courses/{courseId}/enrollments - List enrollments for a course
app.get('/terms/:termId/courses/:courseId/enrollments', (req, res) => {
  const { termId, courseId } = req.params;
  const { limit = 50, offset = 0 } = req.query;

  if (!validateUUID(termId) || !validateUUID(courseId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  const course = data.courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(req, 'ERR_COURSE_NOT_FOUND', 
      'Course not found'));
  }

  // Authorization - only professor of course or registrar can list enrollments
  if (req.auth.userRole === 'STUDENT') {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Students cannot list course enrollments'));
  } else if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse(req, 'ERR_NOT_INSTRUCTOR', 
      'Only the course instructor can view enrollments'));
  }

  // Get enrollments for this course
  let enrollments = Array.from(data.enrollments.values())
    .filter(e => e.course_id === courseId);

  // Sort by creation time for stable pagination
  enrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  // Apply pagination
  const parsedLimit = Math.min(Math.max(1, parseInt(limit)), 100);
  const parsedOffset = Math.max(0, parseInt(offset));
  const paginatedEnrollments = enrollments.slice(parsedOffset, parsedOffset + parsedLimit);

  res.json(createSuccessResponse(req, paginatedEnrollments, 'array'));
});

// GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId} - Get specific enrollment
app.get('/terms/:termId/courses/:courseId/enrollments/:enrollmentId', (req, res) => {
  const { termId, courseId, enrollmentId } = req.params;

  if (!validateUUID(termId) || !validateUUID(courseId) || !validateUUID(enrollmentId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  const course = data.courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(req, 'ERR_COURSE_NOT_FOUND', 
      'Course not found'));
  }

  const enrollment = data.enrollments.get(enrollmentId);
  if (!enrollment || enrollment.course_id !== courseId) {
    return res.status(404).json(createErrorResponse(req, 'ERR_ENROLLMENT_NOT_FOUND', 
      'Enrollment not found'));
  }

  // Authorization
  if (req.auth.userRole === 'STUDENT' && enrollment.student_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse(req, 'ERR_FORBIDDEN', 
      'Students can only view their own enrollments'));
  } else if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse(req, 'ERR_NOT_INSTRUCTOR', 
      'Only the course instructor can view enrollments'));
  }

  res.json(createSuccessResponse(req, enrollment));
});

// PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop - Drop an enrollment
app.patch('/terms/:termId/courses/:courseId/enrollments/:enrollmentId/drop', (req, res) => {
  const { termId, courseId, enrollmentId } = req.params;

  if (!validateUUID(termId) || !validateUUID(courseId) || !validateUUID(enrollmentId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  // Term must allow drops
  if (term.state === 'CONCLUDED') {
    return res.status(409).json(createErrorResponse(req, 'ERR_TERM_NOT_ACTIVE', 
      'Cannot drop enrollments in concluded term'));
  }

  const course = data.courses.get(courseId);
  if (!course || course.term_id !== termId) {
    return res.status(404).json(createErrorResponse(req, 'ERR_COURSE_NOT_FOUND', 
      'Course not found'));
  }

  const enrollment = data.enrollments.get(enrollmentId);
  if (!enrollment || enrollment.course_id !== courseId) {
    return res.status(404).json(createErrorResponse(req, 'ERR_ENROLLMENT_NOT_FOUND', 
      'Enrollment not found'));
  }

  // Authorization
  if (req.auth.userRole === 'STUDENT' && enrollment.student_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse(req, 'ERR_FORBIDDEN', 
      'Students can only drop their own enrollments'));
  } else if (req.auth.userRole === 'PROFESSOR' && course.professor_id !== req.auth.userId) {
    return res.status(403).json(createErrorResponse(req, 'ERR_NOT_INSTRUCTOR', 
      'Only the course instructor can drop enrollments'));
  }

  // Enrollment must be droppable
  if (!['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {
    return res.status(409).json(createErrorResponse(req, 'ERR_ENROLLMENT_WRONG_STATE', 
      'Enrollment cannot be dropped in current state'));
  }

  // Check drop limit for students
  if (req.auth.userRole === 'STUDENT') {
    const studentDrops = Array.from(data.enrollments.values())
      .filter(e => e.student_id === enrollment.student_id && 
                   e.term_id === termId && 
                   e.state === 'DROPPED').length;

    if (studentDrops >= MAX_DROP_COUNT_PER_TERM) {
      return res.status(409).json(createErrorResponse(req, 'ERR_TOO_MANY_DROPS', 
        'Student has reached maximum drop limit'));
    }
  }

  const previousState = enrollment.state;
  enrollment.state = 'DROPPED';
  enrollment.revision++;

  // Handle ledger updates
  if (previousState === 'ENROLLED') {
    // Free up a seat
    const seatLedger = data.courseSeatLedgers.get(courseId);
    if (seatLedger) {
      seatLedger.seats_available++;
    }

    // Refund tuition
    const tuitionCost = course.credits * COST_PER_CREDIT;
    const ledgerKey = `${termId}-${enrollment.student_id}`;
    const tuitionLedger = data.studentTuitionLedgers.get(ledgerKey);
    if (tuitionLedger) {
      tuitionLedger.balance_cents = Math.max(0, tuitionLedger.balance_cents - tuitionCost);
    }

    // Try to promote someone from waitlist
    setTimeout(() => promoteFromWaitlist(courseId), 0);
  }

  // Apply drop penalty if this is student's 3rd drop
  if (req.auth.userRole === 'STUDENT') {
    const studentDropsAfter = Array.from(data.enrollments.values())
      .filter(e => e.student_id === enrollment.student_id && 
                   e.term_id === termId && 
                   e.state === 'DROPPED').length;

    if (studentDropsAfter === MAX_DROP_COUNT_PER_TERM) {
      const ledgerKey = `${termId}-${enrollment.student_id}`;
      let tuitionLedger = data.studentTuitionLedgers.get(ledgerKey);
      
      if (!tuitionLedger) {
        tuitionLedger = {
          term_id: termId,
          student_id: enrollment.student_id,
          balance_cents: 0
        };
        data.studentTuitionLedgers.set(ledgerKey, tuitionLedger);
      }
      
      tuitionLedger.balance_cents += DROP_PENALTY_FEE;
    }
  }

  res.json(createSuccessResponse(req, enrollment));
});

// Payment Endpoint

// POST /terms/{termId}/students/{studentId}:pay - Record a tuition payment
app.post('/terms/:termId/students/:studentId/pay', 
  validateNoUnknownFields(['amount']), (req, res) => {
  
  const { termId, studentId } = req.params;
  const { amount } = req.body;

  if (!validateUUID(termId) || !validateUUID(studentId)) {
    return res.status(400).json(createErrorResponse(req, 'ERR_INVALID_ID_FORMAT', 
      'Invalid ID format'));
  }

  const term = data.terms.get(termId);
  if (!term) {
    return res.status(404).json(createErrorResponse(req, 'ERR_TERM_NOT_FOUND', 
      'Term not found'));
  }

  // Authorization
  if (req.auth.userRole === 'STUDENT' && studentId !== req.auth.userId) {
    return res.status(403).json(createErrorResponse(req, 'ERR_FORBIDDEN', 
      'Students can only pay their own balance'));
  } else if (req.auth.userRole === 'PROFESSOR') {
    return res.status(403).json(createErrorResponse(req, 'ERR_UNAUTHORIZED_ROLE', 
      'Professors cannot process payments'));
  }

  // Validate amount
  if (!Number.isInteger(amount) || amount <= 0) {
    return res.status(422).json(createErrorResponse(req, 'ERR_INVALID_PAYMENT_AMOUNT', 
      'Payment amount must be a positive integer in cents'));
  }

  // Get or create tuition ledger
  const ledgerKey = `${termId}-${studentId}`;
  let tuitionLedger = data.studentTuitionLedgers.get(ledgerKey);
  
  if (!tuitionLedger) {
    tuitionLedger = {
      term_id: termId,
      student_id: studentId,
      balance_cents: 0
    };
    data.studentTuitionLedgers.set(ledgerKey, tuitionLedger);
  }

  // Check for overpayment
  if (amount > tuitionLedger.balance_cents) {
    return res.status(422).json(createErrorResponse(req, 'ERR_OVERPAY_NOT_ALLOWED', 
      'Payment amount exceeds outstanding balance'));
  }

  // Process payment
  tuitionLedger.balance_cents -= amount;

  const paymentResult = {
    student_id: studentId,
    term_id: termId,
    amount_paid: amount,
    new_balance: tuitionLedger.balance_cents
  };

  res.json(createSuccessResponse(req, paymentResult));
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`University Course Registration API running on port ${PORT}`);
});

module.exports = app;