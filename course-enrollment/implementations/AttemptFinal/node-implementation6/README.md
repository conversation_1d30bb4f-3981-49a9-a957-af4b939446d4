# University Course Registration & Enrollment API

A complete Node.js implementation of a University Course Registration & Enrollment API based on the provided Product Requirements Document (PRD).

## Features

- **Academic Term Management**: Create terms, open/close registration, conclude terms
- **Course Management**: Create, publish, and cancel courses with delivery modes
- **Enrollment System**: Student enrollment with automatic waitlist management
- **Financial Operations**: Tuition payment processing with ledger tracking
- **Role-Based Access Control**: Student, Professor, and Registrar roles with appropriate permissions
- **Business Rule Enforcement**: Credit limits, drop penalties, course capacity management
- **Automatic Waitlist Promotion**: FIFO promotion when seats become available
- **Audit Logging**: Complete audit trail of all operations

## Installation

1. Clone or extract the project
2. Install dependencies:
```bash
npm install
```

## Running the API

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

The API will start on port 3000 by default (or the PORT environment variable).

## Testing

Run the comprehensive test suite:
```bash
node test.js
```

This will test all major functionality including:
- Term lifecycle management
- Course creation and publishing
- Student enrollment and waitlist
- Payment processing
- Role-based access control
- Business rule enforcement

## API Endpoints

### Authentication
All requests must include these headers:
- `X-User-ID`: User's UUID
- `X-User-Role`: One of `STUDENT`, `PROFESSOR`, `REGISTRAR`

### Term Management
- `POST /terms` - Create academic term (Registrar only)
- `GET /terms/{termId}` - Get term details
- `PATCH /terms/{termId}:open-registration` - Open enrollment (Registrar only)
- `PATCH /terms/{termId}:close-registration` - Close enrollment (Registrar only)
- `PATCH /terms/{termId}:conclude` - Conclude term (Registrar only)

### Course Management
- `POST /terms/{termId}/courses` - Create course (Professor/Registrar)
- `GET /terms/{termId}/courses` - List courses (filtered by role)
- `GET /terms/{termId}/courses/{courseId}` - Get course details
- `PATCH /terms/{termId}/courses/{courseId}:publish` - Publish course
- `PATCH /terms/{termId}/courses/{courseId}:cancel` - Cancel course

### Enrollment & Waitlist
- `POST /terms/{termId}/courses/{courseId}/enrollments` - Enroll in course
- `GET /terms/{termId}/courses/{courseId}/enrollments` - List enrollments (Professor/Registrar)
- `GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}` - Get enrollment
- `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop` - Drop enrollment

### Financial Operations
- `POST /terms/{termId}/students/{studentId}:pay` - Record payment
- `GET /terms/{termId}/students/{studentId}/balance` - Get balance (bonus endpoint)

## Business Rules Implemented

1. **Term State Gating**: Operations are restricted based on term state
2. **Credit Load Limit**: Students limited to 18 credits per term
3. **Course Capacity**: Automatic waitlist when courses are full
4. **Professor Course Limit**: Maximum 5 courses per professor per term
5. **Drop Count & Penalty**: Maximum 3 drops per term with $50 penalty on 3rd drop
6. **Waitlist Promotion**: Automatic FIFO promotion when seats become available
7. **Role-Based Access**: Appropriate permissions for each user role
8. **Optimistic Locking**: Revision control for concurrent updates

## Data Models

### AcademicTerm
- States: PLANNING → ENROLLMENT_OPEN → ENROLLMENT_CLOSED → CONCLUDED

### Course  
- States: DRAFT → OPEN → IN_PROGRESS → COMPLETED/CANCELLED
- Delivery modes: IN_PERSON, ONLINE, HYBRID

### Enrollment
- States: ENROLLED, WAITLISTED, DROPPED, COMPLETED

### Ledgers
- **CourseSeatLedger**: Tracks available seats per course
- **StudentTuitionLedger**: Tracks outstanding tuition balance per student

## Error Handling

The API implements comprehensive error handling with standardized error responses:

```json
{
  "meta": {
    "api_request_id": "req_ABC123",
    "api_request_timestamp": "2025-01-01T00:00:00.000Z"
  },
  "response_type": "error",
  "data": {
    "error_id": "ERR_SPECIFIC_ERROR",
    "message": "Human readable error message"
  }
}
```

All error codes from the PRD are implemented with appropriate HTTP status codes.

## Example Usage

### 1. Create and Open a Term
```bash
# Create term (as Registrar)
curl -X POST http://localhost:3000/terms \
  -H "X-User-ID: your-registrar-id" \
  -H "X-User-Role: REGISTRAR" \
  -H "Content-Type: application/json" \
  -d '{"name": "Fall 2025"}'

# Open registration
curl -X PATCH http://localhost:3000/terms/{termId}:open-registration \
  -H "X-User-ID: your-registrar-id" \
  -H "X-User-Role: REGISTRAR" \
  -H "Content-Type: application/json" \
  -d '{"revision": 0}'
```

### 2. Create and Publish a Course
```bash
# Create course (as Professor)
curl -X POST http://localhost:3000/terms/{termId}/courses \
  -H "X-User-ID: your-professor-id" \
  -H "X-User-Role: PROFESSOR" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "CS101",
    "title": "Intro to Computer Science",
    "credits": 3,
    "capacity": 30,
    "delivery_mode": "IN_PERSON",
    "location": "Room 101"
  }'

# Publish course
curl -X PATCH http://localhost:3000/terms/{termId}/courses/{courseId}:publish \
  -H "X-User-ID: your-professor-id" \
  -H "X-User-Role: PROFESSOR"
```

### 3. Student Enrollment
```bash
# Enroll in course (as Student)
curl -X POST http://localhost:3000/terms/{termId}/courses/{courseId}/enrollments \
  -H "X-User-ID: your-student-id" \
  -H "X-User-Role: STUDENT"
```

### 4. Payment
```bash
# Pay tuition (as Student)
curl -X POST http://localhost:3000/terms/{termId}/students/{studentId}:pay \
  -H "X-User-ID: your-student-id" \
  -H "X-User-Role: STUDENT" \
  -H "Content-Type: application/json" \
  -d '{"amount": 30000}'  # $300 in cents
```

## Architecture

- **Express.js**: Web framework
- **In-memory storage**: Using Maps for data persistence
- **Joi**: Input validation
- **UUID**: Resource identifiers
- **Middleware**: Authentication, response formatting, error handling
- **Event-driven**: Automatic waitlist promotion using async processing

## Standards Compliance

- RESTful API design
- Standardized response formats per PRD
- Comprehensive input validation
- Proper HTTP status codes
- Role-based access control
- Business rule enforcement
- Audit logging

This implementation fully adheres to the provided PRD specifications and includes all required functionality, business rules, and error handling.