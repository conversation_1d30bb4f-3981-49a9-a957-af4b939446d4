const request = require('supertest');
const app = require('./src/app');

// Test data
const testUsers = {
  registrar: {
    id: '550e8400-e29b-41d4-a716-446655440001',
    role: 'REGISTRAR'
  },
  professor: {
    id: '550e8400-e29b-41d4-a716-446655440002',
    role: 'PROFESSOR'
  },
  student1: {
    id: '550e8400-e29b-41d4-a716-446655440003',
    role: 'STUDENT'
  },
  student2: {
    id: '550e8400-e29b-41d4-a716-446655440004',
    role: 'STUDENT'
  }
};

let termId;
let courseId;
let enrollmentId;

// Helper function to make requests with auth headers
function makeRequest(method, url, user = testUsers.registrar, data = null) {
  const req = request(app)[method](url)
    .set('X-User-ID', user.id)
    .set('X-User-Role', user.role);
  
  if (data) {
    req.send(data);
  }
  
  return req;
}

async function runTests() {
  console.log('🚀 Starting University Enrollment API Tests...\n');

  try {
    // Test 1: Create Term
    console.log('📚 Test 1: Creating academic term...');
    const termResponse = await makeRequest('post', '/terms', testUsers.registrar, {
      name: 'Fall 2025'
    });
    
    if (termResponse.status !== 201) {
      throw new Error(`Create term failed: ${termResponse.status} - ${JSON.stringify(termResponse.body)}`);
    }
    
    termId = termResponse.body.data.id;
    console.log(`✅ Term created successfully: ${termId}\n`);

    // Test 2: Open Registration
    console.log('🔓 Test 2: Opening registration...');
    const openResponse = await makeRequest('patch', `/terms/${termId}:open-registration`, testUsers.registrar, {
      revision: 0
    });
    
    if (openResponse.status !== 200) {
      throw new Error(`Open registration failed: ${openResponse.status} - ${JSON.stringify(openResponse.body)}`);
    }
    console.log('✅ Registration opened successfully\n');

    // Test 3: Create Course
    console.log('📖 Test 3: Creating course...');
    const courseResponse = await makeRequest('post', `/terms/${termId}/courses`, testUsers.professor, {
      code: 'CS101',
      title: 'Introduction to Computer Science',
      description: 'Basic programming concepts',
      credits: 3,
      capacity: 2,
      delivery_mode: 'IN_PERSON',
      location: 'Room 101'
    });
    
    if (courseResponse.status !== 201) {
      throw new Error(`Create course failed: ${courseResponse.status} - ${JSON.stringify(courseResponse.body)}`);
    }
    
    courseId = courseResponse.body.data.id;
    console.log(`✅ Course created successfully: ${courseId}\n`);

    // Test 4: Publish Course
    console.log('📢 Test 4: Publishing course...');
    const publishResponse = await makeRequest('patch', `/terms/${termId}/courses/${courseId}:publish`, testUsers.professor);
    
    if (publishResponse.status !== 200) {
      throw new Error(`Publish course failed: ${publishResponse.status} - ${JSON.stringify(publishResponse.body)}`);
    }
    console.log('✅ Course published successfully\n');

    // Test 5: Student Enrollment
    console.log('🎓 Test 5: Student enrollment...');
    const enrollResponse = await makeRequest('post', `/terms/${termId}/courses/${courseId}/enrollments`, testUsers.student1);
    
    if (enrollResponse.status !== 201) {
      throw new Error(`Student enrollment failed: ${enrollResponse.status} - ${JSON.stringify(enrollResponse.body)}`);
    }
    
    enrollmentId = enrollResponse.body.data.id;
    console.log(`✅ Student enrolled successfully: ${enrollmentId}\n`);

    // Test 6: Second Student Enrollment (should fill last seat)
    console.log('🎓 Test 6: Second student enrollment...');
    const enroll2Response = await makeRequest('post', `/terms/${termId}/courses/${courseId}/enrollments`, testUsers.student2);
    
    if (enroll2Response.status !== 201) {
      throw new Error(`Second student enrollment failed: ${enroll2Response.status} - ${JSON.stringify(enroll2Response.body)}`);
    }
    console.log('✅ Second student enrolled successfully\n');

    // Test 7: Third Student Enrollment (should be waitlisted)
    console.log('⏳ Test 7: Third student enrollment (waitlist)...');
    const student3 = {
      id: '550e8400-e29b-41d4-a716-446655440005',
      role: 'STUDENT'
    };
    
    const enroll3Response = await makeRequest('post', `/terms/${termId}/courses/${courseId}/enrollments`, student3);
    
    if (enroll3Response.status !== 201) {
      throw new Error(`Third student enrollment failed: ${enroll3Response.status} - ${JSON.stringify(enroll3Response.body)}`);
    }
    
    if (enroll3Response.body.data.state !== 'WAITLISTED') {
      throw new Error('Third student should be waitlisted');
    }
    console.log('✅ Third student waitlisted successfully\n');

    // Test 8: View Course Details (Student)
    console.log('👀 Test 8: Student viewing course details...');
    const courseViewResponse = await makeRequest('get', `/terms/${termId}/courses/${courseId}`, testUsers.student1);
    
    if (courseViewResponse.status !== 200) {
      throw new Error(`Course view failed: ${courseViewResponse.status} - ${JSON.stringify(courseViewResponse.body)}`);
    }
    
    if (!courseViewResponse.body.data.is_enrolled) {
      throw new Error('Student should see they are enrolled');
    }
    console.log('✅ Student can view course details correctly\n');

    // Test 9: Payment
    console.log('💰 Test 9: Student payment...');
    const paymentResponse = await makeRequest('post', `/terms/${termId}/students/${testUsers.student1.id}:pay`, testUsers.student1, {
      amount: 30000 // $300 (3 credits * $100)
    });
    
    if (paymentResponse.status !== 200) {
      throw new Error(`Payment failed: ${paymentResponse.status} - ${JSON.stringify(paymentResponse.body)}`);
    }
    
    if (paymentResponse.body.data.new_balance !== 0) {
      throw new Error('Student balance should be 0 after payment');
    }
    console.log('✅ Payment processed successfully\n');

    // Test 10: Drop Enrollment (should promote waitlisted student)
    console.log('📤 Test 10: Student dropping enrollment...');
    const dropResponse = await makeRequest('patch', `/terms/${termId}/courses/${courseId}/enrollments/${enrollmentId}:drop`, testUsers.student1);
    
    if (dropResponse.status !== 200) {
      throw new Error(`Drop enrollment failed: ${dropResponse.status} - ${JSON.stringify(dropResponse.body)}`);
    }
    console.log('✅ Student dropped successfully\n');

    // Give waitlist promotion time to process
    await new Promise(resolve => setTimeout(resolve, 100));

    // Test 11: Verify Waitlist Promotion
    console.log('🔄 Test 11: Verifying waitlist promotion...');
    const enrollmentsResponse = await makeRequest('get', `/terms/${termId}/courses/${courseId}/enrollments`, testUsers.professor);
    
    if (enrollmentsResponse.status !== 200) {
      throw new Error(`Get enrollments failed: ${enrollmentsResponse.status} - ${JSON.stringify(enrollmentsResponse.body)}`);
    }
    
    const enrollments = enrollmentsResponse.body.data;
    const promotedStudent = enrollments.find(e => e.student_id === student3.id);
    
    if (!promotedStudent || promotedStudent.state !== 'ENROLLED') {
      throw new Error('Waitlisted student should have been promoted to enrolled');
    }
    console.log('✅ Waitlist promotion worked correctly\n');

    // Test 12: Close Registration
    console.log('🔒 Test 12: Closing registration...');
    const closeResponse = await makeRequest('patch', `/terms/${termId}:close-registration`, testUsers.registrar, {
      revision: 1
    });
    
    if (closeResponse.status !== 200) {
      throw new Error(`Close registration failed: ${closeResponse.status} - ${JSON.stringify(closeResponse.body)}`);
    }
    console.log('✅ Registration closed successfully\n');

    // Test 13: Conclude Term
    console.log('🎯 Test 13: Concluding term...');
    const concludeResponse = await makeRequest('patch', `/terms/${termId}:conclude`, testUsers.registrar, {
      revision: 2
    });
    
    if (concludeResponse.status !== 200) {
      throw new Error(`Conclude term failed: ${concludeResponse.status} - ${JSON.stringify(concludeResponse.body)}`);
    }
    console.log('✅ Term concluded successfully\n');

    // Test 14: Error Handling - Try to enroll in concluded term
    console.log('❌ Test 14: Testing error handling...');
    const errorResponse = await makeRequest('post', `/terms/${termId}/courses/${courseId}/enrollments`, testUsers.student1);
    
    if (errorResponse.status !== 409) {
      throw new Error('Should not be able to enroll in concluded term');
    }
    console.log('✅ Error handling works correctly\n');

    console.log('🎉 All tests passed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- Term lifecycle management ✅');
    console.log('- Course creation and publishing ✅');
    console.log('- Student enrollment and waitlist ✅');
    console.log('- Automatic waitlist promotion ✅');
    console.log('- Payment processing ✅');
    console.log('- Role-based access control ✅');
    console.log('- Business rule enforcement ✅');
    console.log('- Error handling ✅');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };