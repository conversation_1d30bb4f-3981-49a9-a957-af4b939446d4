#!/bin/bash

echo "🚀 Setting up University Enrollment API..."

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Check if installation was successful
if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo ""
echo "🎯 Setup complete! You can now:"
echo "  - Run in development mode: npm run dev"
echo "  - Run in production mode: npm start"
echo "  - Run tests: node test.js"
echo ""
echo "📖 API will be available at http://localhost:3000"
echo "📚 See README.md for detailed usage instructions"