const express = require('express');
const { 
  StudentTuitionLedger,
  USER_ROLES, 
  db 
} = require('../models');
const { schemas, validate } = require('../utils/validation');

const router = express.Router({ mergeParams: true });

// POST /terms/{termId}/students/{studentId}:pay - Record tuition payment
router.post('/:studentId\\:pay',
  validate(schemas.termId, 'params'),
  validate(schemas.studentId, 'params'),
  validate(schemas.payment),
  (req, res) => {
    const { termId, studentId } = req.params;
    const { amount } = req.body;
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Check permissions
    if (req.user.role === USER_ROLES.STUDENT && studentId !== req.user.id) {
      return res.error(403, 'ERR_FORBIDDEN', 'Students can only pay their own tuition');
    } else if (req.user.role === USER_ROLES.PROFESSOR) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Professors cannot record payments');
    }
    
    // Validate payment amount
    if (!Number.isInteger(amount) || amount <= 0) {
      return res.error(422, 'ERR_INVALID_PAYMENT_AMOUNT', 'Payment amount must be a positive integer (in cents)');
    }
    
    // Get or create student tuition ledger
    let tuitionLedger = db.getStudentTuitionLedger(termId, studentId);
    if (!tuitionLedger) {
      tuitionLedger = new StudentTuitionLedger(termId, studentId);
    }
    
    // Check for overpayment
    if (amount > tuitionLedger.balance) {
      return res.error(422, 'ERR_OVERPAY_NOT_ALLOWED', 'Payment amount exceeds outstanding balance');
    }
    
    // Process payment
    const oldBalance = tuitionLedger.balance;
    tuitionLedger.debit(amount);
    const newBalance = tuitionLedger.balance;
    
    // Save updated ledger
    db.setStudentTuitionLedger(termId, studentId, tuitionLedger);
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'PAYMENT_RECORDED',
      user_id: req.user.id,
      resource_type: 'payment',
      resource_id: `${termId}-${studentId}-${Date.now()}`,
      details: { 
        term_id: termId,
        student_id: studentId,
        amount: amount,
        old_balance: oldBalance,
        new_balance: newBalance
      }
    });
    
    // Check if fully paid for audit
    if (newBalance === 0) {
      db.addAuditEntry({
        action: 'TUITION_FULLY_PAID',
        user_id: req.user.id,
        resource_type: 'ledger',
        resource_id: `${termId}-${studentId}`,
        details: { 
          term_id: termId,
          student_id: studentId
        }
      });
    }
    
    // Return payment confirmation
    res.json({
      student_id: studentId,
      term_id: termId,
      amount_paid: amount,
      previous_balance: oldBalance,
      new_balance: newBalance,
      payment_timestamp: new Date().toISOString()
    });
  }
);

// GET /terms/{termId}/students/{studentId}/balance - Get student's tuition balance (bonus endpoint for convenience)
router.get('/:studentId/balance',
  validate(schemas.termId, 'params'),
  validate(schemas.studentId, 'params'),
  (req, res) => {
    const { termId, studentId } = req.params;
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Check permissions
    if (req.user.role === USER_ROLES.STUDENT && studentId !== req.user.id) {
      return res.error(403, 'ERR_FORBIDDEN', 'Students can only view their own balance');
    } else if (req.user.role === USER_ROLES.PROFESSOR) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Professors cannot view student balances');
    }
    
    // Get student tuition ledger
    const tuitionLedger = db.getStudentTuitionLedger(termId, studentId);
    const balance = tuitionLedger ? tuitionLedger.balance : 0;
    
    res.json({
      student_id: studentId,
      term_id: termId,
      balance: balance,
      balance_dollars: (balance / 100).toFixed(2)
    });
  }
);

module.exports = router;