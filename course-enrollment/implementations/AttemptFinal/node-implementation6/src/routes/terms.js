const express = require('express');
const { 
  AcademicTerm, 
  TERM_STATES, 
  COURSE_STATES,
  ENROLLMENT_STATES,
  USER_ROLES, 
  db 
} = require('../models');
const { schemas, validate, validateTermStateTransition } = require('../utils/validation');

const router = express.Router();

// POST /terms - Create a new academic term
router.post('/', 
  validate(schemas.createTerm),
  (req, res) => {
    const { name } = req.body;
    
    // Only Registrar can create terms
    if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Only Registrar can create terms');
    }
    
    // Check for duplicate term name
    const existingTerm = db.findTermByName(name);
    if (existingTerm) {
      return res.error(409, 'ERR_TERM_NAME_NOT_UNIQUE', 'A term with this name already exists');
    }
    
    // Create new term
    const term = new AcademicTerm(name, req.user.id);
    db.save('terms', term);
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'TERM_CREATED',
      user_id: req.user.id,
      resource_type: 'term',
      resource_id: term.id,
      details: { name }
    });
    
    res.status(201).json(term);
  }
);

// GET /terms/{termId} - Retrieve details of an academic term
router.get('/:termId',
  validate(schemas.termId, 'params'),
  (req, res) => {
    const { termId } = req.params;
    
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    res.json(term);
  }
);

// PATCH /terms/{termId}:open-registration - Open student registration
router.patch('/:termId\\:open-registration',
  validate(schemas.termId, 'params'),
  validate(schemas.termRevision),
  (req, res) => {
    const { termId } = req.params;
    const { revision } = req.body;
    
    // Only Registrar can open registration
    if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Only Registrar can open registration');
    }
    
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Check revision for optimistic locking
    if (term.revision !== revision) {
      return res.error(409, 'ERR_REV_CONFLICT', 'Term has been modified by another user');
    }
    
    // Validate state transition
    try {
      validateTermStateTransition(term.state, TERM_STATES.ENROLLMENT_OPEN);
    } catch (error) {
      return res.error(409, 'ERR_TERM_NOT_ACTIVE', error.message);
    }
    
    // Update term state
    term.state = TERM_STATES.ENROLLMENT_OPEN;
    term.revision++;
    db.save('terms', term);
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'TERM_REGISTRATION_OPENED',
      user_id: req.user.id,
      resource_type: 'term',
      resource_id: term.id
    });
    
    res.json(term);
  }
);

// PATCH /terms/{termId}:close-registration - Close enrollment period
router.patch('/:termId\\:close-registration',
  validate(schemas.termId, 'params'),
  validate(schemas.termRevision),
  (req, res) => {
    const { termId } = req.params;
    const { revision } = req.body;
    
    // Only Registrar can close registration
    if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Only Registrar can close registration');
    }
    
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Check revision for optimistic locking
    if (term.revision !== revision) {
      return res.error(409, 'ERR_REV_CONFLICT', 'Term has been modified by another user');
    }
    
    // Validate state transition
    try {
      validateTermStateTransition(term.state, TERM_STATES.ENROLLMENT_CLOSED);
    } catch (error) {
      return res.error(409, 'ERR_TERM_NOT_ACTIVE', error.message);
    }
    
    // Update term state
    term.state = TERM_STATES.ENROLLMENT_CLOSED;
    term.revision++;
    
    // Transition all OPEN courses to IN_PROGRESS
    const courses = db.findCoursesByTerm(termId);
    courses.forEach(course => {
      if (course.state === COURSE_STATES.OPEN) {
        course.state = COURSE_STATES.IN_PROGRESS;
        course.revision++;
        db.save('courses', course);
        
        db.addAuditEntry({
          action: 'COURSE_STATE_CHANGED',
          user_id: req.user.id,
          resource_type: 'course',
          resource_id: course.id,
          details: { from: COURSE_STATES.OPEN, to: COURSE_STATES.IN_PROGRESS }
        });
      }
    });
    
    db.save('terms', term);
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'TERM_REGISTRATION_CLOSED',
      user_id: req.user.id,
      resource_type: 'term',
      resource_id: term.id
    });
    
    res.json(term);
  }
);

// PATCH /terms/{termId}:conclude - Conclude the term
router.patch('/:termId\\:conclude',
  validate(schemas.termId, 'params'),
  validate(schemas.termRevision),
  (req, res) => {
    const { termId } = req.params;
    const { revision } = req.body;
    
    // Only Registrar can conclude term
    if (req.user.role !== USER_ROLES.REGISTRAR) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Only Registrar can conclude term');
    }
    
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Check revision for optimistic locking
    if (term.revision !== revision) {
      return res.error(409, 'ERR_REV_CONFLICT', 'Term has been modified by another user');
    }
    
    // Validate state transition
    try {
      validateTermStateTransition(term.state, TERM_STATES.CONCLUDED);
    } catch (error) {
      return res.error(409, 'ERR_TERM_NOT_ACTIVE', error.message);
    }
    
    // Update term state
    term.state = TERM_STATES.CONCLUDED;
    term.revision++;
    
    // Transition all IN_PROGRESS courses to COMPLETED
    const courses = db.findCoursesByTerm(termId);
    courses.forEach(course => {
      if (course.state === COURSE_STATES.IN_PROGRESS) {
        course.state = COURSE_STATES.COMPLETED;
        course.revision++;
        db.save('courses', course);
        
        db.addAuditEntry({
          action: 'COURSE_STATE_CHANGED',
          user_id: req.user.id,
          resource_type: 'course',
          resource_id: course.id,
          details: { from: COURSE_STATES.IN_PROGRESS, to: COURSE_STATES.COMPLETED }
        });
      }
    });
    
    // Finalize all enrollments
    const allEnrollments = db.findAll('enrollments');
    const termCourseIds = courses.map(c => c.id);
    
    allEnrollments.forEach(enrollment => {
      if (termCourseIds.includes(enrollment.course_id)) {
        if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
          enrollment.state = ENROLLMENT_STATES.COMPLETED;
        } else if (enrollment.state === ENROLLMENT_STATES.WAITLISTED) {
          enrollment.state = ENROLLMENT_STATES.DROPPED;
        }
        enrollment.revision++;
        db.save('enrollments', enrollment);
      }
    });
    
    db.save('terms', term);
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'TERM_CONCLUDED',
      user_id: req.user.id,
      resource_type: 'term',
      resource_id: term.id
    });
    
    res.json(term);
  }
);

module.exports = router;