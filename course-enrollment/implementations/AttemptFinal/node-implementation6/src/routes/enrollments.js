const express = require('express');
const { 
  Enrollment, 
  StudentTuitionLedger,
  TERM_STATES, 
  COURSE_STATES,
  ENROLLMENT_STATES,
  USER_ROLES, 
  CONSTANTS,
  db 
} = require('../models');
const { schemas, validate, validateEnrollmentStateTransition } = require('../utils/validation');

const router = express.Router({ mergeParams: true });

// Helper function to promote next student from waitlist
function promoteFromWaitlist(courseId, termId) {
  const waitlistedEnrollments = db.findEnrollmentsByCourse(courseId)
    .filter(e => e.state === ENROLLMENT_STATES.WAITLISTED)
    .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  
  if (waitlistedEnrollments.length === 0) {
    return; // No one on waitlist
  }
  
  const seatLedger = db.getCourseSeatLedger(courseId);
  if (!seatLedger || seatLedger.seats_available <= 0) {
    return; // No seats available
  }
  
  const nextStudent = waitlistedEnrollments[0];
  const course = db.findById('courses', courseId);
  
  // Promote student
  nextStudent.state = ENROLLMENT_STATES.ENROLLED;
  nextStudent.revision++;
  db.save('enrollments', nextStudent);
  
  // Update seat ledger
  seatLedger.debit(1);
  db.setCourseSeatLedger(courseId, seatLedger);
  
  // Charge tuition
  let tuitionLedger = db.getStudentTuitionLedger(termId, nextStudent.student_id);
  if (!tuitionLedger) {
    tuitionLedger = new StudentTuitionLedger(termId, nextStudent.student_id);
  }
  const tuitionCost = course.credits * CONSTANTS.COST_PER_CREDIT;
  tuitionLedger.credit(tuitionCost);
  db.setStudentTuitionLedger(termId, nextStudent.student_id, tuitionLedger);
  
  // Add audit log entry
  db.addAuditEntry({
    action: 'WAITLIST_PROMOTED',
    user_id: 'SYSTEM',
    resource_type: 'enrollment',
    resource_id: nextStudent.id,
    details: { course_id: courseId, promoted_student_id: nextStudent.student_id }
  });
  
  // Recursively promote more if there are more seats and waitlisted students
  if (seatLedger.seats_available > 0) {
    promoteFromWaitlist(courseId, termId);
  }
}

// Helper function to calculate student's current credit load
function calculateStudentCredits(studentId, termId, excludeCourseId = null) {
  const enrollments = db.findEnrollmentsByStudent(studentId, termId)
    .filter(e => e.state === ENROLLMENT_STATES.ENROLLED);
  
  if (excludeCourseId) {
    enrollments.filter(e => e.course_id !== excludeCourseId);
  }
  
  let totalCredits = 0;
  enrollments.forEach(enrollment => {
    const course = db.findById('courses', enrollment.course_id);
    if (course) {
      totalCredits += course.credits;
    }
  });
  
  return totalCredits;
}

// Helper function to count student drops in term
function countStudentDrops(studentId, termId) {
  const enrollments = db.findEnrollmentsByStudent(studentId, termId);
  return enrollments.filter(e => e.state === ENROLLMENT_STATES.DROPPED).length;
}

// POST /terms/{termId}/courses/{courseId}/enrollments - Enroll in course
router.post('/',
  validate(schemas.termId, 'params'),
  validate(schemas.courseId, 'params'),
  validate(schemas.createEnrollment),
  (req, res) => {
    const { termId, courseId } = req.params;
    const { student_id } = req.body;
    
    // Check if term exists and is open for enrollment
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
      return res.error(409, 'ERR_REGISTRATION_CLOSED', 'Term is not open for enrollment');
    }
    
    // Check if course exists and is open for enrollment
    const course = db.findById('courses', courseId);
    if (!course || course.term_id !== termId) {
      return res.error(404, 'ERR_COURSE_NOT_FOUND', 'Course not found');
    }
    
    if (course.state !== COURSE_STATES.OPEN) {
      return res.error(409, 'ERR_COURSE_WRONG_STATE', 'Course is not open for enrollment');
    }
    
    // Determine target student ID based on user role
    let targetStudentId;
    if (req.user.role === USER_ROLES.STUDENT) {
      // Students can only enroll themselves
      if (student_id && student_id !== req.user.id) {
        return res.error(403, 'ERR_FORBIDDEN', 'Students can only enroll themselves');
      }
      targetStudentId = req.user.id;
    } else if (req.user.role === USER_ROLES.REGISTRAR) {
      // Registrar must specify student_id
      if (!student_id) {
        return res.error(400, 'ERR_MISSING_REQUIRED_FIELD', 'student_id is required for Registrar');
      }
      targetStudentId = student_id;
    } else {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Only students and registrars can create enrollments');
    }
    
    // Check for duplicate enrollment
    const existingEnrollment = db.findEnrollment(courseId, targetStudentId);
    if (existingEnrollment) {
      return res.error(409, 'ERR_ALREADY_ENROLLED', 'Student is already enrolled or waitlisted in this course');
    }
    
    // Check credit limit (only for students, not for registrar overrides)
    if (req.user.role === USER_ROLES.STUDENT) {
      const currentCredits = calculateStudentCredits(targetStudentId, termId);
      if (currentCredits + course.credits > CONSTANTS.MAX_CREDITS_PER_TERM) {
        return res.error(409, 'ERR_CREDIT_LIMIT_EXCEEDED', 'Enrollment would exceed credit limit');
      }
    }
    
    // Check seat availability
    const seatLedger = db.getCourseSeatLedger(courseId);
    let enrollmentState;
    
    if (seatLedger && seatLedger.seats_available > 0) {
      enrollmentState = ENROLLMENT_STATES.ENROLLED;
    } else {
      enrollmentState = ENROLLMENT_STATES.WAITLISTED;
    }
    
    // Create enrollment
    const enrollment = new Enrollment(courseId, targetStudentId, enrollmentState);
    db.save('enrollments', enrollment);
    
    // Handle seat and tuition ledgers for enrolled students
    if (enrollmentState === ENROLLMENT_STATES.ENROLLED) {
      // Debit seat ledger
      seatLedger.debit(1);
      db.setCourseSeatLedger(courseId, seatLedger);
      
      // Credit tuition ledger
      let tuitionLedger = db.getStudentTuitionLedger(termId, targetStudentId);
      if (!tuitionLedger) {
        tuitionLedger = new StudentTuitionLedger(termId, targetStudentId);
      }
      const tuitionCost = course.credits * CONSTANTS.COST_PER_CREDIT;
      tuitionLedger.credit(tuitionCost);
      db.setStudentTuitionLedger(termId, targetStudentId, tuitionLedger);
    }
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'ENROLLMENT_CREATED',
      user_id: req.user.id,
      resource_type: 'enrollment',
      resource_id: enrollment.id,
      details: { 
        course_id: courseId, 
        student_id: targetStudentId, 
        state: enrollmentState 
      }
    });
    
    res.status(201).json(enrollment);
  }
);

// GET /terms/{termId}/courses/{courseId}/enrollments - List enrollments for course
router.get('/',
  validate(schemas.termId, 'params'),
  validate(schemas.courseId, 'params'),
  validate(schemas.pagination, 'query'),
  (req, res) => {
    const { termId, courseId } = req.params;
    const { limit, offset } = req.query;
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Check if course exists
    const course = db.findById('courses', courseId);
    if (!course || course.term_id !== termId) {
      return res.error(404, 'ERR_COURSE_NOT_FOUND', 'Course not found');
    }
    
    // Only professor of the course or registrar can list enrollments
    if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.error(403, 'ERR_NOT_INSTRUCTOR', 'Professor can only view enrollments for their own courses');
    } else if (req.user.role === USER_ROLES.STUDENT) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Students cannot list course enrollments');
    }
    
    let enrollments = db.findEnrollmentsByCourse(courseId);
    
    // Sort by creation time (waitlist order)
    enrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    
    // Apply pagination
    const total = enrollments.length;
    const paginatedEnrollments = enrollments.slice(offset, offset + limit);
    
    res.json(paginatedEnrollments);
  }
);

// GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId} - Get specific enrollment
router.get('/:enrollmentId',
  validate(schemas.termId, 'params'),
  validate(schemas.courseId, 'params'),
  validate(schemas.enrollmentId, 'params'),
  (req, res) => {
    const { termId, courseId, enrollmentId } = req.params;
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Check if course exists
    const course = db.findById('courses', courseId);
    if (!course || course.term_id !== termId) {
      return res.error(404, 'ERR_COURSE_NOT_FOUND', 'Course not found');
    }
    
    // Check if enrollment exists
    const enrollment = db.findById('enrollments', enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.error(404, 'ERR_ENROLLMENT_NOT_FOUND', 'Enrollment not found');
    }
    
    // Check permissions
    if (req.user.role === USER_ROLES.STUDENT && enrollment.student_id !== req.user.id) {
      return res.error(403, 'ERR_FORBIDDEN', 'Students can only view their own enrollments');
    } else if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.error(403, 'ERR_NOT_INSTRUCTOR', 'Professor can only view enrollments for their own courses');
    }
    
    res.json(enrollment);
  }
);

// PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop - Drop enrollment
router.patch('/:enrollmentId\\:drop',
  validate(schemas.termId, 'params'),
  validate(schemas.courseId, 'params'),
  validate(schemas.enrollmentId, 'params'),
  (req, res) => {
    const { termId, courseId, enrollmentId } = req.params;
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Term must allow drops (not concluded)
    if (term.state === TERM_STATES.CONCLUDED) {
      return res.error(409, 'ERR_TERM_NOT_ACTIVE', 'Cannot drop from concluded term');
    }
    
    // Check if course exists
    const course = db.findById('courses', courseId);
    if (!course || course.term_id !== termId) {
      return res.error(404, 'ERR_COURSE_NOT_FOUND', 'Course not found');
    }
    
    // Check if enrollment exists
    const enrollment = db.findById('enrollments', enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.error(404, 'ERR_ENROLLMENT_NOT_FOUND', 'Enrollment not found');
    }
    
    // Check permissions
    if (req.user.role === USER_ROLES.STUDENT && enrollment.student_id !== req.user.id) {
      return res.error(403, 'ERR_FORBIDDEN', 'Students can only drop their own enrollments');
    } else if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.error(403, 'ERR_NOT_INSTRUCTOR', 'Professor can only drop enrollments from their own courses');
    }
    
    // Validate enrollment state
    if (![ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED].includes(enrollment.state)) {
      return res.error(409, 'ERR_ENROLLMENT_WRONG_STATE', 'Cannot drop enrollment in current state');
    }
    
    // Check drop limit for student-initiated drops
    if (req.user.role === USER_ROLES.STUDENT) {
      const currentDrops = countStudentDrops(enrollment.student_id, termId);
      if (currentDrops >= CONSTANTS.MAX_DROP_COUNT_PER_TERM) {
        return res.error(409, 'ERR_TOO_MANY_DROPS', 'Student has reached maximum drop limit');
      }
    }
    
    const wasEnrolled = enrollment.state === ENROLLMENT_STATES.ENROLLED;
    
    // Update enrollment state
    enrollment.state = ENROLLMENT_STATES.DROPPED;
    enrollment.revision++;
    db.save('enrollments', enrollment);
    
    // Handle ledger updates for enrolled students
    if (wasEnrolled) {
      // Credit seat ledger (free up seat)
      const seatLedger = db.getCourseSeatLedger(courseId);
      if (seatLedger) {
        seatLedger.credit(1);
        db.setCourseSeatLedger(courseId, seatLedger);
      }
      
      // Debit tuition ledger (refund)
      const tuitionLedger = db.getStudentTuitionLedger(termId, enrollment.student_id);
      if (tuitionLedger) {
        const refundAmount = course.credits * CONSTANTS.COST_PER_CREDIT;
        tuitionLedger.debit(refundAmount);
        db.setStudentTuitionLedger(termId, enrollment.student_id, tuitionLedger);
      }
    }
    
    // Apply drop penalty if this is student's 3rd drop
    if (req.user.role === USER_ROLES.STUDENT) {
      const dropsAfterThis = countStudentDrops(enrollment.student_id, termId);
      if (dropsAfterThis === CONSTANTS.MAX_DROP_COUNT_PER_TERM) {
        let tuitionLedger = db.getStudentTuitionLedger(termId, enrollment.student_id);
        if (!tuitionLedger) {
          tuitionLedger = new StudentTuitionLedger(termId, enrollment.student_id);
        }
        tuitionLedger.credit(CONSTANTS.DROP_PENALTY_FEE);
        db.setStudentTuitionLedger(termId, enrollment.student_id, tuitionLedger);
        
        db.addAuditEntry({
          action: 'DROP_PENALTY_APPLIED',
          user_id: req.user.id,
          resource_type: 'enrollment',
          resource_id: enrollment.id,
          details: { penalty_amount: CONSTANTS.DROP_PENALTY_FEE }
        });
      }
    }
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'ENROLLMENT_DROPPED',
      user_id: req.user.id,
      resource_type: 'enrollment',
      resource_id: enrollment.id,
      details: { 
        course_id: courseId, 
        student_id: enrollment.student_id,
        was_enrolled: wasEnrolled
      }
    });
    
    // Promote from waitlist if a seat was freed
    if (wasEnrolled) {
      // Use setTimeout to handle promotion asynchronously
      setTimeout(() => {
        promoteFromWaitlist(courseId, termId);
      }, 0);
    }
    
    res.json(enrollment);
  }
);

module.exports = router;