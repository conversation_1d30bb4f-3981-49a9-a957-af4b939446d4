const express = require('express');
const { 
  Course, 
  CourseSeatLedger,
  TERM_STATES, 
  COURSE_STATES,
  ENROLLMENT_STATES,
  USER_ROLES, 
  CONSTANTS,
  db 
} = require('../models');
const { 
  schemas, 
  validate, 
  validateCourseDeliveryMode,
  validateCourseStateTransition 
} = require('../utils/validation');

const router = express.Router({ mergeParams: true });

// Helper function to check if user can view course based on role and course state
function canViewCourse(course, userRole, userId) {
  // Registrar can see all courses
  if (userRole === USER_ROLES.REGISTRAR) {
    return true;
  }
  
  // Professor can see their own courses regardless of state
  if (userRole === USER_ROLES.PROFESSOR && course.professor_id === userId) {
    return true;
  }
  
  // Students and professors can only see published courses (or later states)
  return [COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state);
}

// Helper function to filter course data based on user role
function filterCourseData(course, userRole, userId, enrollments = []) {
  const filteredCourse = { ...course };
  
  // Calculate derived fields
  const enrolledCount = enrollments.filter(e => e.state === ENROLLMENT_STATES.ENROLLED).length;
  const waitlistCount = enrollments.filter(e => e.state === ENROLLMENT_STATES.WAITLISTED).length;
  const availableSeats = course.capacity - enrolledCount;
  
  if (userRole === USER_ROLES.STUDENT) {
    // Students see limited information
    delete filteredCourse.revision;
    
    // Add contextual information for student's enrollment status
    const studentEnrollment = enrollments.find(e => e.student_id === userId && e.state !== ENROLLMENT_STATES.DROPPED);
    if (studentEnrollment) {
      filteredCourse.is_enrolled = studentEnrollment.state === ENROLLMENT_STATES.ENROLLED;
      filteredCourse.is_waitlisted = studentEnrollment.state === ENROLLMENT_STATES.WAITLISTED;
    }
    
    filteredCourse.available_seats = availableSeats;
    
  } else if (userRole === USER_ROLES.PROFESSOR && course.professor_id === userId) {
    // Professors see full details of their own courses
    filteredCourse.enrolled_count = enrolledCount;
    filteredCourse.waitlist_count = waitlistCount;
    filteredCourse.available_seats = availableSeats;
    filteredCourse.enrollments = enrollments;
    
  } else if (userRole === USER_ROLES.PROFESSOR) {
    // Professors see limited info of other courses
    delete filteredCourse.revision;
    filteredCourse.available_seats = availableSeats;
    
  } else if (userRole === USER_ROLES.REGISTRAR) {
    // Registrars see everything
    filteredCourse.enrolled_count = enrolledCount;
    filteredCourse.waitlist_count = waitlistCount;
    filteredCourse.available_seats = availableSeats;
    filteredCourse.enrollments = enrollments;
  }
  
  return filteredCourse;
}

// POST /terms/{termId}/courses - Create a new course
router.post('/',
  validate(schemas.termId, 'params'),
  validate(schemas.createCourse),
  (req, res) => {
    const { termId } = req.params;
    const courseData = req.body;
    
    // Only Professor and Registrar can create courses
    if (![USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR].includes(req.user.role)) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Only Professor and Registrar can create courses');
    }
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Handle professor_id based on caller role
    let professorId;
    if (req.user.role === USER_ROLES.PROFESSOR) {
      // Professor creating course for themselves
      if (courseData.professor_id && courseData.professor_id !== req.user.id) {
        return res.error(400, 'ERR_FIELD_CONFLICT', 'Professor cannot create course for another instructor');
      }
      professorId = req.user.id;
    } else {
      // Registrar creating course
      if (!courseData.professor_id) {
        return res.error(400, 'ERR_MISSING_REQUIRED_FIELD', 'professor_id is required when Registrar creates course');
      }
      professorId = courseData.professor_id;
    }
    
    // Check if course code is unique within the term
    const existingCourse = db.findCourseByCodeAndTerm(courseData.code, termId);
    if (existingCourse) {
      return res.error(409, 'ERR_COURSE_CODE_NOT_UNIQUE', 'Course code already exists in this term');
    }
    
    // Check professor course limit
    const professorCourses = db.findCoursesByProfessor(professorId, termId);
    if (professorCourses.length >= CONSTANTS.MAX_COURSES_PER_PROF) {
      return res.error(409, 'ERR_MAX_COURSES_REACHED', 'Professor has reached maximum course limit');
    }
    
    // Validate delivery mode requirements
    try {
      validateCourseDeliveryMode(courseData);
    } catch (error) {
      return res.error(400, 'ERR_CONDITIONAL_FIELD_REQUIRED', error.message);
    }
    
    // Create course
    const course = new Course(
      termId,
      courseData.code,
      courseData.title,
      courseData.description,
      courseData.credits,
      courseData.capacity,
      professorId,
      courseData.delivery_mode,
      courseData.location,
      courseData.online_link
    );
    
    db.save('courses', course);
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'COURSE_CREATED',
      user_id: req.user.id,
      resource_type: 'course',
      resource_id: course.id,
      details: { code: courseData.code, term_id: termId }
    });
    
    res.status(201).json(course);
  }
);

// GET /terms/{termId}/courses - List courses in the term
router.get('/',
  validate(schemas.termId, 'params'),
  validate(schemas.pagination, 'query'),
  validate(schemas.courseFilters, 'query'),
  (req, res) => {
    const { termId } = req.params;
    const { limit, offset } = req.query;
    const filters = req.query;
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    let courses = db.findCoursesByTerm(termId);
    
    // Apply filters
    if (filters.state) {
      courses = courses.filter(course => course.state === filters.state);
    }
    if (filters.professor_id) {
      courses = courses.filter(course => course.professor_id === filters.professor_id);
    }
    
    // Filter courses based on user role and visibility
    courses = courses.filter(course => canViewCourse(course, req.user.role, req.user.id));
    
    // Sort by creation time for stable pagination
    courses.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    
    // Apply pagination
    const total = courses.length;
    const paginatedCourses = courses.slice(offset, offset + limit);
    
    // Filter course data based on role
    const filteredCourses = paginatedCourses.map(course => {
      const enrollments = db.findEnrollmentsByCourse(course.id);
      return filterCourseData(course, req.user.role, req.user.id, enrollments);
    });
    
    res.json(filteredCourses);
  }
);

// GET /terms/{termId}/courses/{courseId} - Get specific course details
router.get('/:courseId',
  validate(schemas.termId, 'params'),
  validate(schemas.courseId, 'params'),
  (req, res) => {
    const { termId, courseId } = req.params;
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    const course = db.findById('courses', courseId);
    if (!course || course.term_id !== termId) {
      return res.error(404, 'ERR_COURSE_NOT_FOUND', 'Course not found');
    }
    
    // Check if user can view this course
    if (!canViewCourse(course, req.user.role, req.user.id)) {
      return res.error(404, 'ERR_COURSE_NOT_FOUND', 'Course not found');
    }
    
    const enrollments = db.findEnrollmentsByCourse(courseId);
    const filteredCourse = filterCourseData(course, req.user.role, req.user.id, enrollments);
    
    res.json(filteredCourse);
  }
);

// PATCH /terms/{termId}/courses/{courseId}:publish - Publish a draft course
router.patch('/:courseId\\:publish',
  validate(schemas.termId, 'params'),
  validate(schemas.courseId, 'params'),
  (req, res) => {
    const { termId, courseId } = req.params;
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    // Term must be open for enrollment to publish courses
    if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
      return res.error(409, 'ERR_TERM_NOT_ACTIVE', 'Term must be open for enrollment to publish courses');
    }
    
    const course = db.findById('courses', courseId);
    if (!course || course.term_id !== termId) {
      return res.error(404, 'ERR_COURSE_NOT_FOUND', 'Course not found');
    }
    
    // Check authorization
    if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.error(403, 'ERR_NOT_INSTRUCTOR', 'Professor can only publish their own courses');
    } else if (req.user.role === USER_ROLES.STUDENT) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Students cannot publish courses');
    }
    
    // Validate state transition
    try {
      validateCourseStateTransition(course.state, COURSE_STATES.OPEN);
    } catch (error) {
      return res.error(409, 'ERR_COURSE_WRONG_STATE', error.message);
    }
    
    // Update course state
    course.state = COURSE_STATES.OPEN;
    course.revision++;
    db.save('courses', course);
    
    // Initialize seat ledger
    const seatLedger = new CourseSeatLedger(courseId, course.capacity);
    db.setCourseSeatLedger(courseId, seatLedger);
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'COURSE_PUBLISHED',
      user_id: req.user.id,
      resource_type: 'course',
      resource_id: course.id
    });
    
    res.json(course);
  }
);

// PATCH /terms/{termId}/courses/{courseId}:cancel - Cancel a course
router.patch('/:courseId\\:cancel',
  validate(schemas.termId, 'params'),
  validate(schemas.courseId, 'params'),
  (req, res) => {
    const { termId, courseId } = req.params;
    
    // Check if term exists
    const term = db.findById('terms', termId);
    if (!term) {
      return res.error(404, 'ERR_TERM_NOT_FOUND', 'Term not found');
    }
    
    const course = db.findById('courses', courseId);
    if (!course || course.term_id !== termId) {
      return res.error(404, 'ERR_COURSE_NOT_FOUND', 'Course not found');
    }
    
    // Check authorization
    if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.error(403, 'ERR_NOT_INSTRUCTOR', 'Professor can only cancel their own courses');
    } else if (req.user.role === USER_ROLES.STUDENT) {
      return res.error(403, 'ERR_UNAUTHORIZED_ROLE', 'Students cannot cancel courses');
    }
    
    // Validate state transition
    try {
      validateCourseStateTransition(course.state, COURSE_STATES.CANCELLED);
    } catch (error) {
      return res.error(409, 'ERR_COURSE_WRONG_STATE', error.message);
    }
    
    // Update course state
    course.state = COURSE_STATES.CANCELLED;
    course.revision++;
    db.save('courses', course);
    
    // Drop all enrollments and handle refunds
    const enrollments = db.findEnrollmentsByCourse(courseId);
    enrollments.forEach(enrollment => {
      if ([ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED].includes(enrollment.state)) {
        // Handle tuition refund for enrolled students
        if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
          const tuitionLedger = db.getStudentTuitionLedger(termId, enrollment.student_id);
          if (tuitionLedger) {
            const refundAmount = course.credits * CONSTANTS.COST_PER_CREDIT;
            tuitionLedger.debit(refundAmount);
            db.setStudentTuitionLedger(termId, enrollment.student_id, tuitionLedger);
          }
        }
        
        enrollment.state = ENROLLMENT_STATES.DROPPED;
        enrollment.revision++;
        db.save('enrollments', enrollment);
        
        db.addAuditEntry({
          action: 'ENROLLMENT_DROPPED_COURSE_CANCELLED',
          user_id: req.user.id,
          resource_type: 'enrollment',
          resource_id: enrollment.id,
          details: { course_id: courseId }
        });
      }
    });
    
    // Add audit log entry
    db.addAuditEntry({
      action: 'COURSE_CANCELLED',
      user_id: req.user.id,
      resource_type: 'course',
      resource_id: course.id
    });
    
    res.json(course);
  }
);

module.exports = router;