const { generateRequestId } = require('./auth');

const responseFormatter = (req, res, next) => {
  // Generate request ID and timestamp for this request
  req.requestId = generateRequestId();
  req.requestTimestamp = new Date().toISOString();

  // Override res.json to format responses according to PRD
  const originalJson = res.json;
  
  res.json = function(data) {
    // If it's already formatted (error responses), pass through
    if (data && data.meta && data.response_type) {
      return originalJson.call(this, data);
    }

    // Format successful response according to PRD standard
    const formattedResponse = {
      meta: {
        api_request_id: req.requestId,
        api_request_timestamp: req.requestTimestamp
      },
      response_type: Array.isArray(data) ? 'array' : 'object',
      data: data || {}
    };

    return originalJson.call(this, formattedResponse);
  };

  // Helper method for formatted error responses
  res.error = function(statusCode, errorId, message) {
    return this.status(statusCode).json({
      meta: {
        api_request_id: req.requestId,
        api_request_timestamp: req.requestTimestamp
      },
      response_type: 'error',
      data: {
        error_id: errorId,
        message: message
      }
    });
  };

  next();
};

module.exports = {
  responseFormatter
};