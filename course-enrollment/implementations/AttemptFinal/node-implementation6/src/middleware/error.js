const { generateRequestId } = require('./auth');

const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // If response already sent, delegate to default Express error handler
  if (res.headersSent) {
    return next(err);
  }

  const requestId = req.requestId || generateRequestId();
  const timestamp = req.requestTimestamp || new Date().toISOString();

  // Default error response
  let statusCode = 500;
  let errorId = 'ERR_INTERNAL_SERVER_ERROR';
  let message = 'An internal server error occurred';

  // Handle specific error types
  if (err.name === 'ValidationError') {
    statusCode = 400;
    errorId = 'ERR_VALIDATION_FAILED';
    message = err.message;
  } else if (err.statusCode) {
    statusCode = err.statusCode;
    errorId = err.errorId || 'ERR_UNKNOWN';
    message = err.message;
  }

  res.status(statusCode).json({
    meta: {
      api_request_id: requestId,
      api_request_timestamp: timestamp
    },
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  });
};

module.exports = {
  errorHandler
};