const { USER_ROLES, isValidUUID } = require('../models');

const authMiddleware = (req, res, next) => {
  // Skip auth for health check
  if (req.path === '/health') {
    return next();
  }

  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  // Check if headers are present
  if (!userId || !userRole) {
    return res.status(400).json({
      meta: {
        api_request_id: generateRequestId(),
        api_request_timestamp: new Date().toISOString()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
        message: 'Missing or invalid user context headers (X-User-ID and X-User-Role are required)'
      }
    });
  }

  // Validate UUID format for user ID
  if (!isValidUUID(userId)) {
    return res.status(400).json({
      meta: {
        api_request_id: generateRequestId(),
        api_request_timestamp: new Date().toISOString()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_INVALID_ID_FORMAT',
        message: 'Invalid user ID format. Must be a valid UUID.'
      }
    });
  }

  // Validate user role enum
  if (!Object.values(USER_ROLES).includes(userRole)) {
    return res.status(400).json({
      meta: {
        api_request_id: generateRequestId(),
        api_request_timestamp: new Date().toISOString()
      },
      response_type: 'error',
      data: {
        error_id: 'ERR_INVALID_ENUM_VALUE',
        message: 'Invalid user role. Must be one of: STUDENT, PROFESSOR, REGISTRAR'
      }
    });
  }

  // Add user context to request object
  req.user = {
    id: userId,
    role: userRole
  };

  next();
};

function generateRequestId() {
  return 'req_' + Math.random().toString(36).substr(2, 15).toUpperCase();
}

module.exports = {
  authMiddleware,
  generateRequestId
};