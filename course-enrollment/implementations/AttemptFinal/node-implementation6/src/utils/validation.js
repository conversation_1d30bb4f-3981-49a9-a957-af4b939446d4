const Joi = require('joi');
const { 
  TERM_STATES, 
  COURSE_STATES, 
  ENROLLMENT_STATES, 
  USER_ROLES, 
  DELIVERY_MODES,
  isValidUUID 
} = require('../models');

// Custom UUID validator
const uuidValidator = Joi.string().custom((value, helpers) => {
  if (!isValidUUID(value)) {
    return helpers.error('any.invalid');
  }
  return value;
}, 'UUID validation');

// Course code validator (2-4 uppercase letters followed by 3 digits)
const courseCodeValidator = Joi.string().pattern(/^[A-Z]{2,4}\d{3}$/).required();

// Validation schemas
const schemas = {
  // Term schemas
  createTerm: Joi.object({
    name: Joi.string().min(1).max(100).required()
  }).options({ allowUnknown: false }),

  termId: uuidValidator.required(),

  termRevision: Joi.object({
    revision: Joi.number().integer().min(0).required()
  }).options({ allowUnknown: false }),

  // Course schemas
  createCourse: Joi.object({
    code: courseCodeValidator,
    title: Joi.string().min(1).max(100).required(),
    description: Joi.string().max(1000).optional(),
    credits: Joi.number().integer().min(1).max(5).required(),
    capacity: Joi.number().integer().min(1).max(500).required(),
    professor_id: uuidValidator.optional(),
    delivery_mode: Joi.string().valid(...Object.values(DELIVERY_MODES)).required(),
    location: Joi.string().min(1).optional(),
    online_link: Joi.string().uri().optional()
  }).options({ allowUnknown: false }),

  courseId: uuidValidator.required(),

  // Enrollment schemas
  createEnrollment: Joi.object({
    student_id: uuidValidator.optional()
  }).options({ allowUnknown: false }),

  enrollmentId: uuidValidator.required(),

  // Payment schemas
  payment: Joi.object({
    amount: Joi.number().integer().min(1).required()
  }).options({ allowUnknown: false }),

  studentId: uuidValidator.required(),

  // Query parameters
  pagination: Joi.object({
    limit: Joi.number().integer().min(1).max(1000).default(50),
    offset: Joi.number().integer().min(0).default(0)
  }),

  courseFilters: Joi.object({
    state: Joi.string().valid(...Object.values(COURSE_STATES)).optional(),
    professor_id: uuidValidator.optional()
  })
};

// Validation middleware factory
function validate(schema, source = 'body') {
  return (req, res, next) => {
    const data = source === 'params' ? req.params : 
                  source === 'query' ? req.query : req.body;
    
    const { error, value } = schema.validate(data);
    
    if (error) {
      const message = error.details.map(detail => detail.message).join(', ');
      return res.error(400, 'ERR_VALIDATION_FAILED', message);
    }
    
    // Replace the source data with validated/coerced values
    if (source === 'params') {
      req.params = value;
    } else if (source === 'query') {
      req.query = value;
    } else {
      req.body = value;
    }
    
    next();
  };
}

// Business rule validators
function validateCourseDeliveryMode(course) {
  const { delivery_mode, location, online_link } = course;
  
  switch (delivery_mode) {
    case DELIVERY_MODES.IN_PERSON:
      if (!location) {
        throw new Error('Location is required for IN_PERSON courses');
      }
      if (online_link) {
        throw new Error('Online link not allowed for IN_PERSON courses');
      }
      break;
      
    case DELIVERY_MODES.ONLINE:
      if (!online_link) {
        throw new Error('Online link is required for ONLINE courses');
      }
      if (location) {
        throw new Error('Location not allowed for ONLINE courses');
      }
      break;
      
    case DELIVERY_MODES.HYBRID:
      if (!location && !online_link) {
        throw new Error('At least one of location or online_link is required for HYBRID courses');
      }
      break;
  }
}

function validateTermStateTransition(currentState, newState) {
  const validTransitions = {
    [TERM_STATES.PLANNING]: [TERM_STATES.ENROLLMENT_OPEN],
    [TERM_STATES.ENROLLMENT_OPEN]: [TERM_STATES.ENROLLMENT_CLOSED],
    [TERM_STATES.ENROLLMENT_CLOSED]: [TERM_STATES.CONCLUDED],
    [TERM_STATES.CONCLUDED]: []
  };
  
  if (!validTransitions[currentState] || !validTransitions[currentState].includes(newState)) {
    throw new Error(`Invalid state transition from ${currentState} to ${newState}`);
  }
}

function validateCourseStateTransition(currentState, newState) {
  const validTransitions = {
    [COURSE_STATES.DRAFT]: [COURSE_STATES.OPEN, COURSE_STATES.CANCELLED],
    [COURSE_STATES.OPEN]: [COURSE_STATES.IN_PROGRESS, COURSE_STATES.CANCELLED],
    [COURSE_STATES.IN_PROGRESS]: [COURSE_STATES.COMPLETED, COURSE_STATES.CANCELLED],
    [COURSE_STATES.COMPLETED]: [],
    [COURSE_STATES.CANCELLED]: []
  };
  
  if (!validTransitions[currentState] || !validTransitions[currentState].includes(newState)) {
    throw new Error(`Invalid state transition from ${currentState} to ${newState}`);
  }
}

function validateEnrollmentStateTransition(currentState, newState) {
  const validTransitions = {
    [ENROLLMENT_STATES.ENROLLED]: [ENROLLMENT_STATES.DROPPED, ENROLLMENT_STATES.COMPLETED],
    [ENROLLMENT_STATES.WAITLISTED]: [ENROLLMENT_STATES.DROPPED, ENROLLMENT_STATES.ENROLLED],
    [ENROLLMENT_STATES.DROPPED]: [],
    [ENROLLMENT_STATES.COMPLETED]: []
  };
  
  if (!validTransitions[currentState] || !validTransitions[currentState].includes(newState)) {
    throw new Error(`Invalid state transition from ${currentState} to ${newState}`);
  }
}

module.exports = {
  schemas,
  validate,
  validateCourseDeliveryMode,
  validateTermStateTransition,
  validateCourseStateTransition,
  validateEnrollmentStateTransition
};