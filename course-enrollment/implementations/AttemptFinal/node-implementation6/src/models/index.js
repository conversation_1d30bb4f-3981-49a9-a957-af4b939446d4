const { v4: uuidv4 } = require('uuid');

// Constants from PRD
const CONSTANTS = {
  MAX_CREDITS_PER_TERM: 18,
  MAX_COURSES_PER_PROF: 5,
  MAX_DROP_COUNT_PER_TERM: 3,
  DROP_PENALTY_FEE: 5000, // in cents
  COST_PER_CREDIT: 10000, // in cents ($100.00)
};

// Enums
const TERM_STATES = {
  PLANNING: 'PLANNING',
  ENROLLMENT_OPEN: 'ENROLLMENT_OPEN', 
  ENROLLMENT_CLOSED: 'ENROLLMENT_CLOSED',
  CONCLUDED: 'CONCLUDED'
};

const COURSE_STATES = {
  DRAFT: 'DRAFT',
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

const ENROLLMENT_STATES = {
  ENROLLED: 'ENROLLED',
  WAITLISTED: 'WAITLISTED',
  DROPPED: 'DROPPED',
  COMPLETED: 'COMPLETED'
};

const USER_ROLES = {
  STUDENT: 'STUDENT',
  PROFESSOR: 'PROFESSOR',
  REGISTRAR: 'REGISTRAR'
};

const DELIVERY_MODES = {
  IN_PERSON: 'IN_PERSON',
  ONLINE: 'ONLINE',
  HYBRID: 'HYBRID'
};

// In-memory data storage
const database = {
  terms: new Map(),
  courses: new Map(),
  enrollments: new Map(),
  courseSeatLedgers: new Map(),
  studentTuitionLedgers: new Map(),
  users: new Map(), // For validation purposes
  auditLog: []
};

// Helper functions for UUID validation
function isValidUUID(uuid) {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidRegex.test(uuid);
}

// Data model classes
class AcademicTerm {
  constructor(name, createdBy) {
    this.id = uuidv4();
    this.name = name;
    this.state = TERM_STATES.PLANNING;
    this.created_by = createdBy;
    this.created_at = new Date().toISOString();
    this.revision = 0;
  }
}

class Course {
  constructor(termId, code, title, description, credits, capacity, professorId, deliveryMode, location, onlineLink) {
    this.id = uuidv4();
    this.term_id = termId;
    this.code = code;
    this.title = title;
    this.description = description || '';
    this.credits = credits;
    this.capacity = capacity;
    this.professor_id = professorId;
    this.delivery_mode = deliveryMode;
    this.location = location || null;
    this.online_link = onlineLink || null;
    this.state = COURSE_STATES.DRAFT;
    this.created_at = new Date().toISOString();
    this.revision = 0;
  }
}

class Enrollment {
  constructor(courseId, studentId, state = ENROLLMENT_STATES.ENROLLED) {
    this.id = uuidv4();
    this.course_id = courseId;
    this.student_id = studentId;
    this.state = state;
    this.created_at = new Date().toISOString();
    this.revision = 0;
  }
}

class CourseSeatLedger {
  constructor(courseId, capacity) {
    this.course_id = courseId;
    this.seats_available = capacity;
    this.capacity = capacity;
  }

  debit(amount = 1) {
    if (this.seats_available < amount) {
      throw new Error('Insufficient seats available');
    }
    this.seats_available -= amount;
  }

  credit(amount = 1) {
    if (this.seats_available + amount > this.capacity) {
      throw new Error('Cannot exceed course capacity');
    }
    this.seats_available += amount;
  }
}

class StudentTuitionLedger {
  constructor(termId, studentId) {
    this.term_id = termId;
    this.student_id = studentId;
    this.balance = 0; // in cents
  }

  credit(amount) {
    this.balance += amount;
  }

  debit(amount) {
    if (this.balance < amount) {
      throw new Error('Insufficient balance');
    }
    this.balance -= amount;
  }
}

// Database operations
const db = {
  // Generic operations
  save(collection, entity) {
    database[collection].set(entity.id, entity);
    return entity;
  },

  findById(collection, id) {
    return database[collection].get(id);
  },

  findAll(collection) {
    return Array.from(database[collection].values());
  },

  delete(collection, id) {
    return database[collection].delete(id);
  },

  // Term operations
  findTermByName(name) {
    return this.findAll('terms').find(term => term.name === name);
  },

  // Course operations
  findCoursesByTerm(termId) {
    return this.findAll('courses').filter(course => course.term_id === termId);
  },

  findCourseByCodeAndTerm(code, termId) {
    return this.findCoursesByTerm(termId).find(course => course.code === code);
  },

  findCoursesByProfessor(professorId, termId) {
    return this.findCoursesByTerm(termId).filter(course => course.professor_id === professorId);
  },

  // Enrollment operations
  findEnrollmentsByCourse(courseId) {
    return this.findAll('enrollments').filter(enrollment => enrollment.course_id === courseId);
  },

  findEnrollmentsByStudent(studentId, termId) {
    const courses = this.findCoursesByTerm(termId);
    const courseIds = courses.map(c => c.id);
    return this.findAll('enrollments').filter(enrollment => 
      courseIds.includes(enrollment.course_id) && enrollment.student_id === studentId
    );
  },

  findEnrollment(courseId, studentId) {
    return this.findAll('enrollments').find(enrollment => 
      enrollment.course_id === courseId && 
      enrollment.student_id === studentId &&
      enrollment.state !== ENROLLMENT_STATES.DROPPED
    );
  },

  // Ledger operations
  getCourseSeatLedger(courseId) {
    return database.courseSeatLedgers.get(courseId);
  },

  setCourseSeatLedger(courseId, ledger) {
    database.courseSeatLedgers.set(courseId, ledger);
  },

  getStudentTuitionLedger(termId, studentId) {
    const key = `${termId}-${studentId}`;
    return database.studentTuitionLedgers.get(key);
  },

  setStudentTuitionLedger(termId, studentId, ledger) {
    const key = `${termId}-${studentId}`;
    database.studentTuitionLedgers.set(key, ledger);
  },

  // Audit log
  addAuditEntry(entry) {
    database.auditLog.push({
      ...entry,
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  CONSTANTS,
  TERM_STATES,
  COURSE_STATES,
  ENROLLMENT_STATES,
  USER_ROLES,
  DELIVERY_MODES,
  AcademicTerm,
  Course,
  Enrollment,
  CourseSeatLedger,
  StudentTuitionLedger,
  db,
  database,
  isValidUUID
};