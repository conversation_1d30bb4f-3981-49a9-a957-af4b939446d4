const express = require('express');
const cors = require('cors');
const { authMiddleware } = require('./middleware/auth');
const { responseFormatter } = require('./middleware/response');
const { errorHandler } = require('./middleware/error');
const termRoutes = require('./routes/terms');
const courseRoutes = require('./routes/courses');
const enrollmentRoutes = require('./routes/enrollments');
const paymentRoutes = require('./routes/payments');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Custom middleware
app.use(authMiddleware);
app.use(responseFormatter);

// Routes
app.use('/terms', termRoutes);
app.use('/terms/:termId/courses', courseRoutes);
app.use('/terms/:termId/courses/:courseId/enrollments', enrollmentRoutes);
app.use('/terms/:termId/students', paymentRoutes);

// Error handling
app.use(errorHandler);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Start server
app.listen(PORT, () => {
  console.log(`University Enrollment API server running on port ${PORT}`);
});

module.exports = app;