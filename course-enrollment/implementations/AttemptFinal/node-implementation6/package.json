{"name": "university-enrollment-api", "version": "1.0.0", "description": "University Course Registration & Enrollment API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "uuid": "^9.0.0", "joi": "^17.11.0", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}, "keywords": ["university", "enrollment", "course", "api", "express"], "author": "<PERSON> Assistant", "license": "MIT"}