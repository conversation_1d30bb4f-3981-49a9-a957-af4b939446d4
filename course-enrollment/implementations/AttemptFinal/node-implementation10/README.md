# University Course Registration & Enrollment API

A comprehensive Node.js implementation of a university course registration and enrollment system based on the provided Product Requirements Document (PRD).

## Features

This API implements all features specified in the PRD:

### Core Functionality
- **Term Management**: Create, open, close, and conclude academic terms
- **Course Management**: Create, list, publish, and cancel courses with role-based access
- **Enrollment System**: Student enrollment with waitlist support and automatic promotion
- **Payment Processing**: Tuition payment tracking with ledger management
- **Role-Based Access Control**: Three user roles (Student, Professor, Registrar) with appropriate permissions

### Business Rules
- **Term State Progression**: PLANNING → ENROLLMENT_OPEN → ENROLLMENT_CLOSED → CONCLUDED
- **Course State Management**: DRAFT → OPEN → IN_PROGRESS → COMPLETED/CANCELLED
- **Credit Limits**: 18 credits maximum per student per term
- **Professor <PERSON>its**: 5 courses maximum per professor per term
- **Drop Limits**: 3 drops maximum per student per term with $50 penalty on 3rd drop
- **Waitlist Management**: Automatic promotion when seats become available
- **Tuition Calculation**: $100 per credit hour with automatic charging and refunds

### Data Models
- **AcademicTerm**: Container for all term-specific data
- **Course**: Individual classes with capacity, delivery mode, and instructor
- **Enrollment**: Student participation in courses (enrolled/waitlisted/dropped/completed)
- **Ledgers**: Real-time tracking of seat availability and tuition balances

## Quick Start

### Prerequisites
- Node.js 18.0.0 or higher
- npm

### Installation
```bash
npm install
```

### Running the Server
```bash
npm start
# or for development with auto-reload:
npm run dev
```

The server will start on port 3000 by default. Health check available at `http://localhost:3000/health`.

### Running Tests
```bash
node test.js
```

## API Endpoints

### Term Management
- `POST /terms` - Create a new academic term (Registrar only)
- `GET /terms/{termId}` - Get term details
- `PATCH /terms/{termId}:open-registration` - Open enrollment (Registrar only)
- `PATCH /terms/{termId}:close-registration` - Close enrollment (Registrar only)
- `PATCH /terms/{termId}:conclude` - Conclude term (Registrar only)

### Course Management
- `POST /terms/{termId}/courses` - Create a course (Professor, Registrar)
- `GET /terms/{termId}/courses` - List courses (role-filtered)
- `GET /terms/{termId}/courses/{courseId}` - Get course details
- `PATCH /terms/{termId}/courses/{courseId}:publish` - Publish course (Professor/owner, Registrar)
- `PATCH /terms/{termId}/courses/{courseId}:cancel` - Cancel course (Professor/owner, Registrar)

### Enrollment Management
- `POST /terms/{termId}/courses/{courseId}/enrollments` - Enroll in course (Student, Registrar)
- `GET /terms/{termId}/courses/{courseId}/enrollments` - List enrollments (Professor/owner, Registrar)
- `GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}` - Get enrollment details
- `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop` - Drop enrollment

### Payment Processing
- `POST /terms/{termId}/students/{studentId}:pay` - Record payment (Student/self, Registrar)

## Authentication

All requests require authentication headers:
- `X-User-ID`: UUID of the user
- `X-User-Role`: One of `STUDENT`, `PROFESSOR`, `REGISTRAR`

### Test Users
The system includes pre-configured test users:
- `registrar-1` (REGISTRAR)
- `prof-1`, `prof-2` (PROFESSOR) 
- `student-1`, `student-2` (STUDENT)

### Example Request
```bash
curl -X POST http://localhost:3000/terms \
  -H "X-User-ID: registrar-1" \
  -H "X-User-Role: REGISTRAR" \
  -H "Content-Type: application/json" \
  -d '{"name": "Fall 2025"}'
```

## Response Format

### Success Response
```json
{
  "meta": {
    "api_request_id": "req_ABC123",
    "api_request_timestamp": "2025-01-01T12:00:00.000Z"
  },
  "response_type": "object",
  "data": { }
}
```

### Error Response
```json
{
  "meta": {
    "api_request_id": "req_ABC123", 
    "api_request_timestamp": "2025-01-01T12:00:00.000Z"
  },
  "response_type": "error",
  "data": {
    "error_id": "ERR_SPECIFIC_ERROR",
    "message": "Human readable error description"
  }
}
```

## Architecture

### File Structure
```
src/
├── models/
│   └── storage.js          # In-memory data storage and business constants
├── middleware/
│   └── auth.js             # Authentication and authorization middleware
├── routes/
│   ├── terms.js           # Term management endpoints
│   ├── courses.js         # Course management endpoints
│   ├── enrollments.js     # Enrollment endpoints
│   └── payments.js        # Payment endpoints
├── utils/
│   ├── response.js        # Response formatting utilities
│   ├── validation.js      # Input validation utilities
│   └── waitlist.js        # Waitlist promotion logic
└── server.js              # Main application entry point
```

### Data Storage
This implementation uses in-memory storage for simplicity. All data is stored in JavaScript Maps and will be lost when the server restarts. For production use, this would be replaced with a persistent database.

### Business Logic
- **Validation Precedence**: Validates from broad scope to specific (term state → resource existence → permissions → business rules)
- **Ledger System**: Maintains real-time seat availability and tuition balances
- **Event-Driven Waitlist**: Automatic promotion when seats become available
- **Role-Based Security**: Field-level filtering based on user role and ownership

## Error Codes

The API implements all error codes specified in the PRD, including:
- Authentication errors (ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER)
- Authorization errors (ERR_UNAUTHORIZED_ROLE, ERR_NOT_INSTRUCTOR)
- Validation errors (ERR_INVALID_ID_FORMAT, ERR_INVALID_ENUM_VALUE)
- Business rule violations (ERR_CREDIT_LIMIT_EXCEEDED, ERR_TOO_MANY_DROPS)
- State conflicts (ERR_TERM_NOT_ACTIVE, ERR_COURSE_WRONG_STATE)

## Compliance with PRD

This implementation strictly adheres to the PRD requirements:
- ✅ All specified endpoints implemented
- ✅ All business rules enforced
- ✅ Role-based access control implemented
- ✅ Standard response formats followed
- ✅ All error codes implemented
- ✅ Pagination support included
- ✅ Optimistic concurrency control implemented
- ✅ Automatic waitlist promotion implemented
- ✅ Complete ledger system implemented

## Limitations

- **In-Memory Storage**: Data is not persistent across server restarts
- **No Database**: Production deployment would require database integration
- **No Email Notifications**: Waitlist promotions happen silently
- **No Audit Trail**: While mentioned in PRD, audit logs are not exposed via API
- **No Grade Management**: Grade assignment is not implemented

This is a fully functional implementation of the University Course Registration & Enrollment API as specified in the PRD, suitable for development, testing, and demonstration purposes.