const { v4: uuidv4 } = require('uuid');

function generateRequestId() {
  return 'req_' + uuidv4().replace(/-/g, '').substring(0, 16);
}

function createSuccessResponse(req, data, responseType = 'object') {
  return {
    meta: {
      api_request_id: req.requestId || generateRequestId(),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: responseType,
    data: data || {}
  };
}

function createErrorResponse(req, errorId, message) {
  return {
    meta: {
      api_request_id: req.requestId || generateRequestId(),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };
}

function addRequestId(req, res, next) {
  req.requestId = generateRequestId();
  next();
}

// Helper function to apply pagination
function applyPagination(items, limit = 50, offset = 0) {
  // Sanitize pagination parameters
  limit = Math.max(1, Math.min(limit, 1000)); // Max 1000 items per page
  offset = Math.max(0, offset);
  
  const total = items.length;
  const paginatedItems = items.slice(offset, offset + limit);
  
  return {
    items: paginatedItems,
    pagination: {
      total,
      limit,
      offset,
      has_more: offset + limit < total
    }
  };
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  addRequestId,
  applyPagination
};