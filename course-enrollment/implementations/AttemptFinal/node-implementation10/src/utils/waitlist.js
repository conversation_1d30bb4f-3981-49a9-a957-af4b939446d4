const { storage, ENROLLMENT_STATES, COST_PER_CREDIT } = require('../models/storage');

function promoteWaitlistedStudent(courseId, termId) {
  try {
    // Check if there are available seats
    const availableSeats = storage.getCourseSeatLedger(courseId) || 0;
    if (availableSeats <= 0) {
      return null; // No seats available
    }

    // Get the next waitlisted student (earliest created_at)
    const nextStudent = storage.getNextWaitlistedStudent(courseId);
    if (!nextStudent) {
      return null; // No one on waitlist
    }

    const course = storage.getCourse(courseId);
    if (!course) {
      throw new Error('Course not found during waitlist promotion');
    }

    // Promote the student
    const updatedEnrollment = storage.updateEnrollment(nextStudent.id, {
      state: ENROLLMENT_STATES.ENROLLED
    });

    // Update ledgers
    // Decrement available seats
    storage.updateCourseSeatLedger(courseId, -1);
    
    // Add tuition charge
    const tuitionCharge = course.credits * COST_PER_CREDIT;
    storage.updateStudentTuitionLedger(nextStudent.student_id, termId, tuitionCharge);

    console.log(`Promoted student ${nextStudent.student_id} from waitlist to enrolled for course ${courseId}`);
    
    // Check if there are more seats and more waitlisted students
    const remainingSeats = storage.getCourseSeatLedger(courseId);
    if (remainingSeats > 0) {
      // Recursively promote the next student
      promoteWaitlistedStudent(courseId, termId);
    }

    return updatedEnrollment;
  } catch (error) {
    console.error('Error during waitlist promotion:', error);
    return null;
  }
}

module.exports = {
  promoteWaitlistedStudent
};