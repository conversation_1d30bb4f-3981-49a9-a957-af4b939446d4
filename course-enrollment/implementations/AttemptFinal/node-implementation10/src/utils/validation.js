function isValidUUID(id) {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i;
  return uuidRegex.test(id);
}

function isValidCourseCode(code) {
  // 2-4 uppercase letters followed by 3 digits (e.g., "CS101", "MATH1001")
  const courseCodeRegex = /^[A-Z]{2,4}\d{3}$/;
  return courseCodeRegex.test(code);
}

function isValidURL(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function validateField(value, fieldName, validationRules) {
  const errors = [];

  if (validationRules.required && (value === undefined || value === null || value === '')) {
    errors.push(`${fieldName} is required`);
    return errors;
  }

  if (value === undefined || value === null || value === '') {
    return errors; // Skip other validations if not required and empty
  }

  if (validationRules.type === 'string') {
    if (typeof value !== 'string') {
      errors.push(`${fieldName} must be a string`);
    } else {
      if (validationRules.minLength && value.length < validationRules.minLength) {
        errors.push(`${fieldName} must be at least ${validationRules.minLength} characters`);
      }
      if (validationRules.maxLength && value.length > validationRules.maxLength) {
        errors.push(`${fieldName} must be no more than ${validationRules.maxLength} characters`);
      }
      if (validationRules.pattern && !validationRules.pattern.test(value)) {
        errors.push(`${fieldName} format is invalid`);
      }
    }
  }

  if (validationRules.type === 'number') {
    if (typeof value !== 'number' || isNaN(value)) {
      errors.push(`${fieldName} must be a valid number`);
    } else {
      if (validationRules.min !== undefined && value < validationRules.min) {
        errors.push(`${fieldName} must be at least ${validationRules.min}`);
      }
      if (validationRules.max !== undefined && value > validationRules.max) {
        errors.push(`${fieldName} must be no more than ${validationRules.max}`);
      }
      if (validationRules.integer && !Number.isInteger(value)) {
        errors.push(`${fieldName} must be an integer`);
      }
    }
  }

  if (validationRules.type === 'enum') {
    if (!validationRules.values.includes(value)) {
      errors.push(`${fieldName} must be one of: ${validationRules.values.join(', ')}`);
    }
  }

  if (validationRules.custom) {
    const customResult = validationRules.custom(value);
    if (customResult !== true) {
      errors.push(customResult);
    }
  }

  return errors;
}

function validateObject(obj, schema, allowUnknown = false) {
  const errors = [];

  // Check for unknown fields
  if (!allowUnknown) {
    const knownFields = new Set(Object.keys(schema));
    for (const field of Object.keys(obj)) {
      if (!knownFields.has(field)) {
        errors.push(`Unknown field: ${field}`);
      }
    }
  }

  // Validate known fields
  for (const [fieldName, rules] of Object.entries(schema)) {
    const fieldErrors = validateField(obj[fieldName], fieldName, rules);
    errors.push(...fieldErrors);
  }

  return errors;
}

function validateConditionalFields(obj, conditions) {
  const errors = [];

  for (const condition of conditions) {
    if (condition.when(obj)) {
      for (const [fieldName, rules] of Object.entries(condition.require)) {
        const fieldErrors = validateField(obj[fieldName], fieldName, rules);
        errors.push(...fieldErrors);
      }
      
      if (condition.forbid) {
        for (const fieldName of condition.forbid) {
          if (obj[fieldName] !== undefined) {
            errors.push(`Field ${fieldName} is not allowed when ${condition.description}`);
          }
        }
      }
    }
  }

  return errors;
}

module.exports = {
  isValidUUID,
  isValidCourseCode,
  isValidURL,
  validateField,
  validateObject,
  validateConditionalFields
};