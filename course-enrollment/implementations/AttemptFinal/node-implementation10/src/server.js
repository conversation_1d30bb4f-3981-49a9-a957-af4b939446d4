const express = require('express');
const cors = require('cors');
const { authenticateUser } = require('./middleware/auth');
const { addRequestId, createErrorResponse } = require('./utils/response');

// Import route handlers
const termsRouter = require('./routes/terms');
const coursesRouter = require('./routes/courses');
const enrollmentsRouter = require('./routes/enrollments');
const paymentsRouter = require('./routes/payments');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(addRequestId);

// Global error handler for invalid JSON
app.use((err, req, res, next) => {
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_JSON',
      'Invalid JSON in request body.'
    ));
  }
  next();
});

// Routes
app.use('/terms', termsRouter);
app.use('/terms/:termId/courses', coursesRouter);
app.use('/terms/:termId/courses/:courseId/enrollments', enrollmentsRouter);
app.use('/terms/:termId/students', paymentsRouter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Catch-all for undefined routes
app.use('*', (req, res) => {
  res.status(404).json(createErrorResponse(
    req,
    'ERR_ENDPOINT_NOT_FOUND',
    'The requested endpoint does not exist.'
  ));
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json(createErrorResponse(
    req,
    'ERR_INTERNAL_SERVER_ERROR',
    'An internal server error occurred.'
  ));
});

// Start server
app.listen(PORT, () => {
  console.log(`University Course Registration API server running on port ${PORT}`);
  console.log(`Health check available at: http://localhost:${PORT}/health`);
  console.log('\nTest users available:');
  console.log('- registrar-1 (REGISTRAR)');
  console.log('- prof-1, prof-2 (PROFESSOR)');
  console.log('- student-1, student-2 (STUDENT)');
  console.log('\nExample request headers:');
  console.log('X-User-ID: registrar-1');
  console.log('X-User-Role: REGISTRAR');
});

module.exports = app;