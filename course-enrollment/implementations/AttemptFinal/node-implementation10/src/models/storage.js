const { v4: uuidv4 } = require('uuid');

// Constants from PRD
const MAX_CREDITS_PER_TERM = 18;
const MAX_COURSES_PER_PROF = 5;
const MAX_DROP_COUNT_PER_TERM = 3;
const DROP_PENALTY_FEE = 5000; // cents ($50.00)
const COST_PER_CREDIT = 10000; // cents ($100.00)

// Term states
const TERM_STATES = {
  PLANNING: 'PLANNING',
  ENROLLMENT_OPEN: 'ENROLLMENT_OPEN',
  ENROLLMENT_CLOSED: 'ENROLLMENT_CLOSED',
  CONCLUDED: 'CONCLUDED'
};

// Course states
const COURSE_STATES = {
  DRAFT: 'DRAFT',
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

// Enrollment states
const ENR<PERSON>LMENT_STATES = {
  ENROLLED: 'ENROLLED',
  WAITLISTED: 'WAITLISTED',
  DROPPED: 'DROPPED',
  COMPLETED: 'COMPLETED'
};

// User roles
const USER_ROLES = {
  STUDENT: 'STUDENT',
  PROFESSOR: 'PROFESSOR',
  REGISTRAR: 'REGISTRAR'
};

// Delivery modes
const DELIVERY_MODES = {
  IN_PERSON: 'IN_PERSON',
  ONLINE: 'ONLINE', 
  HYBRID: 'HYBRID'
};

// In-memory storage
class Storage {
  constructor() {
    this.terms = new Map();
    this.courses = new Map();
    this.enrollments = new Map();
    this.courseSeatLedgers = new Map();
    this.studentTuitionLedgers = new Map();
    this.users = new Map(); // For validation
    
    // Initialize with some test users
    this.initializeTestUsers();
  }

  initializeTestUsers() {
    // Test registrar
    this.users.set('registrar-1', {
      id: 'registrar-1',
      role: USER_ROLES.REGISTRAR,
      name: 'Test Registrar'
    });
    
    // Test professors
    this.users.set('prof-1', {
      id: 'prof-1',
      role: USER_ROLES.PROFESSOR,
      name: 'Professor Smith'
    });
    
    this.users.set('prof-2', {
      id: 'prof-2',
      role: USER_ROLES.PROFESSOR,
      name: 'Professor Jones'
    });
    
    // Test students
    this.users.set('student-1', {
      id: 'student-1',
      role: USER_ROLES.STUDENT,
      name: 'John Doe'
    });
    
    this.users.set('student-2', {
      id: 'student-2',
      role: USER_ROLES.STUDENT,
      name: 'Jane Smith'
    });
  }

  // Term operations
  createTerm(termData) {
    const id = uuidv4();
    const term = {
      id,
      name: termData.name,
      state: TERM_STATES.PLANNING,
      created_by: termData.created_by,
      created_at: new Date().toISOString(),
      revision: 0
    };
    this.terms.set(id, term);
    return term;
  }

  getTerm(id) {
    return this.terms.get(id);
  }

  updateTerm(id, updates) {
    const term = this.terms.get(id);
    if (!term) return null;
    
    const updatedTerm = { ...term, ...updates, revision: term.revision + 1 };
    this.terms.set(id, updatedTerm);
    return updatedTerm;
  }

  // Course operations
  createCourse(termId, courseData) {
    const id = uuidv4();
    const course = {
      id,
      term_id: termId,
      code: courseData.code,
      title: courseData.title,
      description: courseData.description || '',
      credits: courseData.credits,
      capacity: courseData.capacity,
      professor_id: courseData.professor_id,
      delivery_mode: courseData.delivery_mode,
      location: courseData.location,
      online_link: courseData.online_link,
      state: COURSE_STATES.DRAFT,
      created_at: new Date().toISOString(),
      revision: 0
    };
    this.courses.set(id, course);
    return course;
  }

  getCourse(id) {
    return this.courses.get(id);
  }

  updateCourse(id, updates) {
    const course = this.courses.get(id);
    if (!course) return null;
    
    const updatedCourse = { ...course, ...updates, revision: course.revision + 1 };
    this.courses.set(id, updatedCourse);
    return updatedCourse;
  }

  getCoursesByTerm(termId) {
    return Array.from(this.courses.values()).filter(course => course.term_id === termId);
  }

  getCoursesByProfessor(termId, professorId) {
    return Array.from(this.courses.values()).filter(
      course => course.term_id === termId && course.professor_id === professorId
    );
  }

  // Enrollment operations
  createEnrollment(enrollmentData) {
    const id = uuidv4();
    const enrollment = {
      id,
      course_id: enrollmentData.course_id,
      student_id: enrollmentData.student_id,
      state: enrollmentData.state,
      created_at: new Date().toISOString(),
      revision: 0
    };
    this.enrollments.set(id, enrollment);
    return enrollment;
  }

  getEnrollment(id) {
    return this.enrollments.get(id);
  }

  updateEnrollment(id, updates) {
    const enrollment = this.enrollments.get(id);
    if (!enrollment) return null;
    
    const updatedEnrollment = { ...enrollment, ...updates, revision: enrollment.revision + 1 };
    this.enrollments.set(id, updatedEnrollment);
    return updatedEnrollment;
  }

  getEnrollmentsByCourse(courseId) {
    return Array.from(this.enrollments.values()).filter(
      enrollment => enrollment.course_id === courseId
    );
  }

  getEnrollmentsByStudent(studentId, termId) {
    const termCourses = this.getCoursesByTerm(termId);
    const termCourseIds = new Set(termCourses.map(c => c.id));
    
    return Array.from(this.enrollments.values()).filter(
      enrollment => enrollment.student_id === studentId && termCourseIds.has(enrollment.course_id)
    );
  }

  getStudentEnrollment(studentId, courseId) {
    return Array.from(this.enrollments.values()).find(
      enrollment => enrollment.student_id === studentId && enrollment.course_id === courseId
    );
  }

  // Ledger operations
  getCourseSeatLedger(courseId) {
    return this.courseSeatLedgers.get(courseId);
  }

  setCourseSeatLedger(courseId, seatsAvailable) {
    this.courseSeatLedgers.set(courseId, seatsAvailable);
  }

  updateCourseSeatLedger(courseId, delta) {
    const current = this.courseSeatLedgers.get(courseId) || 0;
    const newValue = current + delta;
    this.courseSeatLedgers.set(courseId, newValue);
    return newValue;
  }

  getStudentTuitionLedger(studentId, termId) {
    const key = `${studentId}-${termId}`;
    return this.studentTuitionLedgers.get(key) || 0;
  }

  updateStudentTuitionLedger(studentId, termId, delta) {
    const key = `${studentId}-${termId}`;
    const current = this.studentTuitionLedgers.get(key) || 0;
    const newValue = current + delta;
    this.studentTuitionLedgers.set(key, newValue);
    return newValue;
  }

  // User operations
  getUser(id) {
    return this.users.get(id);
  }

  // Helper methods for business logic
  getEnrolledStudentsCount(courseId) {
    return this.getEnrollmentsByCourse(courseId).filter(
      e => e.state === ENROLLMENT_STATES.ENROLLED
    ).length;
  }

  getWaitlistedStudentsCount(courseId) {
    return this.getEnrollmentsByCourse(courseId).filter(
      e => e.state === ENROLLMENT_STATES.WAITLISTED
    ).length;
  }

  getStudentDropCount(studentId, termId) {
    return this.getEnrollmentsByStudent(studentId, termId).filter(
      e => e.state === ENROLLMENT_STATES.DROPPED
    ).length;
  }

  getStudentEnrolledCredits(studentId, termId) {
    const enrollments = this.getEnrollmentsByStudent(studentId, termId).filter(
      e => e.state === ENROLLMENT_STATES.ENROLLED
    );
    
    let totalCredits = 0;
    for (const enrollment of enrollments) {
      const course = this.getCourse(enrollment.course_id);
      if (course) {
        totalCredits += course.credits;
      }
    }
    
    return totalCredits;
  }

  getNextWaitlistedStudent(courseId) {
    const waitlisted = this.getEnrollmentsByCourse(courseId)
      .filter(e => e.state === ENROLLMENT_STATES.WAITLISTED)
      .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    
    return waitlisted[0] || null;
  }
}

// Create singleton instance
const storage = new Storage();

module.exports = {
  storage,
  MAX_CREDITS_PER_TERM,
  MAX_COURSES_PER_PROF,
  MAX_DROP_COUNT_PER_TERM,
  DROP_PENALTY_FEE,
  COST_PER_CREDIT,
  TERM_STATES,
  COURSE_STATES,
  ENROLLMENT_STATES,
  USER_ROLES,
  DELIVERY_MODES
};