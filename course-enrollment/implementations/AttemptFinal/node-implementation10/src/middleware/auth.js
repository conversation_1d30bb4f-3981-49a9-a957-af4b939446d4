const { storage, USER_ROLES } = require('../models/storage');
const { v4: uuidv4 } = require('uuid');

function createErrorResponse(req, errorId, message) {
  return {
    meta: {
      api_request_id: req.requestId || 'req_' + uuidv4().replace(/-/g, '').substring(0, 16),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };
}

function authenticateUser(req, res, next) {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  // Check if headers are present
  if (!userId || !userRole) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'Missing or invalid user context headers. Both X-User-ID and X-User-Role are required.'
    ));
  }

  // Validate UUID format for user ID
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i;
  if (!uuidRegex.test(userId)) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_ID_FORMAT',
      'User ID must be a valid UUID format.'
    ));
  }

  // Validate role enum
  if (!Object.values(USER_ROLES).includes(userRole)) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_INVALID_ENUM_VALUE',
      `Invalid user role. Must be one of: ${Object.values(USER_ROLES).join(', ')}`
    ));
  }

  // Verify user exists (optional - for more realistic validation)
  const user = storage.getUser(userId);
  if (!user) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'Invalid user ID provided in headers.'
    ));
  }

  // Verify role matches
  if (user.role !== userRole) {
    return res.status(400).json(createErrorResponse(
      req,
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'User role in header does not match user record.'
    ));
  }

  // Attach user info to request
  req.user = {
    id: userId,
    role: userRole,
    name: user.name
  };

  next();
}

function requireRole(...allowedRoles) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
        'Authentication required.'
      ));
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_UNAUTHORIZED_ROLE',
        `Access denied. Required role: ${allowedRoles.join(' or ')}, but user has role: ${req.user.role}`
      ));
    }

    next();
  };
}

function requireOwnership(getResourceOwnerId) {
  return (req, res, next) => {
    // Only applies to students and professors - registrars can access everything
    if (req.user.role === USER_ROLES.REGISTRAR) {
      return next();
    }

    const resourceOwnerId = getResourceOwnerId(req);
    
    if (req.user.role === USER_ROLES.STUDENT && req.user.id !== resourceOwnerId) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_FORBIDDEN',
        'Access denied. Students can only access their own resources.'
      ));
    }

    if (req.user.role === USER_ROLES.PROFESSOR && req.user.id !== resourceOwnerId) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_NOT_INSTRUCTOR',
        'Access denied. Professors can only access their own courses.'
      ));
    }

    next();
  };
}

module.exports = {
  authenticateUser,
  requireRole,
  requireOwnership
};