const express = require('express');
const router = express.Router();
const { storage, TERM_STATES, COURSE_STATES, ENROLLMENT_STATES, USER_ROLES } = require('../models/storage');
const { authenticateUser, requireRole } = require('../middleware/auth');
const { createSuccessResponse, createErrorResponse } = require('../utils/response');
const { validateObject, isValidUUID } = require('../utils/validation');

// POST /terms - Create a new academic term
router.post('/', authenticateUser, requireRole(USER_ROLES.REGISTRAR), (req, res) => {
  try {
    // Validate request body
    const schema = {
      name: {
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 100
      }
    };

    const errors = validateObject(req.body, schema);
    if (errors.length > 0) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_FIELD_LENGTH',
        `Validation errors: ${errors.join(', ')}`
      ));
    }

    const { name } = req.body;

    // Check if term name is unique
    const existingTerms = Array.from(storage.terms.values());
    if (existingTerms.some(term => term.name === name)) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_TERM_NAME_NOT_UNIQUE',
        'A term with this name already exists.'
      ));
    }

    // Create term
    const term = storage.createTerm({
      name,
      created_by: req.user.id
    });

    res.status(201).json(createSuccessResponse(req, term));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// GET /terms/{termId} - Get term details
router.get('/:termId', authenticateUser, (req, res) => {
  try {
    const { termId } = req.params;

    if (!isValidUUID(termId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'Term ID must be a valid UUID format.'
      ));
    }

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    res.json(createSuccessResponse(req, term));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// PATCH /terms/{termId}:open-registration - Open enrollment
router.patch('/:termId\\:open-registration', authenticateUser, requireRole(USER_ROLES.REGISTRAR), (req, res) => {
  try {
    const { termId } = req.params;

    if (!isValidUUID(termId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'Term ID must be a valid UUID format.'
      ));
    }

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Check revision if provided
    if (req.body.revision !== undefined && req.body.revision !== term.revision) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_REV_CONFLICT',
        'Term has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (term.state !== TERM_STATES.PLANNING) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_ACTIVE',
        'Term must be in PLANNING state to open registration.'
      ));
    }

    // Update term state
    const updatedTerm = storage.updateTerm(termId, {
      state: TERM_STATES.ENROLLMENT_OPEN
    });

    res.json(createSuccessResponse(req, updatedTerm));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// PATCH /terms/{termId}:close-registration - Close enrollment
router.patch('/:termId\\:close-registration', authenticateUser, requireRole(USER_ROLES.REGISTRAR), (req, res) => {
  try {
    const { termId } = req.params;

    if (!isValidUUID(termId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'Term ID must be a valid UUID format.'
      ));
    }

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Check revision if provided
    if (req.body.revision !== undefined && req.body.revision !== term.revision) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_REV_CONFLICT',
        'Term has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_ACTIVE',
        'Term must be in ENROLLMENT_OPEN state to close registration.'
      ));
    }

    // Update term state
    const updatedTerm = storage.updateTerm(termId, {
      state: TERM_STATES.ENROLLMENT_CLOSED
    });

    // Cascade update: Move all OPEN courses to IN_PROGRESS
    const courses = storage.getCoursesByTerm(termId);
    for (const course of courses) {
      if (course.state === COURSE_STATES.OPEN) {
        storage.updateCourse(course.id, {
          state: COURSE_STATES.IN_PROGRESS
        });
      }
    }

    res.json(createSuccessResponse(req, updatedTerm));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// PATCH /terms/{termId}:conclude - Conclude term
router.patch('/:termId\\:conclude', authenticateUser, requireRole(USER_ROLES.REGISTRAR), (req, res) => {
  try {
    const { termId } = req.params;

    if (!isValidUUID(termId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'Term ID must be a valid UUID format.'
      ));
    }

    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Check revision if provided
    if (req.body.revision !== undefined && req.body.revision !== term.revision) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_REV_CONFLICT',
        'Term has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (term.state !== TERM_STATES.ENROLLMENT_CLOSED) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_ACTIVE',
        'Term must be in ENROLLMENT_CLOSED state to conclude.'
      ));
    }

    // Update term state
    const updatedTerm = storage.updateTerm(termId, {
      state: TERM_STATES.CONCLUDED
    });

    // Cascade updates: Complete all IN_PROGRESS courses and finalize enrollments
    const courses = storage.getCoursesByTerm(termId);
    for (const course of courses) {
      if (course.state === COURSE_STATES.IN_PROGRESS) {
        storage.updateCourse(course.id, {
          state: COURSE_STATES.COMPLETED
        });
      }

      // Finalize enrollments for this course
      const enrollments = storage.getEnrollmentsByCourse(course.id);
      for (const enrollment of enrollments) {
        if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
          storage.updateEnrollment(enrollment.id, {
            state: ENROLLMENT_STATES.COMPLETED
          });
        } else if (enrollment.state === ENROLLMENT_STATES.WAITLISTED) {
          storage.updateEnrollment(enrollment.id, {
            state: ENROLLMENT_STATES.DROPPED
          });
        }
      }
    }

    res.json(createSuccessResponse(req, updatedTerm));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

module.exports = router;