const express = require('express');
const router = express.Router({ mergeParams: true });
const { storage, USER_ROLES } = require('../models/storage');
const { authenticateUser, requireRole } = require('../middleware/auth');
const { createSuccessResponse, createErrorResponse } = require('../utils/response');
const { validateObject, isValidUUID } = require('../utils/validation');

// POST /terms/{termId}/students/{studentId}:pay - Record tuition payment
router.post('/:studentId\\:pay', authenticateUser, requireRole(USER_ROLES.STUDENT, USER_ROLES.REGISTRAR), (req, res) => {
  try {
    const { termId, studentId } = req.params;

    if (!isValidUUID(termId) || !isValidUUID(studentId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'IDs must be valid UUID format.'
      ));
    }

    // Verify term exists
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Check authorization - students can only pay their own bill
    if (req.user.role === USER_ROLES.STUDENT && req.user.id !== studentId) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_FORBIDDEN',
        'Students can only make payments for their own account.'
      ));
    }

    // Verify student exists if registrar is making payment
    if (req.user.role === USER_ROLES.REGISTRAR) {
      const student = storage.getUser(studentId);
      if (!student || student.role !== USER_ROLES.STUDENT) {
        return res.status(404).json(createErrorResponse(
          req,
          'ERR_STUDENT_NOT_FOUND',
          'Student not found.'
        ));
      }
    }

    // Validate request body
    const schema = {
      amount: {
        type: 'number',
        required: true,
        integer: true,
        min: 1
      }
    };

    const errors = validateObject(req.body, schema);
    if (errors.length > 0) {
      return res.status(422).json(createErrorResponse(
        req,
        'ERR_INVALID_PAYMENT_AMOUNT',
        'Payment amount must be a positive integer (in cents).'
      ));
    }

    const { amount } = req.body;

    // Get current balance
    const currentBalance = storage.getStudentTuitionLedger(studentId, termId);

    // Check for overpayment
    if (amount > currentBalance) {
      return res.status(422).json(createErrorResponse(
        req,
        'ERR_OVERPAY_NOT_ALLOWED',
        `Payment amount ($${(amount / 100).toFixed(2)}) exceeds outstanding balance ($${(currentBalance / 100).toFixed(2)}).`
      ));
    }

    // Process payment by debiting the ledger
    const newBalance = storage.updateStudentTuitionLedger(studentId, termId, -amount);

    const responseData = {
      student_id: studentId,
      term_id: termId,
      payment_amount: amount,
      new_balance: newBalance,
      paid_at: new Date().toISOString()
    };

    res.json(createSuccessResponse(req, responseData));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

module.exports = router;