const express = require('express');
const router = express.Router({ mergeParams: true });
const { 
  storage, 
  TERM_STATES, 
  COURSE_STATES, 
  ENROLLMENT_STATES,
  USER_ROLES,
  MAX_CREDITS_PER_TERM,
  MAX_DROP_COUNT_PER_TERM,
  DROP_PENALTY_FEE,
  COST_PER_CREDIT
} = require('../models/storage');
const { authenticateUser, requireRole } = require('../middleware/auth');
const { createSuccessResponse, createErrorResponse, applyPagination } = require('../utils/response');
const { validateObject, isValidUUID } = require('../utils/validation');
const { promoteWaitlistedStudent } = require('../utils/waitlist');

// POST /terms/{termId}/courses/{courseId}/enrollments - Enroll in course
router.post('/', authenticateUser, requireRole(USER_ROLES.STUDENT, USER_ROLES.REGISTRAR), (req, res) => {
  try {
    const { termId, courseId } = req.params;

    if (!isValidUUID(termId) || !isValidUUID(courseId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'IDs must be valid UUID format.'
      ));
    }

    // Verify term exists and is active for enrollment
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_REGISTRATION_CLOSED',
        'Registration is not currently open for this term.'
      ));
    }

    // Verify course exists and is open for enrollment
    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    if (course.state !== COURSE_STATES.OPEN) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_COURSE_WRONG_STATE',
        'Course is not currently open for enrollment.'
      ));
    }

    // Determine target student ID
    let studentId;
    if (req.user.role === USER_ROLES.STUDENT) {
      studentId = req.user.id;
      // Students cannot specify a different student_id
      if (req.body.student_id && req.body.student_id !== req.user.id) {
        return res.status(400).json(createErrorResponse(
          req,
          'ERR_FIELD_CONFLICT',
          'Students cannot enroll other students.'
        ));
      }
    } else if (req.user.role === USER_ROLES.REGISTRAR) {
      if (!req.body.student_id) {
        return res.status(400).json(createErrorResponse(
          req,
          'ERR_MISSING_REQUIRED_FIELD',
          'Registrar must specify student_id when enrolling students.'
        ));
      }
      studentId = req.body.student_id;
      
      if (!isValidUUID(studentId)) {
        return res.status(400).json(createErrorResponse(
          req,
          'ERR_INVALID_ID_FORMAT',
          'Student ID must be a valid UUID format.'
        ));
      }

      // Verify student exists
      const student = storage.getUser(studentId);
      if (!student || student.role !== USER_ROLES.STUDENT) {
        return res.status(404).json(createErrorResponse(
          req,
          'ERR_STUDENT_NOT_FOUND',
          'Student not found.'
        ));
      }
    }

    // Check for duplicate enrollment
    const existingEnrollment = storage.getStudentEnrollment(studentId, courseId);
    if (existingEnrollment && existingEnrollment.state !== ENROLLMENT_STATES.DROPPED) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_ALREADY_ENROLLED',
        'Student is already enrolled or waitlisted in this course.'
      ));
    }

    // Check credit limit (only for students, not registrar overrides)
    if (req.user.role === USER_ROLES.STUDENT) {
      const currentCredits = storage.getStudentEnrolledCredits(studentId, termId);
      if (currentCredits + course.credits > MAX_CREDITS_PER_TERM) {
        return res.status(409).json(createErrorResponse(
          req,
          'ERR_CREDIT_LIMIT_EXCEEDED',
          `Adding this course would exceed the maximum credit limit of ${MAX_CREDITS_PER_TERM} credits per term.`
        ));
      }
    }

    // Determine enrollment state based on seat availability
    const availableSeats = storage.getCourseSeatLedger(courseId) || 0;
    const enrollmentState = availableSeats > 0 ? ENROLLMENT_STATES.ENROLLED : ENROLLMENT_STATES.WAITLISTED;

    // Create enrollment
    const enrollment = storage.createEnrollment({
      course_id: courseId,
      student_id: studentId,
      state: enrollmentState
    });

    // Update ledgers if student gets a seat
    if (enrollmentState === ENROLLMENT_STATES.ENROLLED) {
      // Decrement available seats
      storage.updateCourseSeatLedger(courseId, -1);
      
      // Add tuition charge
      const tuitionCharge = course.credits * COST_PER_CREDIT;
      storage.updateStudentTuitionLedger(studentId, termId, tuitionCharge);
    }

    res.status(201).json(createSuccessResponse(req, enrollment));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// GET /terms/{termId}/courses/{courseId}/enrollments - List enrollments (roster)
router.get('/', authenticateUser, requireRole(USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR), (req, res) => {
  try {
    const { termId, courseId } = req.params;
    const { limit = 50, offset = 0, state } = req.query;

    if (!isValidUUID(termId) || !isValidUUID(courseId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'IDs must be valid UUID format.'
      ));
    }

    // Verify term exists
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Verify course exists
    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Check authorization - professors can only see their own course rosters
    if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_NOT_INSTRUCTOR',
        'Access denied. Professors can only view rosters for their own courses.'
      ));
    }

    // Get enrollments
    let enrollments = storage.getEnrollmentsByCourse(courseId);

    // Apply state filter if provided
    if (state) {
      if (!Object.values(ENROLLMENT_STATES).includes(state)) {
        return res.status(400).json(createErrorResponse(
          req,
          'ERR_INVALID_ENUM_VALUE',
          `Invalid state filter. Must be one of: ${Object.values(ENROLLMENT_STATES).join(', ')}`
        ));
      }
      enrollments = enrollments.filter(enrollment => enrollment.state === state);
    }

    // Sort by creation date (waitlist order)
    enrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Apply pagination
    const paginatedResult = applyPagination(enrollments, parseInt(limit), parseInt(offset));

    res.json(createSuccessResponse(req, paginatedResult.items, 'array'));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId} - Get specific enrollment
router.get('/:enrollmentId', authenticateUser, (req, res) => {
  try {
    const { termId, courseId, enrollmentId } = req.params;

    if (!isValidUUID(termId) || !isValidUUID(courseId) || !isValidUUID(enrollmentId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'IDs must be valid UUID format.'
      ));
    }

    // Verify term exists
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Verify course exists
    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Get enrollment
    const enrollment = storage.getEnrollment(enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_ENROLLMENT_NOT_FOUND',
        'Enrollment not found.'
      ));
    }

    // Check authorization
    if (req.user.role === USER_ROLES.STUDENT && enrollment.student_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_FORBIDDEN',
        'Students can only view their own enrollments.'
      ));
    }

    if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_NOT_INSTRUCTOR',
        'Professors can only view enrollments for their own courses.'
      ));
    }

    res.json(createSuccessResponse(req, enrollment));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop - Drop enrollment
router.patch('/:enrollmentId\\:drop', authenticateUser, (req, res) => {
  try {
    const { termId, courseId, enrollmentId } = req.params;

    if (!isValidUUID(termId) || !isValidUUID(courseId) || !isValidUUID(enrollmentId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'IDs must be valid UUID format.'
      ));
    }

    // Verify term exists and allows drops
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    if (term.state === TERM_STATES.CONCLUDED) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_ACTIVE',
        'Cannot drop courses in a concluded term.'
      ));
    }

    // Verify course exists
    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Get enrollment
    const enrollment = storage.getEnrollment(enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_ENROLLMENT_NOT_FOUND',
        'Enrollment not found.'
      ));
    }

    // Check authorization
    let isAuthorized = false;
    if (req.user.role === USER_ROLES.STUDENT && enrollment.student_id === req.user.id) {
      isAuthorized = true;
    } else if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id === req.user.id) {
      isAuthorized = true;
    } else if (req.user.role === USER_ROLES.REGISTRAR) {
      isAuthorized = true;
    }

    if (!isAuthorized) {
      return res.status(403).json(createErrorResponse(
        req,
        req.user.role === USER_ROLES.STUDENT ? 'ERR_FORBIDDEN' : 'ERR_NOT_INSTRUCTOR',
        'Access denied.'
      ));
    }

    // Check enrollment state
    if (![ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED].includes(enrollment.state)) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_ENROLLMENT_WRONG_STATE',
        'Cannot drop an enrollment that is already dropped or completed.'
      ));
    }

    // Check drop count for students (not for admin drops)
    if (req.user.role === USER_ROLES.STUDENT && enrollment.student_id === req.user.id) {
      const currentDropCount = storage.getStudentDropCount(enrollment.student_id, termId);
      if (currentDropCount >= MAX_DROP_COUNT_PER_TERM) {
        return res.status(409).json(createErrorResponse(
          req,
          'ERR_TOO_MANY_DROPS',
          `Students cannot drop more than ${MAX_DROP_COUNT_PER_TERM} courses per term.`
        ));
      }
    }

    // Check revision if provided
    if (req.body.revision !== undefined && req.body.revision !== enrollment.revision) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_REV_CONFLICT',
        'Enrollment has been modified by another user. Please refresh and try again.'
      ));
    }

    const wasEnrolled = enrollment.state === ENROLLMENT_STATES.ENROLLED;
    const studentId = enrollment.student_id;

    // Update enrollment state
    const updatedEnrollment = storage.updateEnrollment(enrollmentId, {
      state: ENROLLMENT_STATES.DROPPED
    });

    // Handle ledger updates and refunds
    if (wasEnrolled) {
      // Free up a seat
      storage.updateCourseSeatLedger(courseId, 1);
      
      // Refund tuition
      const refundAmount = course.credits * COST_PER_CREDIT;
      storage.updateStudentTuitionLedger(studentId, termId, -refundAmount);
      
      // Trigger waitlist promotion
      promoteWaitlistedStudent(courseId, termId);
    }

    // Apply drop penalty if this is student's 3rd drop
    if (req.user.role === USER_ROLES.STUDENT && enrollment.student_id === req.user.id) {
      const newDropCount = storage.getStudentDropCount(studentId, termId);
      if (newDropCount === MAX_DROP_COUNT_PER_TERM) {
        storage.updateStudentTuitionLedger(studentId, termId, DROP_PENALTY_FEE);
      }
    }

    res.json(createSuccessResponse(req, updatedEnrollment));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

module.exports = router;