const express = require('express');
const router = express.Router({ mergeParams: true });
const { 
  storage, 
  TERM_STATES, 
  COURSE_STATES, 
  ENROLLMENT_STATES,
  USER_ROLES, 
  DELIVERY_MODES,
  MAX_COURSES_PER_PROF,
  COST_PER_CREDIT
} = require('../models/storage');
const { authenticateUser, requireRole } = require('../middleware/auth');
const { createSuccessResponse, createErrorResponse, applyPagination } = require('../utils/response');
const { validateObject, validateConditionalFields, isValidUUID, isValidCourseCode, isValidURL } = require('../utils/validation');

// Helper function to filter course data based on user role
function filterCourseData(course, user, enrollment = null) {
  const baseData = {
    id: course.id,
    code: course.code,
    title: course.title,
    description: course.description,
    credits: course.credits,
    capacity: course.capacity,
    professor_id: course.professor_id,
    delivery_mode: course.delivery_mode,
    location: course.location,
    online_link: course.online_link,
    state: course.state,
    created_at: course.created_at
  };

  // Add computed fields
  const enrolledCount = storage.getEnrolledStudentsCount(course.id);
  const waitlistCount = storage.getWaitlistedStudentsCount(course.id);
  const availableSeats = storage.getCourseSeatLedger(course.id) || Math.max(0, course.capacity - enrolledCount);

  if (user.role === USER_ROLES.STUDENT) {
    // Students see limited data
    const studentData = {
      ...baseData,
      available_seats: availableSeats
    };
    
    // Add student's enrollment status if they're enrolled/waitlisted
    if (enrollment) {
      studentData.is_enrolled = enrollment.state === ENROLLMENT_STATES.ENROLLED;
      studentData.is_waitlisted = enrollment.state === ENROLLMENT_STATES.WAITLISTED;
    }
    
    return studentData;
  } else if (user.role === USER_ROLES.PROFESSOR) {
    // Professors see full data for their courses, limited for others
    if (course.professor_id === user.id) {
      return {
        ...baseData,
        enrolled_count: enrolledCount,
        waitlist_count: waitlistCount,
        available_seats: availableSeats,
        revision: course.revision
      };
    } else {
      // Other professors' courses - student-level view
      return {
        ...baseData,
        available_seats: availableSeats
      };
    }
  } else if (user.role === USER_ROLES.REGISTRAR) {
    // Registrars see everything
    return {
      ...baseData,
      enrolled_count: enrolledCount,
      waitlist_count: waitlistCount,
      available_seats: availableSeats,
      revision: course.revision
    };
  }

  return baseData;
}

// POST /terms/{termId}/courses - Create a new course
router.post('/', authenticateUser, requireRole(USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR), (req, res) => {
  try {
    const { termId } = req.params;

    if (!isValidUUID(termId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'Term ID must be a valid UUID format.'
      ));
    }

    // Verify term exists
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Validate request body
    const schema = {
      code: {
        type: 'string',
        required: true,
        custom: (value) => isValidCourseCode(value) || 'Course code must be 2-4 uppercase letters followed by 3 digits (e.g., CS101)'
      },
      title: {
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 100
      },
      description: {
        type: 'string',
        maxLength: 1000
      },
      credits: {
        type: 'number',
        required: true,
        integer: true,
        min: 1,
        max: 5
      },
      capacity: {
        type: 'number',
        required: true,
        integer: true,
        min: 1,
        max: 500
      },
      delivery_mode: {
        type: 'enum',
        required: true,
        values: Object.values(DELIVERY_MODES)
      },
      location: {
        type: 'string',
        minLength: 1
      },
      online_link: {
        type: 'string',
        custom: (value) => isValidURL(value) || 'Online link must be a valid URL'
      },
      professor_id: {
        type: 'string',
        custom: (value) => isValidUUID(value) || 'Professor ID must be a valid UUID'
      }
    };

    const errors = validateObject(req.body, schema);
    if (errors.length > 0) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_FIELD_LENGTH',
        `Validation errors: ${errors.join(', ')}`
      ));
    }

    // Validate conditional fields based on delivery mode
    const conditionalErrors = validateConditionalFields(req.body, [
      {
        when: (obj) => obj.delivery_mode === DELIVERY_MODES.IN_PERSON,
        require: {
          location: { type: 'string', required: true, minLength: 1 }
        },
        forbid: ['online_link'],
        description: 'delivery mode is IN_PERSON'
      },
      {
        when: (obj) => obj.delivery_mode === DELIVERY_MODES.ONLINE,
        require: {
          online_link: { type: 'string', required: true, custom: (value) => isValidURL(value) || 'Online link must be a valid URL' }
        },
        forbid: ['location'],
        description: 'delivery mode is ONLINE'
      },
      {
        when: (obj) => obj.delivery_mode === DELIVERY_MODES.HYBRID,
        require: {},
        description: 'delivery mode is HYBRID'
      }
    ]);

    if (conditionalErrors.length > 0) {
      return res.status(400).json(createErrorResponse(
        req,
        req.body.online_link && req.body.delivery_mode === DELIVERY_MODES.IN_PERSON ? 'ERR_FIELD_CONFLICT' :
        req.body.location && req.body.delivery_mode === DELIVERY_MODES.ONLINE ? 'ERR_FIELD_CONFLICT' :
        'ERR_CONDITIONAL_FIELD_REQUIRED',
        `Validation errors: ${conditionalErrors.join(', ')}`
      ));
    }

    // Additional validation for HYBRID mode
    if (req.body.delivery_mode === DELIVERY_MODES.HYBRID) {
      if (!req.body.location && !req.body.online_link) {
        return res.status(400).json(createErrorResponse(
          req,
          'ERR_CONDITIONAL_FIELD_REQUIRED',
          'HYBRID delivery mode requires at least one of location or online_link'
        ));
      }
    }

    const { code, title, description, credits, capacity, delivery_mode, location, online_link } = req.body;
    let { professor_id } = req.body;

    // Handle professor assignment based on role
    if (req.user.role === USER_ROLES.PROFESSOR) {
      if (professor_id && professor_id !== req.user.id) {
        return res.status(400).json(createErrorResponse(
          req,
          'ERR_FIELD_CONFLICT',
          'Professors cannot create courses for other professors.'
        ));
      }
      professor_id = req.user.id;
    } else if (req.user.role === USER_ROLES.REGISTRAR) {
      if (!professor_id) {
        return res.status(400).json(createErrorResponse(
          req,
          'ERR_MISSING_REQUIRED_FIELD',
          'Registrar must specify professor_id when creating courses.'
        ));
      }
      // Verify professor exists
      const professor = storage.getUser(professor_id);
      if (!professor || professor.role !== USER_ROLES.PROFESSOR) {
        return res.status(422).json(createErrorResponse(
          req,
          'ERR_INVALID_INSTRUCTOR',
          'Invalid professor ID provided.'
        ));
      }
    }

    // Check course code uniqueness within term
    const existingCourses = storage.getCoursesByTerm(termId);
    if (existingCourses.some(course => course.code === code)) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_COURSE_CODE_NOT_UNIQUE',
        'A course with this code already exists in this term.'
      ));
    }

    // Check professor course limit
    const professorCourses = storage.getCoursesByProfessor(termId, professor_id);
    if (professorCourses.length >= MAX_COURSES_PER_PROF) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_MAX_COURSES_REACHED',
        `Professor cannot teach more than ${MAX_COURSES_PER_PROF} courses per term.`
      ));
    }

    // Create course
    const course = storage.createCourse(termId, {
      code,
      title,
      description: description || '',
      credits,
      capacity,
      professor_id,
      delivery_mode,
      location,
      online_link
    });

    const responseData = filterCourseData(course, req.user);
    res.status(201).json(createSuccessResponse(req, responseData));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// GET /terms/{termId}/courses - List courses
router.get('/', authenticateUser, (req, res) => {
  try {
    const { termId } = req.params;
    const { limit = 50, offset = 0, state, professor_id } = req.query;

    if (!isValidUUID(termId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'Term ID must be a valid UUID format.'
      ));
    }

    // Verify term exists
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Get courses and apply role-based filtering
    let courses = storage.getCoursesByTerm(termId);

    // Apply query filters
    if (state) {
      if (!Object.values(COURSE_STATES).includes(state)) {
        return res.status(400).json(createErrorResponse(
          req,
          'ERR_INVALID_ENUM_VALUE',
          `Invalid state filter. Must be one of: ${Object.values(COURSE_STATES).join(', ')}`
        ));
      }
      courses = courses.filter(course => course.state === state);
    }

    if (professor_id) {
      if (!isValidUUID(professor_id)) {
        return res.status(400).json(createErrorResponse(
          req,
          'ERR_INVALID_ID_FORMAT',
          'Professor ID must be a valid UUID format.'
        ));
      }
      courses = courses.filter(course => course.professor_id === professor_id);
    }

    // Role-based visibility filtering
    if (req.user.role === USER_ROLES.STUDENT) {
      // Students only see published, in-progress, or completed courses
      courses = courses.filter(course => 
        [COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)
      );
    } else if (req.user.role === USER_ROLES.PROFESSOR) {
      // Professors see their own courses (all states) + other professors' published courses
      courses = courses.filter(course => 
        course.professor_id === req.user.id || 
        [COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)
      );
    }
    // Registrars see all courses (no additional filtering)

    // Sort by creation date
    courses.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Apply pagination
    const paginatedResult = applyPagination(courses, parseInt(limit), parseInt(offset));

    // Filter data for each course based on user role
    const filteredCourses = paginatedResult.items.map(course => 
      filterCourseData(course, req.user)
    );

    res.json(createSuccessResponse(req, filteredCourses, 'array'));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// GET /terms/{termId}/courses/{courseId} - Get course details
router.get('/:courseId', authenticateUser, (req, res) => {
  try {
    const { termId, courseId } = req.params;

    if (!isValidUUID(termId) || !isValidUUID(courseId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'IDs must be valid UUID format.'
      ));
    }

    // Verify term exists
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Role-based visibility check
    if (req.user.role === USER_ROLES.STUDENT) {
      // Students can only see published, in-progress, or completed courses
      // OR courses they're enrolled in (even if cancelled)
      const studentEnrollment = storage.getStudentEnrollment(req.user.id, courseId);
      if (!studentEnrollment && 
          ![COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)) {
        return res.status(404).json(createErrorResponse(
          req,
          'ERR_COURSE_NOT_FOUND',
          'Course not found.'
        ));
      }
      
      const responseData = filterCourseData(course, req.user, studentEnrollment);
      
      // Add enrollments for instructor/registrar
      if (course.professor_id === req.user.id || req.user.role === USER_ROLES.REGISTRAR) {
        const enrollments = storage.getEnrollmentsByCourse(courseId);
        responseData.enrollments = enrollments.map(enrollment => ({
          id: enrollment.id,
          student_id: enrollment.student_id,
          state: enrollment.state,
          created_at: enrollment.created_at
        }));
      }
      
      return res.json(createSuccessResponse(req, responseData));
    }

    if (req.user.role === USER_ROLES.PROFESSOR) {
      // Professors can see their own courses (any state) or published courses of others
      if (course.professor_id !== req.user.id && 
          ![COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS, COURSE_STATES.COMPLETED].includes(course.state)) {
        return res.status(404).json(createErrorResponse(
          req,
          'ERR_COURSE_NOT_FOUND',
          'Course not found.'
        ));
      }
    }

    const responseData = filterCourseData(course, req.user);
    
    // Add enrollments for instructor/registrar
    if (course.professor_id === req.user.id || req.user.role === USER_ROLES.REGISTRAR) {
      const enrollments = storage.getEnrollmentsByCourse(courseId);
      responseData.enrollments = enrollments.map(enrollment => ({
        id: enrollment.id,
        student_id: enrollment.student_id,
        state: enrollment.state,
        created_at: enrollment.created_at
      }));
    }

    res.json(createSuccessResponse(req, responseData));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// PATCH /terms/{termId}/courses/{courseId}:publish - Publish course
router.patch('/:courseId\\:publish', authenticateUser, requireRole(USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR), (req, res) => {
  try {
    const { termId, courseId } = req.params;

    if (!isValidUUID(termId) || !isValidUUID(courseId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'IDs must be valid UUID format.'
      ));
    }

    // Verify term exists and is open for enrollment
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    if (term.state !== TERM_STATES.ENROLLMENT_OPEN) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_ACTIVE',
        'Term must be open for enrollment to publish courses.'
      ));
    }

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Check authorization
    if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_NOT_INSTRUCTOR',
        'Only the course instructor can publish this course.'
      ));
    }

    // Check revision if provided
    if (req.body.revision !== undefined && req.body.revision !== course.revision) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_REV_CONFLICT',
        'Course has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (course.state !== COURSE_STATES.DRAFT) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_COURSE_WRONG_STATE',
        'Course must be in DRAFT state to publish.'
      ));
    }

    // Update course state and initialize seat ledger
    const updatedCourse = storage.updateCourse(courseId, {
      state: COURSE_STATES.OPEN
    });

    // Initialize seat ledger
    storage.setCourseSeatLedger(courseId, course.capacity);

    const responseData = filterCourseData(updatedCourse, req.user);
    res.json(createSuccessResponse(req, responseData));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

// PATCH /terms/{termId}/courses/{courseId}:cancel - Cancel course
router.patch('/:courseId\\:cancel', authenticateUser, requireRole(USER_ROLES.PROFESSOR, USER_ROLES.REGISTRAR), (req, res) => {
  try {
    const { termId, courseId } = req.params;

    if (!isValidUUID(termId) || !isValidUUID(courseId)) {
      return res.status(400).json(createErrorResponse(
        req,
        'ERR_INVALID_ID_FORMAT',
        'IDs must be valid UUID format.'
      ));
    }

    // Verify term exists
    const term = storage.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    const course = storage.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        req,
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Check authorization
    if (req.user.role === USER_ROLES.PROFESSOR && course.professor_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        req,
        'ERR_NOT_INSTRUCTOR',
        'Only the course instructor can cancel this course.'
      ));
    }

    // Check revision if provided
    if (req.body.revision !== undefined && req.body.revision !== course.revision) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_REV_CONFLICT',
        'Course has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (![COURSE_STATES.DRAFT, COURSE_STATES.OPEN, COURSE_STATES.IN_PROGRESS].includes(course.state)) {
      return res.status(409).json(createErrorResponse(
        req,
        'ERR_COURSE_WRONG_STATE',
        'Cannot cancel a course that is already completed or cancelled.'
      ));
    }

    // Update course state
    const updatedCourse = storage.updateCourse(courseId, {
      state: COURSE_STATES.CANCELLED
    });

    // Drop all enrollments and process refunds
    const enrollments = storage.getEnrollmentsByCourse(courseId);
    for (const enrollment of enrollments) {
      if ([ENROLLMENT_STATES.ENROLLED, ENROLLMENT_STATES.WAITLISTED].includes(enrollment.state)) {
        // Drop the enrollment
        storage.updateEnrollment(enrollment.id, {
          state: ENROLLMENT_STATES.DROPPED
        });

        // Refund tuition if student was enrolled (had a seat)
        if (enrollment.state === ENROLLMENT_STATES.ENROLLED) {
          const refundAmount = course.credits * COST_PER_CREDIT;
          storage.updateStudentTuitionLedger(enrollment.student_id, termId, -refundAmount);
        }
      }
    }

    const responseData = filterCourseData(updatedCourse, req.user);
    res.json(createSuccessResponse(req, responseData));
  } catch (error) {
    res.status(500).json(createErrorResponse(
      req,
      'ERR_INTERNAL_SERVER_ERROR',
      'An internal server error occurred.'
    ));
  }
});

module.exports = router;