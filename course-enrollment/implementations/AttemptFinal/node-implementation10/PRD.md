# University Course Registration & Enrollment API – Product Requirements Document

## 1. Overview

The **University Course Registration & Enrollment API** manages academic course offerings and student enrollments within an **AcademicTerm**.

## 2. Global Specifications

### 2.1 Authentication & User Context

All requests must include identity headers: **`X-User-ID`** (the user's UUID) and **`X-User-Role`** (the user's role, enum: `STUDENT`, `PROFESSOR`, or `REGISTRAR`). These define the acting user's context for authorization checks. If either header is missing or malformed, the API rejects the call as a bad request (`400`) with error `ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER`. The system recognizes three roles:

* **Student:** an end-user who can view available courses and manage *their own* enrollments (registering or dropping themselves). Students cannot create or modify courses, nor see confidential fields like other students' information or financial ledgers. Certain actions (like paying tuition) are restricted to affecting their own record only. All student operations are subject to academic rules (e.g. credit load limits, course availability).
* **Professor:** an instructor who can create new courses (automatically listed as that course's instructor) and manage the lifecycle of courses they teach (e.g. publishing a draft course to open it for enrollment, or cancelling their course if needed). Professors can view detailed rosters and course stats for their own classes (including which students are enrolled or waitlisted and relevant ledger info like available seats), but they cannot enroll students (except by invoking Registrar privileges) and cannot modify or see restricted data for courses they do not teach.
* **Registrar:** an administrative role with the broadest privileges. The Registrar manages academic terms (e.g. creating a term, opening/closing enrollment periods, concluding terms) and can oversee any course or enrollment within the term. This role can perform **administrative overrides**: creating courses on behalf of professors, enrolling or dropping students in any course, and processing tuition payments. The Registrar is also the only role permitted to transition term-wide states (such as opening or closing registration). In cases where a business rule would normally block an action (exceeding credit limits, enrolling in a closed term, etc.), the Registrar's actions can explicitly bypass or override those rules when noted.

**RBAC Enforcement:** Every endpoint defines which roles are allowed. If a user's role is not authorized for a certain operation, the request fails with a `403 Forbidden` error (e.g. `ERR_UNAUTHORIZED_ROLE`). Additionally, even if the role is correct, many actions require the user to be related to the resource: for instance, a Professor can only modify a course if they are that course's instructor. Attempts to perform an action on a resource one doesn't own (or a professor trying to manipulate another professor's course) are forbidden (e.g. `ERR_NOT_INSTRUCTOR` for a non-owner professor). These checks prevent privilege escalation. On the response side, **field-level security** is in place: sensitive fields are omitted or masked for unauthorized viewers. For example, only the Registrar or a course's instructor can see the full enrollment list and financial details of a course, whereas a student viewing the course will see a limited set of fields. All such redactions happen automatically based on the `X-User-Role` context. When providing values for enum fields throughout the API (such as `state` fields for terms, courses, or enrollments, or the `X-User-Role` header itself), the system strictly validates that the provided value is one of the allowed options for that field. Any value outside the defined set results in `ERR_INVALID_ENUM_VALUE` (400 Bad Request), ensuring type safety and preventing undefined states.

### 2.2 Standard Response Formats

#### **Standard Success Response Format**

All successful API responses (those with 2xx status codes) follow this standard envelope format in their JSON body:

```json
{
  "meta": {
    "api_request_id": "req_RANDOMALPHANUM123456",
    "api_request_timestamp": "2025-05-24T10:30:00.123Z"
  },
  "response_type": "object" | "array",
  "data": { }
}
```

* The `api_request_id` is a server-generated unique identifier for the request.
* The `api_request_timestamp` is a server-generated ISO 8601 UTC timestamp.
* The `response_type` is either "object" for a single resource or "array" for a list of resources.
* The `data` field contains the resource object or an array of resource objects. For 2xx responses with no data, it contains an empty object.

#### **Standard Error Response Format**

All error API responses (4xx status codes) follow this standard envelope format:

```json
{
  "meta": {
    "api_request_id": "req_RANDOMALPHANUM123456",
    "api_request_timestamp": "2025-05-24T10:30:00.123Z"
  },
  "response_type": "error",
  "data": {
    "error_id": "UNIQUE_ERROR_IDENTIFIER_STRING",
    "message": "A human-readable explanation of the error."
  }
}
```

* The `error_id` is a unique, machine-readable string that identifies the specific error.
* The `message` provides a human-readable explanation of what went wrong.

### 2.3 Pagination

List endpoints (any `GET` that returns a collection, such as listing courses or enrollments) support pagination via standard query parameters. The **`limit`** parameter defines the maximum number of items to return, and **`offset`** specifies the index of the first item to retrieve (zero-based). If not provided, default values (e.g.  `limit=50`) are used. The response's `meta` section will include relevant fields like the `total` number of items (if available) or indicators for next/previous page. Clients can use this information to implement paging UIs. All list endpoints return items sorted in a deterministic order (usually by creation time or another logical ordering) to ensure stable pagination results. For instance, when listing enrollments of a course, the default order may be by enrollment creation date (which effectively orders waitlisted students in the sequence they joined the waitlist). Pagination is optional on these endpoints; if a client omits `limit/offset`, the service may return a full list capped by an internal maximum. The implementation ensures that pagination parameters outside valid ranges (negative or excessively large values) are sanitized or result in a `400 Bad Request`.

### 2.4 Academic Term

**AcademicTerm:** All courses, enrollments, payments, ledgers, etc. exist only within the context of a specific AcademicTerm, identified by a term ID in the endpoint paths (e.g. `/terms/{termId}/...`). All endpoints require a valid term identifier in the URL, and the system will treat any resource reference that doesn't match the given term as nonexistent. If a client tries to access or modify a course or enrollment under the wrong term ID, the API will return a `404 Not Found` (`ERR_TERM_NOT_FOUND` or a resource-specific not-found error) rather than risk leaking information from another term.

**Resource Identity & Uniqueness:** Within a term, each resource has a unique identifier. All IDs are UUIDs (string format `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`). The API validates these IDs format on input; any malformed UUID (e.g. wrong length or characters) results in a `400 Bad Request` with `ERR_INVALID_ID_FORMAT` before any lookup is attempted. Specifically, every `id` field must match the pattern `^[0-9a-fA-F\-]{36}$` – containing exactly 36 characters of hexadecimal digits (0-9, a-f, A-F) and hyphens in the standard UUID positions. Certain fields are also unique within a term's scope: for instance, each Course has a `code` (like "CS101") which must be unique among courses of the same term. If a duplicate code is provided when creating or updating a course, the request is rejected with a conflict error (such as `ERR_COURSE_CODE_NOT_UNIQUE`). This uniqueness is checked within each term, so identical codes can exist in different terms. Similarly, AcademicTerm's own `name` field (e.g. "Fall 2025") must be unique across all terms, preventing two active terms with the same name; an attempt to create a term with a duplicate name fails with `ERR_TERM_NAME_NOT_UNIQUE`. To enforce strict schema adherence, any request body containing properties that are not defined by the API schema will be rejected with `ERR_UNKNOWN_FIELD` – the system does not tolerate extraneous fields, ensuring clean data contracts.

### 2.5 Core Systems and Ledgers

The platform maintains several **term-scoped ledgers** to keep track of important aggregated counts and balances that transcend a single resource. These ledgers are automatically updated as side effects of user actions, ensuring global consistency:

* **CourseSeatLedger:** For each Course within a term, this ledger tracks the remaining **seats available** in that course. It is initialized when a course is published (opened for enrollment) with `seats_available` equal to the course's capacity. Every time a student successfully enrolls and obtains a seat, the ledger is **debited** (decremented) by 1. Whenever a seat is freed (e.g. a student drops a course they were enrolled in), the ledger is **credited** (incremented) by 1 to reflect an open seat. The ledger enforces that its value never drops below 0 or above the course's capacity; any attempt to enroll beyond capacity is blocked by the business rules (triggering errors like `ERR_COURSE_FULL`) or internally treated as an invalid ledger operation. More specifically, any attempt – whether administrative or through internal system operations – to debit the ledger below 0 or credit it above the course's capacity is rejected with `ERR_CAPACITY_EXCEEDED` (409 Conflict). In essence, the **available_seats** for a course is always kept in sync with the course's capacity minus current enrolled count. This ledger-driven approach centralizes seat counting logic and prevents race conditions in concurrent enrollments.

* **StudentTuitionLedger:** For each Student in a term, this ledger records that student's **outstanding tuition balance** (in cents) for the courses they are enrolled in. It starts at 0 for a new student (or is created on the fly at first enrollment) and increases when the student takes on new courses, then decreases when payments are made or courses are dropped. Specifically, enrolling in a course *credits* the student's ledger by the tuition cost of that course (calculated as `credits * COST_PER_CREDIT`, where each credit hour costs $100.00 or 10000 cents), representing an increase in money owed. Dropping a course refunds tuition: if the student had a seat, the ledger is *debited* by the course cost (reducing balance) upon drop. Payments explicitly debit the ledger as well (see Payment workflow). The system ensures the ledger's balance can never go negative – if a payment exceeds the current balance, the request is rejected (`ERR_OVERPAY_NOT_ALLOWED`). The ledger also prevents any illegal operations (e.g. trying to decrement more than the current balance or credit in unexpected ways), which would be considered an internal consistency error (`ERR_LEDGER_INVALID_OP`). Overall, these ledgers provide a consistent, transactional view of available seats and financials, and their values are used to derive real-time fields in responses (like a course's `available_seats` or a student's balance due).

* **Audit & Revision Control:** The system keeps an audit log (not exposed via API, but mentioned for completeness) of significant events (term opened, course published, waitlist promotions, payments, etc.) for traceability. Each modifiable resource includes a **`revision`** number (integer) that increments on updates, implementing optimistic concurrency control. Clients are expected to send the latest known `revision` when attempting updates that fetch-then-modify (for example, when publishing or cancelling a course, or dropping an enrollment). If the server finds the provided revision is out-of-date – meaning the resource was changed by someone else between fetch and update – it will reject the request with a `409 Conflict` error `ERR_REV_CONFLICT`. This prevents simultaneous updates from accidentally overwriting each other. The revision check is done early in the request processing, typically after basic permission checks but before applying business logic, to fail fast on stale data.

### 2.6 System Invariants

The following invariants are **global rules** that must hold true across all operations within an academic term. They represent constraints or truths of the academic domain and system that all endpoints and workflows respect:

* **Term State Gating:** An AcademicTerm's **`state`** (one of `PLANNING`, `ENROLLMENT_OPEN`, `ENROLLMENT_CLOSED`, `CONCLUDED`) determines what actions are globally allowed. It is invariant that no student can enroll or drop courses unless the term's state is `ENROLLMENT_OPEN`. If the term is not open for enrollment, any attempt to create or modify enrollments is blocked (`ERR_TERM_NOT_ACTIVE`). Likewise, certain course operations depend on term state: e.g. a course cannot be published if the term itself is not open for registration. The term state progression is linear (PLANNING -> ENROLLMENT_OPEN -> ENROLLMENT_CLOSED -> CONCLUDED); the system will not permit illegal transitions (such as concluding a term that is still in PLANNING) – doing so triggers `ERR_TERM_NOT_ACTIVE` or a specific state error. When a term moves to `CONCLUDED`, it is an invariant that no further changes (enrollments, drops, course edits) can happen in that term – all courses are finalized and all enrollments become read-only records.

* **Role Permissions Hierarchy:** At all times, the allowed actions follow a strict hierarchy by role – Registrar override powers are topmost. This means if a Student and Registrar both attempt the same action, the Registrar's attempt can bypass certain checks (for instance, credit limits or closed registration windows) that would stop a student. However, even the Registrar cannot violate fundamental data consistency (e.g. cannot enroll a non-existent student or exceed a course's capacity without adjusting it first). The invariant is that **Students are constrained by all rules**, **Professors by all except where noted (usually they have no special override)**, and **Registrars can override specific student-focused limits** (like credit max or drop deadlines) *only where explicitly allowed*. Any action not permitted by role yields a `403` error as described in Section 2.1.

* **Credit Load Limit:** By default, a student's total enrolled credit hours in a term cannot exceed **18 credits** (constant `MAX_CREDITS_PER_TERM = 18`). The sum of credits from all courses in which the student is actively enrolled (not counting waitlisted courses) is checked whenever a new enrollment is attempted. If admitting the student to a course would push their total credits over 18, the system blocks a self-service enrollment with `ERR_CREDIT_LIMIT_EXCEEDED`. This is a hard invariant for students. The Registrar, however, may override this on a case-by-case basis: an administrative enrollment that would exceed 18 credits is allowed (the Registrar is assumed to have granted an overload exception), and in such cases no error is thrown. The limit itself (18) is never violated in system state – in an override scenario, the invariant is maintained by virtue of Registrar's authority (i.e. it's understood that the rule doesn't apply to that action).

* **Course Capacity:** Each Course has a fixed **`capacity`** (number of seats available) set when it is created. It is invariant that the number of enrolled students in a course can never exceed this capacity. The **CourseSeatLedger** (seats_available) enforces this by disallowing any operation that would drop the available seats below 0. In practice, if a student tries to enroll in a course that is at capacity, they will be placed on a waitlist instead of being enrolled (thus not violating the capacity). If an attempt is made to force enrollment beyond capacity (which normal flows wouldn't do, except potentially via misuse or a bug), it would result in an error (`ERR_COURSE_FULL` or equivalently `ERR_CAPACITY_EXCEEDED`) and no enrollment would be created. The invariant is also maintained through seat management on drops: whenever a seat opens up (someone drops an enrolled seat), the system will automatically fill it with the next waitlisted student (see **Waitlist Promotion** below), or if none are waiting, simply increment the available seat count. This guarantees that the ledger's `seats_available + enrolled_count = capacity` at all times.

* **Professor Course Limit:** A single professor can only instruct a limited number of courses per term. This is capped at **5 courses** (`MAX_COURSES_PER_PROF = 5`). The system will not allow creation of a new course if the designated instructor (either the professor self-creating, or a Registrar assigning a professor) already has 5 active courses in that term. This check occurs on course creation requests; exceeding the limit yields a `409 Conflict` error `ERR_MAX_COURSES_REACHED` and the course will not be created. Thus, it remains invariant that no professor is listed as instructor on more than 5 courses in any term's dataset.

* **Drop Count & Penalty:** A student is limited in how many courses they can drop per term to prevent abuse of course registration. The **maximum number of drops** (complete withdrawals from courses) allowed for a student in one term is **3** (`MAX_DROP_COUNT_PER_TERM = 3`). The system counts each time a student successfully drops a course (whether they were enrolled or waitlisted doesn't matter for the count). If a student attempts to drop a fourth course in the same term, the action is blocked with `ERR_TOO_MANY_DROPS` and they remain in that course. Additionally, there is an **escalating penalty** on the *third* drop: when a student executes their 3rd drop, the system will apply a **$50.00 penalty fee** (constant `DROP_PENALTY_FEE = 5000` cents) to their tuition ledger. This fee is added to their outstanding balance (i.e. the StudentTuitionLedger is credited by 5000 cents) at the moment of the drop, to discourage excessive withdrawals. The invariant here is that no student's drop count can exceed 3 – the fourth attempt is never allowed – and that the penalty fee is imposed exactly once when the third drop occurs (the ledger will reflect this transaction, and no further penalties are added beyond the first time reaching 3 drops).

* **Waitlist Promotion Guarantee:** If a course has a waitlist (students waiting for a seat) and an enrolled seat becomes available (someone drops or the capacity is increased), the system **automatically moves the longest-waiting student into the course**. This is implemented via an internal event `seat_vacated` that triggers a promotion workflow (see Section 3.4). The invariant is that at any given time, if a course has open seats and a waitlist is not empty, the system will eventually (near-instantly after the triggering drop) enroll the next student from the waitlist. Thus, an available seat never goes unfilled while someone is waiting. This invariant relies on the event-driven rule to always consume a freed seat if possible, maintaining fairness (first-come, first-served order) in waitlist processing. It also ensures consistency between the Course's `available_seats` count and the presence of waitlisted students: you should not observe a course with available_seats > 0 and still people on the waitlist – any discrepancy is resolved by immediate promotions.

* **Derived Data Consistency:** Various response fields are derived from live data and must always reflect the true state. For instance, a Course object may include **`enrolled_count`** (the number of students currently enrolled), **`waitlist_count`** (number of waitlisted students), and **`available_seats`** (calculated as capacity minus enrolled_count). It is invariant that these values are consistent with each other and with the ledgers. `available_seats` is essentially a mirror of the CourseSeatLedger and is always equal to `capacity - enrolled_count`. The system updates these derived fields transactionally with any enrollment or drop changes so that clients never see stale or contradictory data. Similarly, a student's **full-time status** could be derived (e.g. `is_full_time` flag if enrolled credits ≥ 12), and if present, it is always based on the current sum of their enrollments. All such derivations are for convenience and do not introduce independent state; they are recomputed or looked up as needed to guarantee accuracy at response time.

* **Validation Precedence:** A global policy exists for the order in which rules are validated when a request is processed (though not a field value invariant per se, it's a system-wide behavior invariant). The checks proceed from **broad scope to specific**: container-level conditions (like "is the term in a state that allows this?") are evaluated first, then resource existence (e.g. "does this course exist in the term and is it accessible?"), then permission (role/RBAC checks), then fine-grained business rules (capacity, credits, etc.), and finally request format validations. This hierarchy ensures that, for example, if a term is already concluded, an enrollment attempt will fail immediately on that basis (`ERR_TERM_NOT_ACTIVE`) even if other issues (like course being full) could also apply. Lower-level checks are skipped once a higher-level condition fails. This invariant prevents conflicting error scenarios and information leakage: the user only sees the most relevant error for their context, often the one with the highest scope (you must first have permission and a valid container before anything else matters).

By adhering to these invariants, the system maintains logical consistency across all operations. Any violation of the above rules results in a well-defined error (detailed in Section 5) and no state change, preserving the integrity of the academic term's data.

## 3. Business Logic & Workflows

### 3.1 Academic Term Lifecycle & Administration

The **AcademicTerm** resource represents an academic term or semester and acts as the top-level container for all activity. The Registrar is responsible for managing term resources and their lifecycle. When a Registrar **creates a new term** (via `POST /terms`), they must provide a unique term name (e.g. `"Fall 2025"`). The system requires this name to be non-empty and distinct from any existing term's name. On creation, a term's **`state`** is initialized to `PLANNING` (meaning the term exists but student registration has not yet opened). The new AcademicTerm record is stamped with metadata: an `id` (a UUID generated by the system), the `created_by` field set to the Registrar's user ID (linking who created it), a `created_at` timestamp (ISO-8601), and a `revision` number starting at 0 for concurrency control. The successful creation returns **201 Created** with the term's data. Uniqueness of term name is enforced (attempting to reuse a name causes a `ERR_TERM_NAME_NOT_UNIQUE` error, as mentioned in invariants), and only a Registrar role can create terms (others get `ERR_UNAUTHORIZED_ROLE`). No two terms share data; each term's ID will scope any subsequent course or enrollment.

Once a term exists in PLANNING, the Registrar can **open the term for enrollment** by transitioning its state. Calling `PATCH /terms/{termId}:open-registration` is allowed only for the Registrar and only when the term is currently in the `PLANNING` state. This action flips the term's state to **`ENROLLMENT_OPEN`**, indicating that students may now begin enrolling in courses (and professors may publish courses for enrollment). The API will validate the term's current state before allowing this transition: if the term was not in PLANNING (e.g. if it's already open or closed), the request is rejected with a conflict error (`ERR_TERM_NOT_ACTIVE`, capturing "wrong state for opening"). A term not found or not accessible will of course yield `ERR_TERM_NOT_FOUND` (404). On success, the term's `state` is updated to ENROLLMENT_OPEN and the term's `revision` is incremented by 1 to record the state change. No other immediate side-effects occur at the moment of opening (courses remain in whatever state they were; typically many will be in draft until explicitly published).

After some period of enrollment being open (during which courses can be published and students register), the Registrar will eventually **close the enrollment period** with `PATCH /terms/{termId}:close-registration`. This moves the term's state from ENROLLMENT_OPEN to **`ENROLLMENT_CLOSED`** (again a restricted operation: only the Registrar can invoke it, and it only succeeds if the term is currently open). Closing registration signifies that classes are starting and no new student-initiated enrollments or drops are allowed going forward. The system enforces at this point that students cannot self-enroll or drop courses anymore (any attempts would hit errors like `ERR_REGISTRATION_CLOSED` as described in invariants). As part of the closing action, there is a cascade update: **all courses that were in `OPEN` state are transitioned to `IN_PROGRESS`** automatically. This models that once registration is closed, courses move into an in-session state (active classes). The API internally iterates through each course in the term that is still open and flips its state to in-progress, recording an audit log for each. Students on waitlists remain on waitlists (since the course is now in progress with possibly some still waiting, but no new additions will occur since registration is closed). This mass transition ensures consistency: after closing registration, no course is left in the OPEN state. The term's `revision` increments, and a 200 OK response with the updated term is returned.

Finally, at the end of the term, the Registrar **concludes the term** via `PATCH /terms/{termId}:conclude`. This can only be done after registration has been closed (the term must be in ENROLLMENT_CLOSED state to proceed). Concluding a term transitions its state to **`CONCLUDED`** and triggers a final cascade of changes: all courses that are still `IN_PROGRESS` are marked as **`COMPLETED`**, and any enrollments that are still active (ENROLLED or WAITLISTED) are finalized. Specifically, students who remained on a waitlist and never got a seat by term's end will be marked as `DROPPED` (they did not complete the course), and those actively enrolled are marked `COMPLETED` with finality. This ensures that when a term is concluded, there are no dangling in-progress items: every course has either been completed or canceled, and every enrollment is either completed or dropped. After this point, the entire term's data becomes read-only – the invariant (no modifications after conclusion) is enforced by rejecting any further mutating requests with `ERR_TERM_CLOSED` or similar errors, treating the term as an archived record. Attempting to conclude a term that is still open for enrollment (not yet closed) is not allowed; the system will respond with an error indicating the prerequisite not met (you must close registration first). A successful conclude operation returns the updated term in CONCLUDED state. It also increments the term's revision and typically logs a term conclusion event for auditing.

Throughout these term workflows, **optimistic locking** is used: the `open-registration`, `close-registration`, and `conclude` calls all expect the caller to provide the current `revision` of the term in the request (for example, as part of the body or precondition header). If the revision does not match (meaning someone else modified the term's state in between, e.g., two admins accidentally try to open the term at the same time), the request fails with `ERR_REV_CONFLICT` without making changes. This ensures that term state changes are applied consistently and intentionally.

### 3.2 Course Lifecycle Management

Courses represent the individual classes offered within a term. A **Course** has attributes including a unique `id` (UUID), a `code` (like "CS101") which is a short alphanumeric identifier for the course, a textual `title` and `description`, a credit value, a seat `capacity`, and an associated `professor_id` (the instructor). Courses also have a `state` field that follows a well-defined **Course Lifecycle** state machine: initially **`DRAFT`**, then **`OPEN`** (once published for enrollment), possibly **`IN_PROGRESS`** (after term enrollment closes and classes start), and finally either **`COMPLETED`** (if the term concluded normally) or **`CANCELLED`** (if the course was terminated early). The `state` controls how the course can be interacted with (e.g. students can only enroll when state is OPEN) and what appears in responses (DRAFT courses are hidden from students, etc.). The state machine enforces strict transition rules: courses cannot skip states (e.g., **OPEN cannot transition directly to COMPLETED** – it must pass through IN_PROGRESS when the term's enrollment closes), and terminal states are irreversible (a **COMPLETED or CANCELLED course cannot be reopened** or transitioned to any other state). Any attempt to perform an illegal state transition results in `ERR_ILLEGAL_COURSE_STATE_TRANSITION` (409 Conflict).

**Creating a Course:** Either a Professor or the Registrar can create a new course in a term by calling `POST /terms/{termId}/courses`. The request body must include key details about the course: a `code` (2-4 uppercase letters followed by 3 digits, such as "MATH101"), a `title` (non-empty, up to 100 characters), an optional longer `description` (up to 1000 characters), the number of `credits` (an integer, 1 to 5 range), and the maximum `capacity` of students (a positive integer indicating how many seats are available in the class). If the caller is a Registrar, they also need to specify a `professor_id` in the body to designate an instructor for the course; the given professor_id must correspond to an existing user with role Professor, otherwise the creation fails with an error (`ERR_INVALID_INSTRUCTOR` if the ID is not a valid professor in the system). If the caller is a Professor, they are implicitly creating the course for themselves – in this case, they **either omit `professor_id` or must match it to their own user ID**. Any attempt by a professor to create a course and assign a different instructor (i.e. professor_id not equal to the authenticated creator) is rejected as a conflict (`ERR_FIELD_CONFLICT`), since professors cannot create courses on behalf of others. In either case, on success, the new course is created in the **DRAFT** state (meaning it's not yet visible to students for enrollment). The response returns **201 Created** with the course's data. The course's `id` is generated by the system, and `professor_id` is set to either the provided value (Registrar's case) or the creator's ID (Professor's case). The `state` is DRAFT and an initial `enrolled_count` and `waitlist_count` would both be 0 (implicitly, since no enrollments yet). Key validations at creation include: the course `code` must conform to the required pattern and be unique within this term (`ERR_INVALID_COURSE_CODE` for a bad format, or `ERR_COURSE_CODE_NOT_UNIQUE` if that code is already taken in the term); the `title` cannot be empty or exceed length limits (`ERR_INVALID_FIELD_LENGTH` if so); `credits` must be within 1–5 (`ERR_INVALID_CREDITS` if outside); and `capacity` must be a positive integer between 1 and 500 inclusive (capacity cannot be 0 or negative, and values exceeding 500 are rejected with `ERR_INVALID_CAPACITY`). Additionally, the system checks the professor's course load: if the professor already teaches 5 courses in this term, creating another is blocked with `ERR_MAX_COURSES_REACHED`, enforcing the invariant on course count per professor. Furthermore, the course creation request must specify its **`delivery_mode`**, which can be `IN_PERSON`, `ONLINE`, or `HYBRID`. This choice dictates which conditional fields are required and allowed:
*   **`IN_PERSON`**: Requires a physical `location` (non-empty string). Providing an `online_link` for this mode is a conflict.
*   **`ONLINE`**: Requires a valid `online_link` (URL). Providing a `location` for this mode is a conflict.
*   **`HYBRID`**: Requires at least one of `location` or `online_link` to be provided. Both are permitted.
Failure to provide a required field for a given mode (e.g., `'IN_PERSON'` without a `location`) will result in an error (`ERR_CONDITIONAL_FIELD_REQUIRED`). Providing a conflicting field (e.g., `location` for an `ONLINE` course) will result in a different error (`ERR_FIELD_CONFLICT`). All these checks ensure the new course adheres to academic rules and data integrity.

**Publishing a Course:** A course in DRAFT is not yet available for student enrollment. The instructor (Professor who owns it) or the Registrar can **publish** the course by calling `PATCH /terms/{termId}/courses/{courseId}:publish`. Publishing moves the course from DRAFT to **OPEN** state, meaning students can now see it in listings and attempt to enroll. This operation triggers important side effects: upon a successful publish, the system **initializes the Course's seat ledger entry** – setting `seats_available = capacity` – because the course is now open for enrollment and all seats are initially free. The publish action is only allowed under certain conditions: (a) The course must currently be in DRAFT (if it's already published or cancelled, you cannot publish again, and the API returns `ERR_COURSE_WRONG_STATE`). (b) The AcademicTerm containing the course must be in ENROLLMENT_OPEN state (if the term's not accepting enrollments, you also cannot make a course available – the call fails with `ERR_TERM_NOT_ACTIVE`). (c) The caller must be authorized: if a Professor is calling, they must be the instructor of that course (if not, they get `ERR_NOT_INSTRUCTOR`); the Registrar can publish any course. If all checks pass, the course's state is set to OPEN, the `published_at` timestamp (if such a field exists implicitly) would be recorded, and its `revision` is incremented (optimistic lock for course records). The response returns the updated course (now with state OPEN). From this point forward, the course appears in student-facing **GET** results (provided the term is open), and students may enroll according to the rules. If by any chance there were enrollments created in the interim (unlikely since students can't enroll in a draft), they would now count; but typically no enrollment exists until open. The ledger now tracks seats, so any enrollment will decrement `seats_available`. (If publish was called when term wasn't open, as mentioned, it's rejected – meaning professors cannot accidentally open their course early unless the Registrar has opened the entire term for registration.)

**Course Listing & Details (Visibility by Role):** Once courses are in various states, different users retrieving course data will see different things. For example, a `GET /terms/{termId}/courses` (list all courses in term) is open to all roles but with filtering: Students by default only see courses that are published or in-progress/completed – DRAFT courses (and any that were canceled) are hidden from them. Each course entry in the list for a student will include basic fields like code, title, credits, and instructor name/ID (so they know who teaches it), as well as some indication of availability (e.g. how many seats left). However, students do **not** see the raw `enrolled_count` or `waitlist_count` numbers, nor the full roster of a course in any listing. Instead, a derived field like `available_seats` may be shown to students to indicate if a course has space (this can be computed as capacity minus enrolled count, or directly from the ledger). Professors, when listing courses, will see *all their own courses* regardless of state (so they can view even their DRAFT courses that students can't see) as well as other professors' published courses (though with limited info). For their own courses, professors get more detail – they can see how many students are enrolled or waitlisted in the list view. The Registrar's view is unrestricted: listing courses as Registrar returns every course in the term, including drafts and cancelled ones, along with all details (counts, rosters, etc.). The list API also supports query filters (for admin convenience) such as `?state=DRAFT` or `?professor_id=<X>` so that, for example, a Registrar could list all draft courses or all courses taught by a certain professor.

When fetching a specific course via `GET /terms/{termId}/courses/{courseId}`, similar role-based data shaping occurs. If a Student requests a course: they must only ever get data if the course is effectively public to them. If the course is DRAFT or CANCELLED and the student is not involved in it, the API will pretend it doesn't exist (returning 404 `ERR_COURSE_NOT_FOUND`). If it's OPEN/IN_PROGRESS/COMPLETED, the student will get the course details (code, title, description, credits, instructor info, capacity, etc., basically the syllabus information) and some contextual flags: if that student is **enrolled** or **waitlisted** in the course, the response will include a boolean like `is_enrolled: true` or `is_waitlisted: true` to explicitly indicate the student's status in this course. Additionally, if the student is enrolled and the course has concluded with a grade, the student's own grade might be included in their view of their enrollment (the `Enrollment.grade` field) – but students will never see other students' grades or identities. Students also do not see the total enrolled_count or waitlist_count fields in the course detail, since that is considered extraneous or sensitive (though they can infer fullness from available_seats). If a Professor requests a course: if it's *their* course, they get the full details including internal stats and the roster of enrollments. The course representation for the instructor may embed an array of `enrollments` – each containing at least each student's ID and enrollment state (possibly including grades if completed). This allows the professor to see who is in the class and track progress. If a professor requests a course that they do **not** teach and that course is not published (draft), it will appear as not found (similar treatment as for a student). If it's published and not theirs, they'll essentially see the student-level view (no roster). The Registrar requesting any course will always get everything – no field is hidden from the Registrar. They see full rosters, counts, and even financial info relating to the course (e.g. they could compute total tuition generated by the course from the enrollments, though this is not a direct field). This differential data return is a crucial aspect of the business logic: **the API tailors output based on role and ownership**, ensuring privacy and alignment with each actor's need-to-know.

**Cancelling a Course:** Occasionally a course might need to be terminated (for instance, low enrollment or other administrative reason) before the term concludes. The instructor or Registrar can perform **course cancellation** via `PATCH /terms/{termId}/courses/{courseId}:cancel`. This transitions the course's state to **CANCELLED** and triggers significant side effects on related enrollments. Only certain states can be cancelled: a course in DRAFT, OPEN, or IN_PROGRESS is eligible to cancel (a completed course cannot be cancelled retroactively, and obviously a course already cancelled can't be cancelled again). If the course is already completed or already cancelled, the request is invalid (`ERR_COURSE_WRONG_STATE`). Authorization is similar to publishing: a Professor can cancel only their own course, otherwise `ERR_NOT_INSTRUCTOR` applies; the Registrar can cancel any course. Upon cancellation, the course is considered closed for good – students should no longer see it (to students not involved, it may disappear/404 as if it never existed, to avoid confusion). Internally, **all enrollments associated with that course are immediately transitioned to `DROPPED`** because the course will not continue. This means if students were enrolled, they are now removed from the course; if they were waitlisted, that waitlist is essentially cleared. The business logic for cancellation will iterate through each enrollment of the course and mark it dropped, likely logging each drop event. Importantly, ledger adjustments accompany this: for each student who was actively enrolled (with a seat), the system frees the seat and refunds tuition – effectively similar to each of those students dropping the course. The CourseSeatLedger for that course will be reset (in fact, once cancelled, the concept of available seats is moot, but any in-progress ledger entries might be adjusted), and each student's tuition ledger is debited by the course cost (so they don't owe money for a course that got cancelled). Waitlisted students who never had a seat don't require a refund since they hadn't been charged. These details are handled in the Drop workflow (Section 3.4), but initiated in bulk here. The cancel operation returns the updated course (now with `state: CANCELLED`). Enrollments typically are not individually returned in this response (to keep it simple), but if a client were to query those enrollments after, they would find them in DROPPED state. From a student's perspective, a course that was cancelled may simply vanish: if they try to retrieve it, they'll get a 404 (with `ERR_COURSE_NOT_FOUND` to avoid exposing cancelled status) unless they were enrolled, in which case they might see their enrollment record marked dropped. Professors and Registrars will know it's cancelled. Cancellation is a sensitive operation with no direct client-specified body fields (just an endpoint trigger) – the system handles the ramifications automatically according to the rules.

### 3.3 Enrollment Process & Waitlist Handling

Enrolling students into courses is the core workflow for Students and one of the more complex areas due to the interplay of capacities, waitlists, and credit limits. An **Enrollment** represents a student's participation in a specific course within the term. Each Enrollment has its own `id` (UUID), references the associated `course_id` and `student_id`, and has a `state` which can be **ENROLLED** (active seat in the course), **WAITLISTED** (queued for a seat), **DROPPED** (the student was in or waiting for the course but has withdrawn), or **COMPLETED** (finished the course). Initially, when an enrollment is created, its state will be ENROLLED or WAITLISTED depending on seat availability. The Enrollment also tracks a `created_at` timestamp, and possibly a `revision` for concurrency (especially if we allow drop operations with revision checks to avoid double-drops). The enrollment state machine prohibits certain transitions to maintain data integrity: **WAITLISTED cannot transition directly to COMPLETED** (a waitlisted student who never gets a seat is marked DROPPED at term conclusion), and **DROPPED enrollments cannot be re-enrolled** (transitioning from DROPPED to ENROLLED is forbidden – a new enrollment record would be required). Attempts to perform these illegal transitions trigger `ERR_ILLEGAL_ENROLLMENT_STATE` (409 Conflict).

**Creating/Posting an Enrollment (Student Registration):** The endpoint `POST /terms/{termId}/courses/{courseId}/enrollments` is used by a Student to enroll themselves in a course, or by the Registrar to enroll a specified student (e.g. an admin override to place a student in a class). Professors generally do not call this endpoint (they have no authority to enroll students via the API, except if they also hold Registrar rights). The act of enrollment is subject to many validations:

* **Term and Course State:** The target course must exist in the specified term and must be in a state that allows enrollment. If the `courseId` provided does not correspond to an existing course under that term (or is a draft/cancelled course invisible to the student), the server returns `ERR_COURSE_NOT_FOUND` (404). If the course exists but is not currently open for enrollment (i.e. course.state is not OPEN), the request fails with `ERR_COURSE_WRONG_STATE` or a more specific `ERR_COURSE_NOT_OPEN` (409 Conflict) indicating you can't enroll in this course right now. Likewise, if the term itself is not accepting enrollments (term.state is not ENROLLMENT_OPEN), the attempt is blocked with `ERR_REGISTRATION_CLOSED` (if term closed or concluded). These ensure the context is correct (matching the invariants on term/course state gating).

* **Role and Self-Enroll vs. Admin:** The **RBAC rules** enforce that a Student can only create an enrollment for themselves. The `student_id` of the new enrollment will implicitly be the authenticated student's own ID. In fact, if a Student provides a body with some other `student_id`, it's not allowed – the system either ignores it or throws `ERR_FIELD_CONFLICT` or `ERR_FORBIDDEN`. Typically, a Student wouldn't include any body payload (the act of POSTing to their own enrollments is enough). In contrast, when a Registrar uses this endpoint to enroll a student, they **must** provide a JSON body like `{ "student_id": "<targetStudentId>" }` to indicate which student to enroll (because the Registrar themselves is not the one taking the course). The provided student_id is verified: it must belong to an existing user with role Student in that term context (if not, `ERR_STUDENT_NOT_FOUND` is returned). If a Student user somehow tried to specify a student_id in the path or body that isn't them, that's a forbidden action (they can't impersonate others; the check would result in a 403 error, likely captured by `ERR_FORBIDDEN`). Summarily, *Students can only enroll themselves*, while *Registrars can enroll anyone* (with proper identification of the target).

* **Duplicate Enrollment Check:** The system checks if the student is already enrolled or waitlisted in the target course. A student cannot have two enrollment records for the same course. If an enrollment (of any active state other than dropped) already exists for that student-course pair, the new attempt is rejected with `ERR_ALREADY_ENROLLED` (409 Conflict). This covers cases like a student accidentally clicking enroll twice, or an admin trying to enroll someone who is already in the class or on its waitlist.

* **Seat Availability and Waitlisting:** The **CourseSeatLedger** comes into play to determine if the student gets a seat or goes onto a waitlist. The logic is: if the course's `seats_available` > 0 at the moment of the request, then a seat is free and the student will be **ENROLLED** (given the seat). If `seats_available` is 0 (course is full), the student will be placed in **WAITLISTED** state. Notably, reaching a full course is not considered an error condition in this design – it's a handled outcome (the enrollment is still created, just in waitlist status). So unlike some systems, we do not throw `ERR_COURSE_FULL` when a student tries to enroll in a full course; instead we accept the request but mark them waitlisted. (An error `ERR_COURSE_FULL` would mainly arise if an attempt was made to force-add beyond capacity without waitlisting, which normal usage doesn't do. In essence, a Student won't see an error for a full course, they'll just see they got waitlisted, whereas maybe a misbehaving client that bypasses our logic could trigger such an error.) After determining the seat situation, the Enrollment record is created accordingly: state = ENROLLED if they got a seat, or WAITLISTED if not. The new enrollment's `id` is generated and it's associated with the course and student.

* **Credit Load Check:** Before finalizing enrollment, if the acting user is a Student, the system sums up the credits of all courses that student is currently actively enrolled in (in the same term) and adds the credits of this prospective course. If that total would exceed the allowed maximum (18 credits), the enrollment is blocked with `ERR_CREDIT_LIMIT_EXCEEDED`. This ensures no student surpasses the credit cap through self-service. This check is skipped if the caller is Registrar (i.e. the Registrar can enroll a student even if it overloads their schedule, as an override). The credit count only considers enrollments with state ENROLLED (not waitlisted, since waitlisted courses don't count toward credit load until the student actually gets in). So, for example, a student with 15 credits enrolled who tries to enroll in a 4-credit course will be denied (15+4 > 18), whereas being waitlisted might not trigger the denial unless we foresee them eventually getting in – but our rule is applied at request time considering this course as if enrolled. Registrar's override means the rule doesn't apply to their actions: they can put that student in the course regardless (the student will end up with, say, 19 credits, which is ordinarily against policy but allowed due to admin intervention).

If all validations pass, the system proceeds to **create the Enrollment**. For an enrolled student (not waitlisted), the following actions occur atomically: the **CourseSeatLedger is debited by 1** (reducing available seats count by one), and the **StudentTuitionLedger is credited** with the tuition cost of the course (in cents, calculated as course.credits * COST_PER_CREDIT). These updates reflect that the student now occupies a seat (one less seat available) and owes tuition for the course. If the student was placed on the waitlist, those ledger changes are skipped: we do **not** decrement the seat count (since they didn't take a seat) and we do **not** charge tuition yet. Waitlisted enrollments essentially reserve a *position* but not a seat or financial obligation (aside from maybe a registration fee, which is not in scope here). In either case, the new enrollment's data (id, course_id, student_id, state, etc.) is returned with a **201 Created** status. The client can tell from the `state` field in the response whether the student is enrolled or waitlisted. If an enrollment is waitlisted, the client might also infer their position by listing enrollments, but at least they know they're not yet in the class. The system sets the enrollment's `created_at` timestamp and initializes its `revision`. If this is the first enrollment for that student in the term, the StudentTuitionLedger entry for that student is automatically created (starting at balance 0) before applying any charges, so that ledger always exists once a student has any enrollment.

After a successful enrollment, if it was waitlisted, the student must wait for a seat to open. There's no immediate additional action; however, the **waitlist promotion mechanism** (see Section 3.4) will take over when someone drops and a seat frees up. The student could also proactively drop from the waitlist if they change their mind (using the same drop endpoint).

It's important to note the error hierarchy during enrollment: for example, if a student tries to enroll in a course that doesn't exist or isn't open, the error about course/term state will come out before the credit limit check ever happens, as described in Section 2.6 (Validation Precedence). Also, if an unauthorized role calls this endpoint (say a Professor trying to enroll a student – which they shouldn't, as no such body param is allowed for them), the request would be rejected with a 403 error (`ERR_UNAUTHORIZED_ROLE`) before any of these other validations.

### 3.4 Drops, Withdrawals & Seat Management

Dropping an enrollment refers to a student withdrawing from a course they were enrolled in (or removing themselves from a waitlist). This can be initiated by the student or done administratively by a professor or Registrar. The endpoint `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop` handles all cases of dropping. The business logic carefully checks the context and applies the correct outcomes:

**Permissions & Conditions to Drop:** A student can only drop *their own* enrollment. That means the `{enrollmentId}` they target must belong to them (the server will verify that `enrollment.student_id == auth.id` for a student caller). If not (e.g. a student tries to drop someone else's enrollment ID by tinkering with the URL), the API will reject with `ERR_FORBIDDEN` (403). Professors can drop enrollments too, but only for courses they teach – essentially they can remove a student from their class. If a professor attempts to drop an enrollment in a course where they are not the instructor, it fails with `ERR_NOT_INSTRUCTOR`. The Registrar can drop any student from any course (assuming the term is active), as they have the override ability. So, allowed actors are: the student themself, that course's professor, or Registrar. If an unauthorized role (or wrong professor) tries, it's forbidden. Additionally, the enrollment must actually exist under that course and term, otherwise a `ERR_ENROLLMENT_NOT_FOUND` (404) is returned – this covers cases where the ID is wrong or doesn't match the course in the path.

There are state constraints on dropping: the enrollment to be dropped should be in **ENROLLED** or **WAITLISTED** state currently. If it's already **DROPPED** or **COMPLETED**, dropping again doesn't make sense and is not allowed (`ERR_ENROLLMENT_WRONG_STATE`). You cannot drop something twice, and you cannot drop a finished course. Similarly, you shouldn't be able to drop after the term is concluded; if for some reason a drop is attempted once the term is over, the system would reject it (using `ERR_TERM_NOT_ACTIVE`). We also wouldn't allow dropping from a course that's already completed (even if the term isn't concluded, say an individual course finished early with grade assigned – not modeled here explicitly, but principle stands).

**Drop Effects and Ledger Updates:** When a drop is executed, the enrollment's state transitions to **DROPPED** (this is final for that enrollment record). The system then performs different actions depending on what the enrollment's previous state was:

* If the enrollment was **ENROLLED** (the student held a seat at drop time): the drop frees up a seat. Therefore, the **CourseSeatLedger is credited by 1** (increasing available seats by one). At the same time, since the student had paid (in terms of balance) for that seat, the system **refunds the tuition**: it *debits the StudentTuitionLedger* by the cost of the course (reducing the student's outstanding balance). The assumption in our design is that any drop during the active term (before completion) entitles the student to a full refund; we are not modeling partial refunds vs no refunds by date, so we keep it simple: drop = no longer owe for that course. So after these operations, the student's ledger will show a decrease in debt equivalent to the credits of the course * cost per credit, and the course's ledger shows an extra seat available. Additionally, because a seat opened up in a course that might have a waitlist, the drop triggers the **seat_vacated event**. The system emits an event indicating course X has a free seat, which asynchronously invokes the waitlist promotion logic (detailed shortly). This promotion is not done inline in the drop endpoint response (to keep dropping quick), but it happens immediately after as a side workflow.

* If the enrollment was **WAITLISTED** (the student had no seat): dropping it simply removes them from the waitlist. There is no seat ledger impact (since they didn't occupy a seat) and no tuition to refund (no charge was made). Essentially the waitlist count for that course will decrement by one. If they were first in line, the next person's position shifts up, but since a seat wasn't freed (a waitlisted person leaving doesn't open a seat, it just shortens the waitlist), no promotion event is triggered by this alone.

After handling the above, the system checks if the drop is initiated by the student themselves (auth.role = Student dropping their own enrollment). If so, it will update the student's drop count for the term. This count is tracked internally (not necessarily as a field on the student, but the system can compute how many DROPPED enrollments this student now has in this term). It then applies the **drop penalty** if appropriate: if this drop is the student's *third* drop of the term (meaning before dropping, they had 2 prior drops, and now this makes 3), the system will **assess the penalty fee** by adding a charge of 5000 cents to their tuition ledger. Concretely, it credits the StudentTuitionLedger with 5000 (increasing their balance) to represent the fine. A log or note may be recorded about this penalty application. If the drop would have been the fourth, that request wouldn't have been allowed in the first place (blocked by validation), so we don't handle fourth drops here – those are stopped with `ERR_TOO_MANY_DROPS` earlier. The penalty is only on the third drop event. If an administrative user (professor or Registrar) drops a student, we likely do not count that against the student's drop count for penalty purposes (the spec implies the count is specifically for student-initiated drops). But if the student asked the professor to drop them, it's a gray area – by the letter, it's who initiates. We assume drop count increments only when auth.role = Student.

Once these updates are done, the enrollment resource is now marked DROPPED. The API returns a **200 OK** with the updated enrollment object (state now "DROPPED"). The client (if it's the student) knows they are no longer in the course from this state. Any side effects like the waitlist promotion might happen just after this response or concurrently, but from the API caller's perspective, their drop succeeded.

**Automatic Waitlist Promotion (Event-Driven):** When a seat opens up in a course (due to a drop or perhaps an admin increasing capacity, though the latter is not explicitly an endpoint here), the system handles it via an asynchronous **event** called `seat_vacated`. The **rule** listening for this event will find the first student on the waitlist for that course and promote them. In narrative form: as soon as a drop frees a seat, the system identifies the earliest-added WAITLISTED enrollment for that course (the waitlist is effectively a queue ordered by `created_at`). That student's enrollment is then **transitioned to ENROLLED** – they are officially in the course now. The CourseSeatLedger, which had been credited when the seat was freed, is now debited by 1 again (consuming the seat for the waitlisted student), and the StudentTuitionLedger for that student is charged for the course cost (since now they have to pay tuition as they got a seat). Essentially, the promotion automates what would have happened had that student been enrolled initially. This promotion process is logged as a `waitlist_promoted` event for auditing. If multiple seats were freed (say a drop of a student who had been enrolled and maybe one who was waitlisted, though waitlisted drop doesn't free a seat, but imagine capacity increase scenario or multiple drops concurrently), the rule will promote one student and then check again – it can loop or re-trigger if more seats are still open and more waitlistees remain. The invariant is it will continue until either no waitlisted students remain or no free seats remain. This ensures a fair FIFO promotion. Importantly, this occurs in the background – from a user perspective, after dropping, the next waitlisted student just finds themselves enrolled (they could be notified out-of-band or will see it on next check). The design avoids needing clients to constantly poll: once they're waitlisted, the onus is on the system to flip them to enrolled when possible. (Our spec doesn't detail a notification mechanism, but one could assume an email or they check via GET and see state changed to ENROLLED). If the promotion fails for some internal reason (shouldn't, unless maybe ledger missing – but we ensure ledger exists by creating on first enrollment), it would be an internal error. Generally it should always succeed given the invariants in place.

To summarize the drop workflow, it covers voluntary withdrawal by students and administrative removals, with the necessary business rules: drop permissions by role, no dropping beyond limits (with an enforced max count and penalty at threshold), seat and tuition adjustments, and a trigger to maintain waitlist continuity. This interplay of synchronous API action (drop) and asynchronous effect (promotion) exemplifies the system's interactive rules where one action cascades to another.

### 3.5 Tuition Payments & Ledger Settlement

Managing tuition payments is an essential financial aspect of the system. Students accumulate charges for each course they are enrolled in, reflected in the StudentTuitionLedger. The endpoint `POST /terms/{termId}/students/{studentId}:pay` allows payments to be recorded against a student's outstanding balance.

**Payment Authorization:** Only two roles can record a payment: the Student themselves (paying their own bill) or the Registrar (accepting a payment on behalf of a student). A student is only permitted to pay for their **own** account, so the `{studentId}` in the path must match the authenticated student's ID if a student calls this. If a student tries to call this endpoint with someone else's ID, it is forbidden (`ERR_FORBIDDEN`). The Registrar can specify any student's ID (presumably after collecting a payment offline or adjusting a balance) – but even the Registrar must ensure the target ID belongs to a valid Student in that term context (`ERR_STUDENT_NOT_FOUND` if not). Professors do not use this endpoint (they have no reason to handle payments).

**Payment Request and Validation:** The request body should contain an `amount` field indicating the payment amount in cents (integer). Before applying the payment, the system performs checks: The `amount` must be a positive, non-zero value. If `amount <= 0` or not provided, that's a bad request (`ERR_INVALID_PAYMENT_AMOUNT`, 422 Unprocessable), since you cannot pay nothing or a negative amount. Next, the system ensures the StudentTuitionLedger for that student exists. If the student has not had any enrollment and thus no ledger yet, the system will transparently initialize one with balance 0 for them (so that we have an entry to update). This isn't an error case; it's an automatic setup. Then, crucially, the payment amount is checked against the student's current balance: the system will not accept an overpayment that would drive the balance below 0. If `amount` is greater than what the student currently owes, the request is rejected with `ERR_OVERPAY_NOT_ALLOWED` (422 Unprocessable). This prevents scenarios where someone accidentally pays more than their outstanding balance (which could create negative balance or require issuing a refund). The client is expected to only pay up to what is due.

**Applying the Payment:** Once validated, the payment is processed by **debiting the StudentTuitionLedger by the given amount**. Debiting in ledger terms means subtracting from the balance (since the student's owed amount goes down when they pay). For example, if the student owed $300 (30000 cents) and pays $100 (10000 cents), the ledger's balance_cents will decrease to 20000. This operation is transactional and ensures the ledger cannot go below 0 due to our prior check. After the debit, the system may check if the balance is now exactly zero – if so, it might log an event like `tuition_fully_paid` for audit or trigger any completion logic (not much in this spec beyond logging, since nothing else depends on being fully paid, but it's a noteworthy state). There is no further side effect on other resources; payment doesn't affect enrollments or course states, it's purely financial. The endpoint returns a **200 OK** with a response that typically includes the updated balance information. For instance, it might return the student_id, term_id, and the new `balance` (or `new_balance`) after the payment. This confirms to the caller how much the student now owes.

The payment system respects the invariants: no negative balances, and it only operates within one term's ledger (paying a term's balance doesn't touch any other term). The error conditions are straightforward and included in the Error Catalogue. Also, the optimistic concurrency concept applies here as well: if we considered two simultaneous payments, having a ledger revision check could be relevant, but typically ledger operations might be simpler. 

In summary, the payment workflow allows reducing a student's financial balance safely and with proper validation, complementing the charges applied during enrollments and drops. The Registrar's involvement means payments can be recorded even if a student isn't directly calling the API (e.g. if they mailed a check, the Registrar can post the payment to the system). After payments, students can potentially enroll in new courses if the only thing holding them back was a financial hold (though financial holds aren't explicitly modeled here beyond not overshooting payments).

## 4. Endpoint Definitions

### 4.1 Term Management Endpoints

* **POST `/terms`** – Create a new academic term.
* **GET `/terms/{termId}`** – Retrieve details of an academic term (state, etc.).
* **PATCH `/terms/{termId}:open-registration`** – Open student registration for the term (transition term from PLANNING to ENROLLMENT_OPEN).
* **PATCH `/terms/{termId}:close-registration`** – Close the enrollment period (transition term from ENROLLMENT_OPEN to ENROLLMENT_CLOSED).
* **PATCH `/terms/{termId}:conclude`** – Conclude the term (finalize term to CONCLUDED, mark all courses and enrollments as completed).

### 4.2 Course Management Endpoints

* **POST `/terms/{termId}/courses`** – Create a new course in the term (instructor provides course details; starts as DRAFT).
* **GET `/terms/{termId}/courses`** – List courses in the term (results filtered by role: e.g. students see published courses).
* **GET `/terms/{termId}/courses/{courseId}`** – Get detailed info on a specific course (including roster or contextual fields based on role).
* **PATCH `/terms/{termId}/courses/{courseId}:publish`** – Publish a draft course (make it OPEN for enrollment, initializing its seat ledger).
* **PATCH `/terms/{termId}/courses/{courseId}:cancel`** – Cancel an active or draft course (state -> CANCELLED, drop all its enrollments).

### 4.3 Enrollment & Waitlist Endpoints

* **POST `/terms/{termId}/courses/{courseId}/enrollments`** – Enroll in a course (student self-enroll or Registrar enrolls a student; may result in waitlist if course is full).
* **GET `/terms/{termId}/courses/{courseId}/enrollments`** – List all enrollments (roster) for a course (accessible to course's professor or Registrar; students cannot list rosters).
* **GET `/terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}`** – Get a specific enrollment record (student can fetch their own enrollment, professor can fetch one from their course, Registrar any).
* **PATCH `/terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop`** – Drop or withdraw an enrollment (student drops self, professor/Registrar remove a student; frees seat, triggers waitlist promotion, applies drop rules).

### 4.4 Financial Operations Endpoints

* **POST `/terms/{termId}/students/{studentId}:pay`** – Record a tuition payment for a student (student pays their own balance or Registrar records a payment; reduces ledger balance).

## 5. Error Code Catalogue

The following table enumerates all defined error identifiers (`error_id`) and their associated HTTP status codes. These errors are returned in the unified error response format when requests fail specific validations or business rules. Each `error_id` is unique to a particular failure condition as described in this document.

| **Error ID**                                 | **HTTP Status**           |
| -------------------------------------------- | ------------------------- |
| `ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER` | 400 Bad Request           |
| `ERR_UNAUTHORIZED_ROLE`                      | 403 Forbidden             |
| `ERR_NOT_INSTRUCTOR`                         | 403 Forbidden             |
| `ERR_PERMISSION_DENIED`                      | 403 Forbidden             |
| `ERR_TERM_NOT_FOUND`                         | 404 Not Found             |
| `ERR_COURSE_NOT_FOUND`                       | 404 Not Found             |
| `ERR_ENROLLMENT_NOT_FOUND`                   | 404 Not Found             |
| `ERR_STUDENT_NOT_FOUND`                      | 404 Not Found             |
| `ERR_TERM_CLOSED`                            | 404 Not Found             |
| `ERR_INVALID_ID_FORMAT`                      | 400 Bad Request           |
| `ERR_INVALID_COURSE_CODE`                    | 400 Bad Request           |
| `ERR_COURSE_CODE_NOT_UNIQUE`                 | 409 Conflict              |
| `ERR_INVALID_FIELD_LENGTH`                   | 400 Bad Request           |
| `ERR_INVALID_CREDITS`                        | 400 Bad Request           |
| `ERR_INVALID_CAPACITY`                       | 400 Bad Request           |
| `ERR_INVALID_ENUM_VALUE`                     | 400 Bad Request           |
| `ERR_MISSING_REQUIRED_FIELD`                 | 400 Bad Request           |
| `ERR_CONDITIONAL_FIELD_REQUIRED`             | 400 Bad Request           |
| `ERR_FIELD_CONFLICT`                         | 400 Bad Request           |
| `ERR_UNKNOWN_FIELD`                          | 400 Bad Request           |
| `ERR_COURSE_WRONG_STATE`                     | 409 Conflict              |
| `ERR_ENROLLMENT_WRONG_STATE`                 | 409 Conflict              |
| `ERR_TERM_NOT_ACTIVE`                        | 409 Conflict              |
| `ERR_REGISTRATION_CLOSED`                    | 409 Conflict              |
| `ERR_COURSE_FULL`                            | 409 Conflict              |
| `ERR_CAPACITY_EXCEEDED`                      | 409 Conflict              |
| `ERR_ALREADY_ENROLLED`                       | 409 Conflict              |
| `ERR_NOT_ENROLLED`                           | 409 Conflict              |
| `ERR_MAX_COURSES_REACHED`                    | 409 Conflict              |
| `ERR_CREDIT_LIMIT_EXCEEDED`                  | 409 Conflict              |
| `ERR_TOO_MANY_DROPS`                         | 409 Conflict              |
| `ERR_ILLEGAL_COURSE_STATE_TRANSITION`        | 409 Conflict              |
| `ERR_ILLEGAL_ENROLLMENT_STATE`               | 409 Conflict              |
| `ERR_FORBIDDEN`                              | 403 Forbidden             |
| `ERR_TERM_NAME_NOT_UNIQUE`                   | 409 Conflict              |
| `ERR_INVALID_INSTRUCTOR`                     | 422 Unprocessable Entity  |
| `ERR_REV_CONFLICT`                           | 409 Conflict              |
| `ERR_INSUFFICIENT_FUNDS`                     | 402 Payment Required      |
| `ERR_OVERPAY_NOT_ALLOWED`                    | 422 Unprocessable Entity  |
| `ERR_INVALID_PAYMENT_AMOUNT`                 | 422 Unprocessable Entity  |
| `ERR_LEDGER_INVALID_OP`                      | 422 Unprocessable Entity  |