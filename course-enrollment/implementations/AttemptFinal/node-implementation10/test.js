// Simple test script to verify the API implementation
const app = require('./src/server');

async function makeRequest(method, path, body, headers = {}) {
  return new Promise((resolve, reject) => {
    const request = require('supertest')(app);
    let req = request[method.toLowerCase()](path);
    
    // Add headers
    Object.keys(headers).forEach(key => {
      req = req.set(key, headers[key]);
    });
    
    // Add body if provided
    if (body) {
      req = req.send(body);
    }
    
    req.end((err, res) => {
      if (err) reject(err);
      else resolve(res);
    });
  });
}

async function runTests() {
  console.log('Running basic API tests...\n');
  
  try {
    // Test 1: Create a term (as registrar)
    console.log('1. Creating a term...');
    const termRes = await makeRequest('POST', '/terms', 
      { name: 'Fall 2025' },
      { 'X-User-ID': 'registrar-1', 'X-User-Role': 'REGISTRAR' }
    );
    console.log(`   Status: ${termRes.status}`);
    console.log(`   Term ID: ${termRes.body.data.id}`);
    const termId = termRes.body.data.id;
    
    // Test 2: Open registration
    console.log('2. Opening registration...');
    const openRes = await makeRequest('PATCH', `/terms/${termId}:open-registration`, 
      { revision: 0 },
      { 'X-User-ID': 'registrar-1', 'X-User-Role': 'REGISTRAR' }
    );
    console.log(`   Status: ${openRes.status}`);
    console.log(`   Term state: ${openRes.body.data.state}`);
    
    // Test 3: Create a course (as professor)
    console.log('3. Creating a course...');
    const courseRes = await makeRequest('POST', `/terms/${termId}/courses`,
      {
        code: 'CS101',
        title: 'Introduction to Computer Science',
        description: 'Basic programming concepts',
        credits: 3,
        capacity: 30,
        delivery_mode: 'IN_PERSON',
        location: 'Room 101'
      },
      { 'X-User-ID': 'prof-1', 'X-User-Role': 'PROFESSOR' }
    );
    console.log(`   Status: ${courseRes.status}`);
    console.log(`   Course ID: ${courseRes.body.data.id}`);
    const courseId = courseRes.body.data.id;
    
    // Test 4: Publish the course
    console.log('4. Publishing the course...');
    const publishRes = await makeRequest('PATCH', `/terms/${termId}/courses/${courseId}:publish`,
      { revision: 0 },
      { 'X-User-ID': 'prof-1', 'X-User-Role': 'PROFESSOR' }
    );
    console.log(`   Status: ${publishRes.status}`);
    console.log(`   Course state: ${publishRes.body.data.state}`);
    
    // Test 5: Enroll a student
    console.log('5. Enrolling a student...');
    const enrollRes = await makeRequest('POST', `/terms/${termId}/courses/${courseId}/enrollments`,
      {},
      { 'X-User-ID': 'student-1', 'X-User-Role': 'STUDENT' }
    );
    console.log(`   Status: ${enrollRes.status}`);
    console.log(`   Enrollment state: ${enrollRes.body.data.state}`);
    const enrollmentId = enrollRes.body.data.id;
    
    // Test 6: List courses (as student)
    console.log('6. Listing courses (as student)...');
    const listRes = await makeRequest('GET', `/terms/${termId}/courses`,
      null,
      { 'X-User-ID': 'student-1', 'X-User-Role': 'STUDENT' }
    );
    console.log(`   Status: ${listRes.status}`);
    console.log(`   Found courses: ${listRes.body.data.length}`);
    
    // Test 7: Check student balance
    console.log('7. Getting course details to see tuition...');
    const courseDetailRes = await makeRequest('GET', `/terms/${termId}/courses/${courseId}`,
      null,
      { 'X-User-ID': 'student-1', 'X-User-Role': 'STUDENT' }
    );
    console.log(`   Status: ${courseDetailRes.status}`);
    console.log(`   Student enrolled: ${courseDetailRes.body.data.is_enrolled}`);
    
    // Test 8: Make a payment
    console.log('8. Making a payment...');
    const paymentRes = await makeRequest('POST', `/terms/${termId}/students/student-1:pay`,
      { amount: 30000 }, // $300 (3 credits * $100)
      { 'X-User-ID': 'student-1', 'X-User-Role': 'STUDENT' }
    );
    console.log(`   Status: ${paymentRes.status}`);
    console.log(`   New balance: $${(paymentRes.body.data.new_balance / 100).toFixed(2)}`);
    
    console.log('\n✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response:', error.response.body);
    }
  }
  
  process.exit(0);
}

// Only run if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };