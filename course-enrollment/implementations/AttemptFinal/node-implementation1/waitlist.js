const { db, COST_PER_CREDIT } = require('./models');

function promoteFromWaitlist(courseId) {
  try {
    // Get course and seat ledger
    const course = db.getCourse(courseId);
    const seatLedger = db.getCourseSeatLedger(courseId);
    
    if (!course || !seatLedger) {
      console.error(`Course or seat ledger not found for course ${courseId}`);
      return;
    }

    // Continue promoting while there are seats and waitlisted students
    while (seatLedger.seats_available > 0) {
      // Get earliest waitlisted enrollment
      const waitlistedEnrollments = db.getWaitlistedEnrollmentsForCourse(courseId);
      
      if (waitlistedEnrollments.length === 0) {
        // No one waiting, stop promotion
        break;
      }

      const nextEnrollment = waitlistedEnrollments[0];
      
      // Promote the student
      db.updateEnrollment(nextEnrollment.id, { state: 'ENROLLED' });
      
      // Debit seat ledger
      db.updateCourseSeatLedger(courseId, -1);
      
      // Credit tuition ledger
      const courseCost = course.credits * COST_PER_CREDIT;
      db.updateStudentTuitionLedger(nextEnrollment.term_id, nextEnrollment.student_id, courseCost);
      
      console.log(`Promoted student ${nextEnrollment.student_id} from waitlist to enrolled in course ${courseId}`);
      
      // Update the seat ledger reference for the next iteration
      // (the database operation already updated the object, but we need the fresh reference)
      const updatedLedger = db.getCourseSeatLedger(courseId);
      if (!updatedLedger) break;
      
      seatLedger.seats_available = updatedLedger.seats_available;
    }
  } catch (error) {
    console.error(`Error promoting from waitlist for course ${courseId}:`, error);
  }
}

module.exports = {
  promoteFromWaitlist
};