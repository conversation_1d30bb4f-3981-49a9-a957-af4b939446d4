const express = require('express');
const cors = require('cors');
const { authMiddleware } = require('./middleware');
const { createErrorResponse } = require('./utils');

// Import route modules
const termRoutes = require('./routes/terms');
const courseRoutes = require('./routes/courses');
const enrollmentRoutes = require('./routes/enrollments');
const paymentRoutes = require('./routes/payments');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Global error handler for malformed JSON
app.use((err, req, res, next) => {
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_JSON',
      'Invalid JSON in request body.'
    ));
  }
  next();
});

// Authentication middleware (applied to all routes)
app.use(authMiddleware);

// API Routes
app.use('/terms', termRoutes);
app.use('/terms/:termId/courses', courseRoutes);
app.use('/terms/:termId/courses/:courseId/enrollments', enrollmentRoutes);
app.use('/terms/:termId/students', paymentRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'University Course Registration API'
  });
});

// 404 handler for unknown routes
app.use('*', (req, res) => {
  res.status(404).json(createErrorResponse(
    'ERR_ENDPOINT_NOT_FOUND',
    `Endpoint ${req.method} ${req.path} not found.`
  ));
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json(createErrorResponse(
    'ERR_INTERNAL_SERVER_ERROR',
    'An internal server error occurred.'
  ));
});

// Start server
app.listen(PORT, () => {
  console.log(`University Course Registration API running on port ${PORT}`);
  console.log(`Health check available at: http://localhost:${PORT}/health`);
  console.log('API Documentation:');
  console.log('  Terms: POST /terms, GET /terms/{id}, PATCH /terms/{id}:open-registration, etc.');
  console.log('  Courses: POST /terms/{id}/courses, GET /terms/{id}/courses, etc.');
  console.log('  Enrollments: POST /terms/{id}/courses/{id}/enrollments, etc.');
  console.log('  Payments: POST /terms/{id}/students/{id}:pay');
  console.log('');
  console.log('Required Headers:');
  console.log('  X-User-ID: {UUID}');
  console.log('  X-User-Role: STUDENT|PROFESSOR|REGISTRAR');
});

module.exports = app;