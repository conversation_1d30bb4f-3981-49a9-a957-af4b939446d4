const { USER_ROLES } = require('./models');
const { createErrorResponse } = require('./utils');

// UUID validation regex pattern
const UUID_PATTERN = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

function validateUUID(id) {
  return UUID_PATTERN.test(id);
}

function authMiddleware(req, res, next) {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  // Check if both headers are present
  if (!userId || !userRole) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'Missing or invalid user context headers. Both X-User-ID and X-User-Role are required.'
    ));
  }

  // Validate UUID format
  if (!validateUUID(userId)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ID_FORMAT',
      'Invalid UUID format for X-User-ID header.'
    ));
  }

  // Validate role enum
  if (!USER_ROLES.includes(userRole)) {
    return res.status(400).json(createErrorResponse(
      'ERR_INVALID_ENUM_VALUE',
      `Invalid user role. Must be one of: ${USER_ROLES.join(', ')}.`
    ));
  }

  // Attach user context to request
  req.user = {
    id: userId,
    role: userRole
  };

  next();
}

// Middleware to validate UUID parameters in routes
function validateUUIDParam(paramName) {
  return (req, res, next) => {
    const id = req.params[paramName];
    if (id && !validateUUID(id)) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_ID_FORMAT',
        `Invalid UUID format for parameter ${paramName}.`
      ));
    }
    next();
  };
}

// Role-based authorization middleware
function requireRole(...allowedRoles) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(createErrorResponse(
        'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
        'User context not found. Ensure authentication middleware is applied.'
      ));
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json(createErrorResponse(
        'ERR_UNAUTHORIZED_ROLE',
        `Access denied. This operation requires one of the following roles: ${allowedRoles.join(', ')}.`
      ));
    }

    next();
  };
}

// Middleware to validate request body against unknown fields
function validateRequestBody(allowedFields) {
  return (req, res, next) => {
    if (req.body && typeof req.body === 'object') {
      const bodyKeys = Object.keys(req.body);
      const unknownFields = bodyKeys.filter(key => !allowedFields.includes(key));
      
      if (unknownFields.length > 0) {
        return res.status(400).json(createErrorResponse(
          'ERR_UNKNOWN_FIELD',
          `Unknown fields in request body: ${unknownFields.join(', ')}.`
        ));
      }
    }
    next();
  };
}

// Middleware to validate pagination parameters
function validatePagination(req, res, next) {
  const limit = req.query.limit;
  const offset = req.query.offset;

  if (limit !== undefined) {
    const limitNum = parseInt(limit, 10);
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 500) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_FIELD_LENGTH',
        'Invalid limit parameter. Must be a number between 1 and 500.'
      ));
    }
    req.query.limit = limitNum;
  } else {
    req.query.limit = 50; // default
  }

  if (offset !== undefined) {
    const offsetNum = parseInt(offset, 10);
    if (isNaN(offsetNum) || offsetNum < 0) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_FIELD_LENGTH',
        'Invalid offset parameter. Must be a non-negative number.'
      ));
    }
    req.query.offset = offsetNum;
  } else {
    req.query.offset = 0; // default
  }

  next();
}

module.exports = {
  authMiddleware,
  validateUUIDParam,
  requireRole,
  validateRequestBody,
  validatePagination,
  validateUUID
};