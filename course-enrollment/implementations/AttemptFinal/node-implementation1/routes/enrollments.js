const express = require('express');
const router = express.Router({ mergeParams: true });
const { 
  db, 
  Enrollment, 
  ENROLLMENT_STATES, 
  MAX_CREDITS_PER_TERM,
  MAX_DROP_COUNT_PER_TERM,
  COST_PER_CREDIT,
  DROP_PENALTY_FEE
} = require('../models');
const { 
  requireRole, 
  validateUUIDParam, 
  validateRequestBody, 
  validatePagination 
} = require('../middleware');
const { 
  createSuccessResponse, 
  createErrorResponse, 
  createPaginatedResponse,
  filterEnrollmentDataByRole
} = require('../utils');
const { promoteFromWaitlist } = require('../waitlist');

// POST /terms/{termId}/courses/{courseId}/enrollments - Enroll in a course
router.post('/',
  requireRole('STUDENT', 'REGISTRAR'),
  validateRequestBody(['student_id']),
  (req, res) => {
    const { termId, courseId } = req.params;
    const { student_id } = req.body;

    // Validate term exists and is open for enrollment
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    if (term.state !== 'ENROLLMENT_OPEN') {
      return res.status(409).json(createErrorResponse(
        'ERR_REGISTRATION_CLOSED',
        'Registration is not currently open for this term.'
      ));
    }

    // Validate course exists and is open for enrollment
    const course = db.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    if (course.state !== 'OPEN') {
      return res.status(409).json(createErrorResponse(
        'ERR_COURSE_WRONG_STATE',
        'Course is not currently open for enrollment.'
      ));
    }

    // Determine student ID
    let targetStudentId;
    if (req.user.role === 'STUDENT') {
      if (student_id && student_id !== req.user.id) {
        return res.status(403).json(createErrorResponse(
          'ERR_FORBIDDEN',
          'Students can only enroll themselves.'
        ));
      }
      targetStudentId = req.user.id;
    } else if (req.user.role === 'REGISTRAR') {
      if (!student_id) {
        return res.status(400).json(createErrorResponse(
          'ERR_MISSING_REQUIRED_FIELD',
          'Student ID is required when Registrar enrolls a student.'
        ));
      }
      // TODO: In a real system, validate that student_id exists and has STUDENT role
      targetStudentId = student_id;
    }

    // Check for duplicate enrollment
    const existingEnrollment = db.getEnrollmentByStudentAndCourse(targetStudentId, courseId);
    if (existingEnrollment && ['ENROLLED', 'WAITLISTED'].includes(existingEnrollment.state)) {
      return res.status(409).json(createErrorResponse(
        'ERR_ALREADY_ENROLLED',
        'Student is already enrolled or waitlisted for this course.'
      ));
    }

    // Check credit limit for students (Registrar can override)
    if (req.user.role === 'STUDENT') {
      const activeEnrollments = db.getActiveEnrollmentsForStudent(termId, targetStudentId);
      const currentCredits = activeEnrollments.reduce((sum, enrollment) => {
        const enrollmentCourse = db.getCourse(enrollment.course_id);
        return sum + enrollmentCourse.credits;
      }, 0);

      if (currentCredits + course.credits > MAX_CREDITS_PER_TERM) {
        return res.status(409).json(createErrorResponse(
          'ERR_CREDIT_LIMIT_EXCEEDED',
          `Student would exceed the maximum credit limit of ${MAX_CREDITS_PER_TERM} credits.`
        ));
      }
    }

    // Check seat availability
    const seatLedger = db.getCourseSeatLedger(courseId);
    let enrollmentState;
    
    if (seatLedger && seatLedger.seats_available > 0) {
      // Seat available - enroll directly
      enrollmentState = 'ENROLLED';
      
      // Debit seat ledger
      db.updateCourseSeatLedger(courseId, -1);
      
      // Credit tuition ledger
      const courseCost = course.credits * COST_PER_CREDIT;
      db.updateStudentTuitionLedger(termId, targetStudentId, courseCost);
    } else {
      // No seats available - add to waitlist
      enrollmentState = 'WAITLISTED';
    }

    // Create enrollment
    const enrollment = new Enrollment(termId, courseId, targetStudentId, enrollmentState);
    db.createEnrollment(enrollment);

    const filteredEnrollment = filterEnrollmentDataByRole(enrollment, req.user.role, req.user.id);
    res.status(201).json(createSuccessResponse(filteredEnrollment));
  }
);

// GET /terms/{termId}/courses/{courseId}/enrollments - List enrollments for a course
router.get('/',
  requireRole('PROFESSOR', 'REGISTRAR'),
  validatePagination,
  (req, res) => {
    const { termId, courseId } = req.params;
    const { limit, offset } = req.query;
    const { state } = req.query;

    // Validate term exists
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Validate course exists
    const course = db.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Check authorization for professors
    if (req.user.role === 'PROFESSOR' && course.professor_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        'ERR_NOT_INSTRUCTOR',
        'Only the course instructor can view enrollments for this course.'
      ));
    }

    // Get enrollments
    let enrollments = db.getEnrollmentsByCourse(courseId);

    // Apply state filter
    if (state) {
      if (!ENROLLMENT_STATES.includes(state)) {
        return res.status(400).json(createErrorResponse(
          'ERR_INVALID_ENUM_VALUE',
          `State must be one of: ${ENROLLMENT_STATES.join(', ')}.`
        ));
      }
      enrollments = enrollments.filter(enrollment => enrollment.state === state);
    }

    // Sort by creation time (waitlist order for WAITLISTED)
    enrollments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Apply pagination
    const total = enrollments.length;
    const paginatedEnrollments = enrollments.slice(offset, offset + limit);

    // Filter enrollment data based on role
    const filteredEnrollments = paginatedEnrollments.map(enrollment => 
      filterEnrollmentDataByRole(enrollment, req.user.role, req.user.id)
    );

    res.json(createPaginatedResponse(filteredEnrollments, total, limit, offset));
  }
);

// GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId} - Get specific enrollment
router.get('/:enrollmentId',
  validateUUIDParam('enrollmentId'),
  (req, res) => {
    const { termId, courseId, enrollmentId } = req.params;

    // Validate term exists
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Validate course exists
    const course = db.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Get enrollment
    const enrollment = db.getEnrollment(enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.status(404).json(createErrorResponse(
        'ERR_ENROLLMENT_NOT_FOUND',
        'Enrollment not found.'
      ));
    }

    // Check authorization
    let authorized = false;
    if (req.user.role === 'REGISTRAR') {
      authorized = true;
    } else if (req.user.role === 'STUDENT' && enrollment.student_id === req.user.id) {
      authorized = true;
    } else if (req.user.role === 'PROFESSOR' && course.professor_id === req.user.id) {
      authorized = true;
    }

    if (!authorized) {
      return res.status(403).json(createErrorResponse(
        'ERR_FORBIDDEN',
        'Access denied to this enrollment record.'
      ));
    }

    const filteredEnrollment = filterEnrollmentDataByRole(enrollment, req.user.role, req.user.id);
    res.json(createSuccessResponse(filteredEnrollment));
  }
);

// PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop - Drop enrollment
router.patch('/:enrollmentId:drop',
  validateUUIDParam('enrollmentId'),
  validateRequestBody(['revision']),
  (req, res) => {
    const { termId, courseId, enrollmentId } = req.params;
    const { revision } = req.body;

    // Validate term exists and allows drops
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    if (term.state === 'CONCLUDED') {
      return res.status(409).json(createErrorResponse(
        'ERR_TERM_NOT_ACTIVE',
        'Cannot drop enrollments in a concluded term.'
      ));
    }

    // Validate course exists
    const course = db.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Get enrollment
    const enrollment = db.getEnrollment(enrollmentId);
    if (!enrollment || enrollment.course_id !== courseId) {
      return res.status(404).json(createErrorResponse(
        'ERR_ENROLLMENT_NOT_FOUND',
        'Enrollment not found.'
      ));
    }

    // Check authorization
    let authorized = false;
    if (req.user.role === 'REGISTRAR') {
      authorized = true;
    } else if (req.user.role === 'STUDENT' && enrollment.student_id === req.user.id) {
      authorized = true;
    } else if (req.user.role === 'PROFESSOR' && course.professor_id === req.user.id) {
      authorized = true;
    }

    if (!authorized) {
      return res.status(403).json(createErrorResponse(
        'ERR_FORBIDDEN',
        'Access denied to drop this enrollment.'
      ));
    }

    // Check revision for optimistic locking
    if (revision !== undefined && enrollment.revision !== revision) {
      return res.status(409).json(createErrorResponse(
        'ERR_REV_CONFLICT',
        'Enrollment has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (!['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {
      return res.status(409).json(createErrorResponse(
        'ERR_ENROLLMENT_WRONG_STATE',
        'Enrollment cannot be dropped in its current state.'
      ));
    }

    // Check drop count limit for students
    if (req.user.role === 'STUDENT') {
      const currentDropCount = db.getDropCountForStudent(termId, enrollment.student_id);
      if (currentDropCount >= MAX_DROP_COUNT_PER_TERM) {
        return res.status(409).json(createErrorResponse(
          'ERR_TOO_MANY_DROPS',
          `Student has reached the maximum drop limit of ${MAX_DROP_COUNT_PER_TERM} courses per term.`
        ));
      }
    }

    // Process the drop
    let shouldPromoteWaitlist = false;
    
    if (enrollment.state === 'ENROLLED') {
      // Free up a seat
      db.updateCourseSeatLedger(courseId, 1);
      
      // Refund tuition
      const courseCost = course.credits * COST_PER_CREDIT;
      db.updateStudentTuitionLedger(termId, enrollment.student_id, -courseCost);
      
      shouldPromoteWaitlist = true;
    }

    // Update enrollment state
    const updatedEnrollment = db.updateEnrollment(enrollmentId, { state: 'DROPPED' });

    // Apply drop penalty if this is the student's third drop
    if (req.user.role === 'STUDENT') {
      const newDropCount = db.getDropCountForStudent(termId, enrollment.student_id);
      if (newDropCount === MAX_DROP_COUNT_PER_TERM) {
        db.updateStudentTuitionLedger(termId, enrollment.student_id, DROP_PENALTY_FEE);
      }
    }

    // Trigger waitlist promotion if a seat was freed
    if (shouldPromoteWaitlist) {
      // This will run asynchronously
      setImmediate(() => {
        promoteFromWaitlist(courseId);
      });
    }

    const filteredEnrollment = filterEnrollmentDataByRole(updatedEnrollment, req.user.role, req.user.id);
    res.json(createSuccessResponse(filteredEnrollment));
  }
);

module.exports = router;