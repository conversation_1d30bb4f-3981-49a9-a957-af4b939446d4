const express = require('express');
const router = express.Router({ mergeParams: true });
const { 
  db, 
  Course, 
  COURSE_STATES, 
  DELIVERY_MODES, 
  MAX_COURSES_PER_PROF 
} = require('../models');
const { 
  requireRole, 
  validateUUIDParam, 
  validateRequestBody, 
  validatePagination 
} = require('../middleware');
const { 
  createSuccessResponse, 
  createErrorResponse, 
  createPaginatedResponse,
  validateCourseCode,
  validateFieldLength,
  validateCredits,
  validateCapacity,
  validateURL,
  filterCourseDataByRole,
  shouldCourseBeVisibleToRole
} = require('../utils');

// POST /terms/{termId}/courses - Create a new course
router.post('/',
  requireRole('PROFESSOR', 'REGISTRAR'),
  validateRequestBody([
    'code', 'title', 'description', 'credits', 'capacity', 
    'professor_id', 'delivery_mode', 'location', 'online_link'
  ]),
  (req, res) => {
    const { termId } = req.params;
    const { 
      code, title, description, credits, capacity, 
      professor_id, delivery_mode, location, online_link 
    } = req.body;

    // Validate term exists and is accessible
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Validate required fields
    if (!code || !title || !credits || !capacity || !delivery_mode) {
      return res.status(400).json(createErrorResponse(
        'ERR_MISSING_REQUIRED_FIELD',
        'Required fields: code, title, credits, capacity, delivery_mode.'
      ));
    }

    // Validate course code format
    if (!validateCourseCode(code)) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_COURSE_CODE',
        'Course code must be 2-4 uppercase letters followed by 3 digits (e.g., CS101).'
      ));
    }

    // Check course code uniqueness within term
    const existingCourse = db.getCoursesByTerm(termId).find(course => course.code === code);
    if (existingCourse) {
      return res.status(409).json(createErrorResponse(
        'ERR_COURSE_CODE_NOT_UNIQUE',
        'A course with this code already exists in this term.'
      ));
    }

    // Validate title length
    const titleValidation = validateFieldLength(title, 100, 'title');
    if (!titleValidation.valid) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_FIELD_LENGTH',
        titleValidation.error
      ));
    }

    // Validate description length if provided
    if (description) {
      const descValidation = validateFieldLength(description, 1000, 'description');
      if (!descValidation.valid) {
        return res.status(400).json(createErrorResponse(
          'ERR_INVALID_FIELD_LENGTH',
          descValidation.error
        ));
      }
    }

    // Validate credits
    if (!validateCredits(credits)) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_CREDITS',
        'Credits must be an integer between 1 and 5.'
      ));
    }

    // Validate capacity
    if (!validateCapacity(capacity)) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_CAPACITY',
        'Capacity must be an integer between 1 and 500.'
      ));
    }

    // Validate delivery mode
    if (!DELIVERY_MODES.includes(delivery_mode)) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_ENUM_VALUE',
        `Delivery mode must be one of: ${DELIVERY_MODES.join(', ')}.`
      ));
    }

    // Validate conditional fields based on delivery mode
    if (delivery_mode === 'IN_PERSON') {
      if (!location) {
        return res.status(400).json(createErrorResponse(
          'ERR_CONDITIONAL_FIELD_REQUIRED',
          'Location is required for IN_PERSON delivery mode.'
        ));
      }
      if (online_link) {
        return res.status(400).json(createErrorResponse(
          'ERR_FIELD_CONFLICT',
          'Online link should not be provided for IN_PERSON delivery mode.'
        ));
      }
    } else if (delivery_mode === 'ONLINE') {
      if (!online_link) {
        return res.status(400).json(createErrorResponse(
          'ERR_CONDITIONAL_FIELD_REQUIRED',
          'Online link is required for ONLINE delivery mode.'
        ));
      }
      if (!validateURL(online_link)) {
        return res.status(400).json(createErrorResponse(
          'ERR_INVALID_FIELD_LENGTH',
          'Online link must be a valid URL.'
        ));
      }
      if (location) {
        return res.status(400).json(createErrorResponse(
          'ERR_FIELD_CONFLICT',
          'Location should not be provided for ONLINE delivery mode.'
        ));
      }
    } else if (delivery_mode === 'HYBRID') {
      if (!location && !online_link) {
        return res.status(400).json(createErrorResponse(
          'ERR_CONDITIONAL_FIELD_REQUIRED',
          'At least one of location or online_link is required for HYBRID delivery mode.'
        ));
      }
      if (online_link && !validateURL(online_link)) {
        return res.status(400).json(createErrorResponse(
          'ERR_INVALID_FIELD_LENGTH',
          'Online link must be a valid URL.'
        ));
      }
    }

    // Determine professor ID
    let assignedProfessorId;
    if (req.user.role === 'REGISTRAR') {
      if (!professor_id) {
        return res.status(400).json(createErrorResponse(
          'ERR_MISSING_REQUIRED_FIELD',
          'Professor ID is required when Registrar creates a course.'
        ));
      }
      // TODO: In a real system, we'd validate that professor_id belongs to a user with PROFESSOR role
      assignedProfessorId = professor_id;
    } else if (req.user.role === 'PROFESSOR') {
      if (professor_id && professor_id !== req.user.id) {
        return res.status(400).json(createErrorResponse(
          'ERR_FIELD_CONFLICT',
          'Professor cannot create a course for another professor.'
        ));
      }
      assignedProfessorId = req.user.id;
    }

    // Check professor course limit
    const professorCourses = db.getCoursesByProfessor(termId, assignedProfessorId);
    if (professorCourses.length >= MAX_COURSES_PER_PROF) {
      return res.status(409).json(createErrorResponse(
        'ERR_MAX_COURSES_REACHED',
        `Professor cannot teach more than ${MAX_COURSES_PER_PROF} courses per term.`
      ));
    }

    // Create the course
    const course = new Course(
      termId, code, title, description, credits, capacity, 
      assignedProfessorId, delivery_mode, location, online_link
    );
    db.createCourse(course);

    res.status(201).json(createSuccessResponse(course));
  }
);

// GET /terms/{termId}/courses - List courses in the term
router.get('/',
  validatePagination,
  (req, res) => {
    const { termId } = req.params;
    const { limit, offset } = req.query;
    const { state, professor_id } = req.query;

    // Validate term exists
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Get all courses for the term
    let courses = db.getCoursesByTerm(termId);

    // Apply filters
    if (state) {
      if (!COURSE_STATES.includes(state)) {
        return res.status(400).json(createErrorResponse(
          'ERR_INVALID_ENUM_VALUE',
          `State must be one of: ${COURSE_STATES.join(', ')}.`
        ));
      }
      courses = courses.filter(course => course.state === state);
    }

    if (professor_id) {
      courses = courses.filter(course => course.professor_id === professor_id);
    }

    // Filter by role visibility
    courses = courses.filter(course => 
      shouldCourseBeVisibleToRole(course, req.user.role, req.user.id)
    );

    // Sort by creation time
    courses.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Apply pagination
    const total = courses.length;
    const paginatedCourses = courses.slice(offset, offset + limit);

    // Filter course data based on role
    const filteredCourses = paginatedCourses.map(course => {
      const enrollments = db.getEnrollmentsByCourse(course.id);
      return filterCourseDataByRole(course, req.user.role, req.user.id, enrollments);
    });

    res.json(createPaginatedResponse(filteredCourses, total, limit, offset));
  }
);

// GET /terms/{termId}/courses/{courseId} - Get detailed info on a specific course
router.get('/:courseId',
  validateUUIDParam('courseId'),
  (req, res) => {
    const { termId, courseId } = req.params;

    // Validate term exists
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Get course
    const course = db.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Check if course should be visible to this role
    if (!shouldCourseBeVisibleToRole(course, req.user.role, req.user.id)) {
      return res.status(404).json(createErrorResponse(
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Get enrollments for detailed view
    const enrollments = db.getEnrollmentsByCourse(courseId);
    const filteredCourse = filterCourseDataByRole(course, req.user.role, req.user.id, enrollments);

    // For professors and registrars viewing their own courses, include enrollment details
    if ((req.user.role === 'PROFESSOR' && course.professor_id === req.user.id) || 
        req.user.role === 'REGISTRAR') {
      filteredCourse.enrollments = enrollments.map(enrollment => ({
        id: enrollment.id,
        student_id: enrollment.student_id,
        state: enrollment.state,
        created_at: enrollment.created_at,
        grade: enrollment.grade
      }));
    }

    res.json(createSuccessResponse(filteredCourse));
  }
);

// PATCH /terms/{termId}/courses/{courseId}:publish - Publish a draft course
router.patch('/:courseId:publish',
  validateUUIDParam('courseId'),
  requireRole('PROFESSOR', 'REGISTRAR'),
  validateRequestBody(['revision']),
  (req, res) => {
    const { termId, courseId } = req.params;
    const { revision } = req.body;

    // Validate term exists and is open for enrollment
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    if (term.state !== 'ENROLLMENT_OPEN') {
      return res.status(409).json(createErrorResponse(
        'ERR_TERM_NOT_ACTIVE',
        'Term must be open for enrollment to publish courses.'
      ));
    }

    // Get course
    const course = db.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Check authorization
    if (req.user.role === 'PROFESSOR' && course.professor_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        'ERR_NOT_INSTRUCTOR',
        'Only the course instructor can publish this course.'
      ));
    }

    // Check revision for optimistic locking
    if (revision !== undefined && course.revision !== revision) {
      return res.status(409).json(createErrorResponse(
        'ERR_REV_CONFLICT',
        'Course has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (course.state !== 'DRAFT') {
      return res.status(409).json(createErrorResponse(
        'ERR_COURSE_WRONG_STATE',
        'Only draft courses can be published.'
      ));
    }

    // Update course state and initialize seat ledger
    const updatedCourse = db.updateCourse(courseId, { 
      state: 'OPEN',
      published_at: new Date().toISOString()
    });
    
    // Initialize course seat ledger
    db.createCourseSeatLedger(courseId, course.capacity);

    res.json(createSuccessResponse(updatedCourse));
  }
);

// PATCH /terms/{termId}/courses/{courseId}:cancel - Cancel a course
router.patch('/:courseId:cancel',
  validateUUIDParam('courseId'),
  requireRole('PROFESSOR', 'REGISTRAR'),
  validateRequestBody(['revision']),
  (req, res) => {
    const { termId, courseId } = req.params;
    const { revision } = req.body;

    // Validate term exists
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Get course
    const course = db.getCourse(courseId);
    if (!course || course.term_id !== termId) {
      return res.status(404).json(createErrorResponse(
        'ERR_COURSE_NOT_FOUND',
        'Course not found.'
      ));
    }

    // Check authorization
    if (req.user.role === 'PROFESSOR' && course.professor_id !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        'ERR_NOT_INSTRUCTOR',
        'Only the course instructor can cancel this course.'
      ));
    }

    // Check revision for optimistic locking
    if (revision !== undefined && course.revision !== revision) {
      return res.status(409).json(createErrorResponse(
        'ERR_REV_CONFLICT',
        'Course has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (!['DRAFT', 'OPEN', 'IN_PROGRESS'].includes(course.state)) {
      return res.status(409).json(createErrorResponse(
        'ERR_COURSE_WRONG_STATE',
        'Course cannot be cancelled in its current state.'
      ));
    }

    // Update course state
    const updatedCourse = db.updateCourse(courseId, { state: 'CANCELLED' });

    // Drop all enrollments and handle refunds
    const enrollments = db.getEnrollmentsByCourse(courseId);
    enrollments.forEach(enrollment => {
      if (enrollment.state === 'ENROLLED') {
        // Refund tuition for enrolled students
        const courseCost = course.credits * require('../models').COST_PER_CREDIT;
        db.updateStudentTuitionLedger(termId, enrollment.student_id, -courseCost);
      }
      // Mark enrollment as dropped
      db.updateEnrollment(enrollment.id, { state: 'DROPPED' });
    });

    res.json(createSuccessResponse(updatedCourse));
  }
);

module.exports = router;