const express = require('express');
const router = express.Router();
const { db, AcademicTerm, TERM_STATES, COURSE_STATES, ENROLLMENT_STATES } = require('../models');
const { requireRole, validateUUIDParam, validateRequestBody } = require('../middleware');
const { createSuccessResponse, createErrorResponse, validateFieldLength } = require('../utils');

// POST /terms - Create a new academic term
router.post('/', 
  requireRole('REGISTRAR'),
  validateRequestBody(['name']),
  (req, res) => {
    const { name } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json(createErrorResponse(
        'ERR_MISSING_REQUIRED_FIELD',
        'Term name is required.'
      ));
    }

    // Validate field length
    const nameValidation = validateFieldLength(name, 100, 'name');
    if (!nameValidation.valid) {
      return res.status(400).json(createErrorResponse(
        'ERR_INVALID_FIELD_LENGTH',
        nameValidation.error
      ));
    }

    // Check for unique term name
    const existingTerm = Array.from(db.terms.values()).find(term => term.name === name);
    if (existingTerm) {
      return res.status(409).json(createErrorResponse(
        'ERR_TERM_NAME_NOT_UNIQUE',
        'A term with this name already exists.'
      ));
    }

    // Create new term
    const term = new AcademicTerm(name, req.user.id);
    db.createTerm(term);

    res.status(201).json(createSuccessResponse(term));
  }
);

// GET /terms/{termId} - Retrieve details of an academic term
router.get('/:termId',
  validateUUIDParam('termId'),
  (req, res) => {
    const { termId } = req.params;
    const term = db.getTerm(termId);

    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    res.json(createSuccessResponse(term));
  }
);

// PATCH /terms/{termId}:open-registration - Open student registration for the term
router.patch('/:termId:open-registration',
  validateUUIDParam('termId'),
  requireRole('REGISTRAR'),
  validateRequestBody(['revision']),
  (req, res) => {
    const { termId } = req.params;
    const { revision } = req.body;
    const term = db.getTerm(termId);

    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Check revision for optimistic locking
    if (revision !== undefined && term.revision !== revision) {
      return res.status(409).json(createErrorResponse(
        'ERR_REV_CONFLICT',
        'Term has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (term.state !== 'PLANNING') {
      return res.status(409).json(createErrorResponse(
        'ERR_TERM_NOT_ACTIVE',
        'Term must be in PLANNING state to open registration.'
      ));
    }

    // Update term state
    const updatedTerm = db.updateTerm(termId, { state: 'ENROLLMENT_OPEN' });

    res.json(createSuccessResponse(updatedTerm));
  }
);

// PATCH /terms/{termId}:close-registration - Close the enrollment period
router.patch('/:termId:close-registration',
  validateUUIDParam('termId'),
  requireRole('REGISTRAR'),
  validateRequestBody(['revision']),
  (req, res) => {
    const { termId } = req.params;
    const { revision } = req.body;
    const term = db.getTerm(termId);

    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Check revision for optimistic locking
    if (revision !== undefined && term.revision !== revision) {
      return res.status(409).json(createErrorResponse(
        'ERR_REV_CONFLICT',
        'Term has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (term.state !== 'ENROLLMENT_OPEN') {
      return res.status(409).json(createErrorResponse(
        'ERR_TERM_NOT_ACTIVE',
        'Term must be in ENROLLMENT_OPEN state to close registration.'
      ));
    }

    // Update term state
    const updatedTerm = db.updateTerm(termId, { state: 'ENROLLMENT_CLOSED' });

    // Transition all OPEN courses to IN_PROGRESS
    const courses = db.getCoursesByTerm(termId);
    courses.forEach(course => {
      if (course.state === 'OPEN') {
        db.updateCourse(course.id, { state: 'IN_PROGRESS' });
      }
    });

    res.json(createSuccessResponse(updatedTerm));
  }
);

// PATCH /terms/{termId}:conclude - Conclude the term
router.patch('/:termId:conclude',
  validateUUIDParam('termId'),
  requireRole('REGISTRAR'),
  validateRequestBody(['revision']),
  (req, res) => {
    const { termId } = req.params;
    const { revision } = req.body;
    const term = db.getTerm(termId);

    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Check revision for optimistic locking
    if (revision !== undefined && term.revision !== revision) {
      return res.status(409).json(createErrorResponse(
        'ERR_REV_CONFLICT',
        'Term has been modified by another user. Please refresh and try again.'
      ));
    }

    // Validate current state
    if (term.state !== 'ENROLLMENT_CLOSED') {
      return res.status(409).json(createErrorResponse(
        'ERR_TERM_NOT_ACTIVE',
        'Term must be in ENROLLMENT_CLOSED state to conclude. Please close registration first.'
      ));
    }

    // Update term state
    const updatedTerm = db.updateTerm(termId, { state: 'CONCLUDED' });

    // Transition all IN_PROGRESS courses to COMPLETED
    const courses = db.getCoursesByTerm(termId);
    courses.forEach(course => {
      if (course.state === 'IN_PROGRESS') {
        db.updateCourse(course.id, { state: 'COMPLETED' });
      }
    });

    // Finalize all enrollments
    const enrollments = Array.from(db.enrollments.values()).filter(e => e.term_id === termId);
    enrollments.forEach(enrollment => {
      if (enrollment.state === 'ENROLLED') {
        db.updateEnrollment(enrollment.id, { state: 'COMPLETED' });
      } else if (enrollment.state === 'WAITLISTED') {
        db.updateEnrollment(enrollment.id, { state: 'DROPPED' });
      }
    });

    res.json(createSuccessResponse(updatedTerm));
  }
);

module.exports = router;