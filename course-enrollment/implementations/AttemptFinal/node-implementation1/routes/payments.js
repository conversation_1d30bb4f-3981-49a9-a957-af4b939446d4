const express = require('express');
const router = express.Router({ mergeParams: true });
const { db } = require('../models');
const { requireRole, validateUUIDParam, validateRequestBody } = require('../middleware');
const { createSuccessResponse, createErrorResponse, validatePaymentAmount } = require('../utils');

// POST /terms/{termId}/students/{studentId}:pay - Record a tuition payment
router.post('/:studentId:pay',
  validateUUIDParam('studentId'),
  requireRole('STUDENT', 'REGISTRAR'),
  validateRequestBody(['amount']),
  (req, res) => {
    const { termId, studentId } = req.params;
    const { amount } = req.body;

    // Validate term exists
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Validate payment amount
    if (!amount || !validatePaymentAmount(amount)) {
      return res.status(422).json(createErrorResponse(
        'ERR_INVALID_PAYMENT_AMOUNT',
        'Payment amount must be a positive integer (in cents).'
      ));
    }

    // Check authorization
    if (req.user.role === 'STUDENT' && studentId !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        'ERR_FORBIDDEN',
        'Students can only make payments for their own account.'
      ));
    }

    // TODO: In a real system, validate that studentId exists and has STUDENT role
    // For now, we'll assume the student exists if we're trying to pay for them

    // Get or create student tuition ledger
    const ledger = db.getOrCreateStudentTuitionLedger(termId, studentId);

    // Check for overpayment
    if (amount > ledger.balance_cents) {
      return res.status(422).json(createErrorResponse(
        'ERR_OVERPAY_NOT_ALLOWED',
        `Payment amount (${amount} cents) exceeds outstanding balance (${ledger.balance_cents} cents).`
      ));
    }

    // Process payment (debit the ledger)
    const updatedLedger = db.updateStudentTuitionLedger(termId, studentId, -amount);

    // Prepare response
    const response = {
      term_id: termId,
      student_id: studentId,
      payment_amount: amount,
      new_balance: updatedLedger.balance_cents,
      payment_timestamp: new Date().toISOString()
    };

    res.json(createSuccessResponse(response));
  }
);

// GET /terms/{termId}/students/{studentId}/balance - Get student's current balance
router.get('/:studentId/balance',
  validateUUIDParam('studentId'),
  (req, res) => {
    const { termId, studentId } = req.params;

    // Validate term exists
    const term = db.getTerm(termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        'ERR_TERM_NOT_FOUND',
        'Term not found.'
      ));
    }

    // Check authorization
    if (req.user.role === 'STUDENT' && studentId !== req.user.id) {
      return res.status(403).json(createErrorResponse(
        'ERR_FORBIDDEN',
        'Students can only view their own balance.'
      ));
    }

    // Get student tuition ledger
    const ledger = db.getStudentTuitionLedger(termId, studentId);
    
    if (!ledger) {
      // No ledger exists, which means student has no enrollments and thus no balance
      const response = {
        term_id: termId,
        student_id: studentId,
        balance_cents: 0
      };
      return res.json(createSuccessResponse(response));
    }

    const response = {
      term_id: termId,
      student_id: studentId,
      balance_cents: ledger.balance_cents
    };

    res.json(createSuccessResponse(response));
  }
);

module.exports = router;