const { v4: uuidv4 } = require('uuid');

function generateRequestId() {
  return 'req_' + uuidv4().replace(/-/g, '').substring(0, 16).toUpperCase();
}

function createSuccessResponse(data, responseType = 'object') {
  return {
    meta: {
      api_request_id: generateRequestId(),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: responseType,
    data: data || {}
  };
}

function createErrorResponse(errorId, message) {
  return {
    meta: {
      api_request_id: generateRequestId(),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };
}

function createPaginatedResponse(items, total, limit, offset) {
  const response = createSuccessResponse(items, 'array');
  response.meta.total = total;
  response.meta.limit = limit;
  response.meta.offset = offset;
  response.meta.has_more = offset + items.length < total;
  return response;
}

// Validation helper functions
function validateCourseCode(code) {
  // 2-4 uppercase letters followed by 3 digits (e.g., "CS101", "MATH101", "PHYS201")
  const courseCodePattern = /^[A-Z]{2,4}\d{3}$/;
  return courseCodePattern.test(code);
}

function validateURL(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function validateFieldLength(value, maxLength, fieldName) {
  if (typeof value !== 'string') {
    return { valid: false, error: `${fieldName} must be a string` };
  }
  if (value.length === 0) {
    return { valid: false, error: `${fieldName} cannot be empty` };
  }
  if (value.length > maxLength) {
    return { valid: false, error: `${fieldName} cannot exceed ${maxLength} characters` };
  }
  return { valid: true };
}

function validateCredits(credits) {
  return Number.isInteger(credits) && credits >= 1 && credits <= 5;
}

function validateCapacity(capacity) {
  return Number.isInteger(capacity) && capacity >= 1 && capacity <= 500;
}

function validatePaymentAmount(amount) {
  return Number.isInteger(amount) && amount > 0;
}

// Data filtering functions for role-based visibility
function filterCourseDataByRole(course, userRole, userId, enrollments = []) {
  const baseCourse = {
    id: course.id,
    code: course.code,
    title: course.title,
    description: course.description,
    credits: course.credits,
    capacity: course.capacity,
    professor_id: course.professor_id,
    delivery_mode: course.delivery_mode,
    location: course.location,
    online_link: course.online_link,
    state: course.state,
    created_at: course.created_at
  };

  if (userRole === 'REGISTRAR') {
    // Registrar sees everything
    return {
      ...baseCourse,
      published_at: course.published_at,
      revision: course.revision,
      enrolled_count: enrollments.filter(e => e.state === 'ENROLLED').length,
      waitlist_count: enrollments.filter(e => e.state === 'WAITLISTED').length,
      available_seats: course.capacity - enrollments.filter(e => e.state === 'ENROLLED').length
    };
  }

  if (userRole === 'PROFESSOR' && course.professor_id === userId) {
    // Professor sees their own course details
    return {
      ...baseCourse,
      published_at: course.published_at,
      revision: course.revision,
      enrolled_count: enrollments.filter(e => e.state === 'ENROLLED').length,
      waitlist_count: enrollments.filter(e => e.state === 'WAITLISTED').length,
      available_seats: course.capacity - enrollments.filter(e => e.state === 'ENROLLED').length
    };
  }

  // Student or Professor viewing other's course
  const studentEnrollment = enrollments.find(e => e.student_id === userId);
  const result = {
    ...baseCourse,
    available_seats: course.capacity - enrollments.filter(e => e.state === 'ENROLLED').length
  };

  if (studentEnrollment) {
    result.is_enrolled = studentEnrollment.state === 'ENROLLED';
    result.is_waitlisted = studentEnrollment.state === 'WAITLISTED';
    if (studentEnrollment.grade !== null) {
      result.grade = studentEnrollment.grade;
    }
  }

  return result;
}

function filterEnrollmentDataByRole(enrollment, userRole, userId) {
  const baseEnrollment = {
    id: enrollment.id,
    course_id: enrollment.course_id,
    student_id: enrollment.student_id,
    state: enrollment.state,
    created_at: enrollment.created_at
  };

  if (userRole === 'REGISTRAR') {
    // Registrar sees everything
    return {
      ...baseEnrollment,
      revision: enrollment.revision,
      grade: enrollment.grade
    };
  }

  if (userRole === 'STUDENT' && enrollment.student_id === userId) {
    // Student sees their own enrollment
    return {
      ...baseEnrollment,
      grade: enrollment.grade
    };
  }

  if (userRole === 'PROFESSOR') {
    // Professor sees enrollments in their courses (course ownership checked elsewhere)
    return {
      ...baseEnrollment,
      grade: enrollment.grade
    };
  }

  // Shouldn't reach here if authorization is properly implemented
  return baseEnrollment;
}

function shouldCourseBeVisibleToRole(course, userRole, userId) {
  if (userRole === 'REGISTRAR') {
    return true; // Registrar sees all courses
  }

  if (userRole === 'PROFESSOR') {
    // Professor sees their own courses (any state) and published courses from others
    return course.professor_id === userId || ['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state);
  }

  if (userRole === 'STUDENT') {
    // Student only sees published/active courses
    return ['OPEN', 'IN_PROGRESS', 'COMPLETED'].includes(course.state);
  }

  return false;
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  createPaginatedResponse,
  validateCourseCode,
  validateURL,
  validateFieldLength,
  validateCredits,
  validateCapacity,
  validatePaymentAmount,
  filterCourseDataByRole,
  filterEnrollmentDataByRole,
  shouldCourseBeVisibleToRole
};