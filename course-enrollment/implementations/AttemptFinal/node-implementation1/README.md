# University Course Registration & Enrollment API

A Node.js API implementation that manages academic course offerings and student enrollments within academic terms.

## Quick Start

1. Install dependencies:
```bash
npm install
```

2. Start the server:
```bash
npm start
# or for development with auto-restart:
npm run dev
```

The API will be available at `http://localhost:3000`

## Authentication

All API requests require two headers:
- `X-User-ID`: User's UUID (e.g., `550e8400-e29b-41d4-a716-************`)
- `X-User-Role`: User's role (`STUDENT`, `PROFESSOR`, or `REGISTRAR`)

## API Endpoints

### Term Management
- `POST /terms` - Create a new academic term (REGISTRAR only)
- `GET /terms/{termId}` - Get term details
- `PATCH /terms/{termId}:open-registration` - Open enrollment (REGISTRAR only)
- `PATCH /terms/{termId}:close-registration` - Close enrollment (REGISTRAR only)  
- `PATCH /terms/{termId}:conclude` - Conclude term (REGISTRAR only)

### Course Management
- `POST /terms/{termId}/courses` - Create a course (PROFESSOR, REGISTRAR)
- `GET /terms/{termId}/courses` - List courses (filtered by role)
- `GET /terms/{termId}/courses/{courseId}` - Get course details
- `PATCH /terms/{termId}/courses/{courseId}:publish` - Publish course (PROFESSOR, REGISTRAR)
- `PATCH /terms/{termId}/courses/{courseId}:cancel` - Cancel course (PROFESSOR, REGISTRAR)

### Enrollment & Waitlist
- `POST /terms/{termId}/courses/{courseId}/enrollments` - Enroll in course (STUDENT, REGISTRAR)
- `GET /terms/{termId}/courses/{courseId}/enrollments` - List enrollments (PROFESSOR, REGISTRAR)
- `GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}` - Get enrollment details
- `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop` - Drop enrollment

### Financial Operations
- `POST /terms/{termId}/students/{studentId}:pay` - Record payment (STUDENT, REGISTRAR)
- `GET /terms/{termId}/students/{studentId}/balance` - Get student balance

## Example Usage

### 1. Create a Term (as REGISTRAR)
```bash
curl -X POST http://localhost:3000/terms \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-************" \
  -H "X-User-Role: REGISTRAR" \
  -H "Content-Type: application/json" \
  -d '{"name": "Fall 2025"}'
```

### 2. Create a Course (as PROFESSOR)
```bash
curl -X POST http://localhost:3000/terms/{termId}/courses \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440001" \
  -H "X-User-Role: PROFESSOR" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "CS101",
    "title": "Introduction to Computer Science",
    "description": "Basic programming concepts",
    "credits": 3,
    "capacity": 30,
    "delivery_mode": "IN_PERSON",
    "location": "Room 101"
  }'
```

### 3. Enroll in a Course (as STUDENT)
```bash
curl -X POST http://localhost:3000/terms/{termId}/courses/{courseId}/enrollments \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440002" \
  -H "X-User-Role: STUDENT" \
  -H "Content-Type: application/json"
```

## Business Rules

### System Constants
- Maximum credits per term: 18
- Maximum courses per professor: 5  
- Maximum drops per student per term: 3
- Cost per credit: $100.00 (10000 cents)
- Drop penalty fee: $50.00 (5000 cents) on 3rd drop

### Term States
- `PLANNING` → `ENROLLMENT_OPEN` → `ENROLLMENT_CLOSED` → `CONCLUDED`

### Course States  
- `DRAFT` → `OPEN` → `IN_PROGRESS` → `COMPLETED` or `CANCELLED`

### Enrollment States
- `ENROLLED`, `WAITLISTED`, `DROPPED`, `COMPLETED`

### Key Features
- **Waitlist Management**: Automatic promotion when seats become available
- **Credit Limits**: Students limited to 18 credits (Registrar can override)
- **Drop Penalties**: $50 fee on 3rd drop per term
- **Seat Ledgers**: Real-time tracking of available seats
- **Tuition Ledgers**: Track student balances and payments
- **Role-based Access**: Different data visibility by user role

## Error Handling

All errors follow a standard format:
```json
{
  "meta": {
    "api_request_id": "req_ABC123",
    "api_request_timestamp": "2025-05-24T10:30:00.123Z"
  },
  "response_type": "error",
  "data": {
    "error_id": "ERR_COURSE_NOT_FOUND",
    "message": "Course not found."
  }
}
```

## Architecture

- **In-Memory Storage**: All data stored in memory (resets on restart)
- **Event-Driven**: Waitlist promotions triggered by seat availability
- **Optimistic Locking**: Revision numbers prevent concurrent update conflicts
- **Role-Based Security**: Field-level filtering based on user role

## Development

The implementation strictly follows the Product Requirements Document (PRD.md) and includes:
- Complete business logic validation
- Comprehensive error handling  
- Role-based authorization
- Pagination support
- Optimistic concurrency control
- Event-driven waitlist management