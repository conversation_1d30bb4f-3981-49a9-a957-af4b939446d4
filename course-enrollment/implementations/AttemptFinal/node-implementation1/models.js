const { v4: uuidv4 } = require('uuid');

const TERM_STATES = ['PLANNING', 'ENROLLMENT_OPEN', 'ENROLLMENT_CLOSED', 'CONCLUDED'];
const COURSE_STATES = ['DRAFT', 'OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'];
const ENROLLMENT_STATES = ['ENROLLED', 'WAITLISTED', 'DROPPED', 'COMPLETED'];
const USER_ROLES = ['STUDENT', 'PROFESSOR', 'REGISTRAR'];
const DELIVERY_MODES = ['IN_PERSON', 'ONLINE', 'HYBRID'];

const MAX_CREDITS_PER_TERM = 18;
const MAX_COURSES_PER_PROF = 5;
const MAX_DROP_COUNT_PER_TERM = 3;
const COST_PER_CREDIT = 10000; // in cents ($100 per credit)
const DROP_PENALTY_FEE = 5000; // in cents ($50)

class AcademicTerm {
  constructor(name, createdBy) {
    this.id = uuidv4();
    this.name = name;
    this.state = 'PLANNING';
    this.created_by = createdBy;
    this.created_at = new Date().toISOString();
    this.revision = 0;
  }
}

class Course {
  constructor(termId, code, title, description, credits, capacity, professorId, deliveryMode, location, onlineLink) {
    this.id = uuidv4();
    this.term_id = termId;
    this.code = code;
    this.title = title;
    this.description = description || '';
    this.credits = credits;
    this.capacity = capacity;
    this.professor_id = professorId;
    this.delivery_mode = deliveryMode;
    this.location = location || null;
    this.online_link = onlineLink || null;
    this.state = 'DRAFT';
    this.created_at = new Date().toISOString();
    this.published_at = null;
    this.revision = 0;
  }
}

class Enrollment {
  constructor(termId, courseId, studentId, state = 'WAITLISTED') {
    this.id = uuidv4();
    this.term_id = termId;
    this.course_id = courseId;
    this.student_id = studentId;
    this.state = state;
    this.created_at = new Date().toISOString();
    this.revision = 0;
    this.grade = null;
  }
}

class CourseSeatLedger {
  constructor(courseId, capacity) {
    this.course_id = courseId;
    this.seats_available = capacity;
    this.capacity = capacity;
  }
}

class StudentTuitionLedger {
  constructor(termId, studentId) {
    this.term_id = termId;
    this.student_id = studentId;
    this.balance_cents = 0;
  }
}

class InMemoryDatabase {
  constructor() {
    this.terms = new Map();
    this.courses = new Map();
    this.enrollments = new Map();
    this.courseSeatLedgers = new Map();
    this.studentTuitionLedgers = new Map();
  }

  // Term operations
  createTerm(term) {
    this.terms.set(term.id, term);
    return term;
  }

  getTerm(termId) {
    return this.terms.get(termId);
  }

  updateTerm(termId, updates) {
    const term = this.terms.get(termId);
    if (term) {
      Object.assign(term, updates);
      term.revision++;
    }
    return term;
  }

  // Course operations
  createCourse(course) {
    this.courses.set(course.id, course);
    return course;
  }

  getCourse(courseId) {
    return this.courses.get(courseId);
  }

  getCoursesByTerm(termId) {
    return Array.from(this.courses.values()).filter(course => course.term_id === termId);
  }

  getCoursesByProfessor(termId, professorId) {
    return Array.from(this.courses.values()).filter(course => 
      course.term_id === termId && course.professor_id === professorId
    );
  }

  updateCourse(courseId, updates) {
    const course = this.courses.get(courseId);
    if (course) {
      Object.assign(course, updates);
      course.revision++;
    }
    return course;
  }

  // Enrollment operations
  createEnrollment(enrollment) {
    this.enrollments.set(enrollment.id, enrollment);
    return enrollment;
  }

  getEnrollment(enrollmentId) {
    return this.enrollments.get(enrollmentId);
  }

  getEnrollmentsByCourse(courseId) {
    return Array.from(this.enrollments.values()).filter(enrollment => 
      enrollment.course_id === courseId
    );
  }

  getEnrollmentsByStudent(termId, studentId) {
    return Array.from(this.enrollments.values()).filter(enrollment => 
      enrollment.term_id === termId && enrollment.student_id === studentId
    );
  }

  getEnrollmentByStudentAndCourse(studentId, courseId) {
    return Array.from(this.enrollments.values()).find(enrollment => 
      enrollment.student_id === studentId && enrollment.course_id === courseId
    );
  }

  updateEnrollment(enrollmentId, updates) {
    const enrollment = this.enrollments.get(enrollmentId);
    if (enrollment) {
      Object.assign(enrollment, updates);
      enrollment.revision++;
    }
    return enrollment;
  }

  // Seat ledger operations
  createCourseSeatLedger(courseId, capacity) {
    const ledger = new CourseSeatLedger(courseId, capacity);
    this.courseSeatLedgers.set(courseId, ledger);
    return ledger;
  }

  getCourseSeatLedger(courseId) {
    return this.courseSeatLedgers.get(courseId);
  }

  updateCourseSeatLedger(courseId, seatsChange) {
    const ledger = this.courseSeatLedgers.get(courseId);
    if (ledger) {
      ledger.seats_available += seatsChange;
      // Ensure seats_available stays within bounds
      ledger.seats_available = Math.max(0, Math.min(ledger.capacity, ledger.seats_available));
    }
    return ledger;
  }

  // Tuition ledger operations
  createStudentTuitionLedger(termId, studentId) {
    const key = `${termId}-${studentId}`;
    const ledger = new StudentTuitionLedger(termId, studentId);
    this.studentTuitionLedgers.set(key, ledger);
    return ledger;
  }

  getStudentTuitionLedger(termId, studentId) {
    const key = `${termId}-${studentId}`;
    return this.studentTuitionLedgers.get(key);
  }

  getOrCreateStudentTuitionLedger(termId, studentId) {
    let ledger = this.getStudentTuitionLedger(termId, studentId);
    if (!ledger) {
      ledger = this.createStudentTuitionLedger(termId, studentId);
    }
    return ledger;
  }

  updateStudentTuitionLedger(termId, studentId, balanceChange) {
    const ledger = this.getOrCreateStudentTuitionLedger(termId, studentId);
    ledger.balance_cents += balanceChange;
    // Ensure balance doesn't go negative
    ledger.balance_cents = Math.max(0, ledger.balance_cents);
    return ledger;
  }

  // Helper methods for business logic
  getDropCountForStudent(termId, studentId) {
    return Array.from(this.enrollments.values()).filter(enrollment => 
      enrollment.term_id === termId && 
      enrollment.student_id === studentId && 
      enrollment.state === 'DROPPED'
    ).length;
  }

  getActiveEnrollmentsForStudent(termId, studentId) {
    return Array.from(this.enrollments.values()).filter(enrollment => 
      enrollment.term_id === termId && 
      enrollment.student_id === studentId && 
      enrollment.state === 'ENROLLED'
    );
  }

  getWaitlistedEnrollmentsForCourse(courseId) {
    return Array.from(this.enrollments.values())
      .filter(enrollment => 
        enrollment.course_id === courseId && 
        enrollment.state === 'WAITLISTED'
      )
      .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  }
}

// Create a singleton database instance
const db = new InMemoryDatabase();

module.exports = {
  db,
  AcademicTerm,
  Course,
  Enrollment,
  CourseSeatLedger,
  StudentTuitionLedger,
  TERM_STATES,
  COURSE_STATES,
  ENROLLMENT_STATES,
  USER_ROLES,
  DELIVERY_MODES,
  MAX_CREDITS_PER_TERM,
  MAX_COURSES_PER_PROF,
  MAX_DROP_COUNT_PER_TERM,
  COST_PER_CREDIT,
  DROP_PENALTY_FEE
};